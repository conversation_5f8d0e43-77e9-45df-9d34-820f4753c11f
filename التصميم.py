from ستايل import *
from for_all import *
from db import *


# دالة جلب بيانات جدول التصميم
def load_design_data(self, table_phases, client_id, selected_year):
    try:
        db_name = f"project_manager_V2"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        query = "SELECT id, المرحلة, الطابق, الوحدة, الكمية, السعر, الإجمالي, حالة_المبلغ, المهندس, ملاحظات FROM المشاريع_المراحل WHERE معرف_العميل = %s"
        cursor.execute(query, (client_id,))
        rows = cursor.fetchall()

        table_phases.setRowCount(0)

        for row_index, row_data in enumerate(rows):
            table_phases.insertRow(row_index)
            # تعيين id في العمود 0
            table_phases.setItem(row_index, 0, QTableWidgetItem(str(row_data[0])))  # id
            #table_phases.setItem(row_index, 10, QTableWidgetItem(str(row_data[0])))  # id
            for col_index, data in enumerate(row_data[0:], start=1):
                item = QTableWidgetItem(str(data))
                item.setTextAlignment(Qt.AlignCenter)    

                if data == "غير مدرج":
                    item.setBackground(QColor("#e9ad6e"))  # اللون البرتقالي
                elif data == "تم الإدراج":
                    item.setBackground(QColor("#cdd7b9"))  # اللون البرتقالي

                table_phases.setItem(row_index, col_index, item)
            #table_phases.setItem(row_index, 0, QTableWidgetItem(str(row_index + 1)))  # الرقم التسلسلي

            number_item = QTableWidgetItem(str(row_index + 1))
            number_item.setTextAlignment(Qt.AlignCenter)
            table_phases.setItem(row_index, 1, number_item)
            
        #اخفاء العامود الاخير
        table_phases.setColumnHidden(0, True)
       # table_phases.setColumnHidden(table_phases.columnCount() - 1, True)

    except mysql.connector.Error as err:
        QMessageBox.warning(self, "خطأ", f"فشل الاتصال بقاعدة البيانات: {err}")
    finally:
        cursor.close()
        conn.close()
    table_setting(table_phases)

# دالة جلب بيانات الجدول الزمني
def load_timeline_data(self, window, table_timeline, client_id, selected_year, status_filter=None):
    try:
        db_name = f"project_manager_V2"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        # إضافة id إلى الاستعلام
        query = """
            SELECT id, المرحلة, الطابق, المهندس, تاريخ_البدء, تاريخ_الإنتهاء, الوقت_المتبقي, حالة_المرحلة
            FROM التصميم
            WHERE معرف_العميل = %s
        """
        cursor.execute(query, (client_id,))
        rows = cursor.fetchall()

        table_timeline.setRowCount(0)

        for row_index, row_data in enumerate(rows):
            status = row_data[7] if row_data[7] else "لم يتم البدء"  # الحالة في العمود 7 بعد إضافة id
            if status_filter and status not in status_filter:
                continue

            table_timeline.insertRow(row_index)
            row_is_incomplete = False

            # وضع id في العمود 0
            معرف_item = QTableWidgetItem(str(row_data[0]))  # id
            معرف_item.setTextAlignment(Qt.AlignCenter)
            table_timeline.setItem(row_index, 0, معرف_item)

            # وضع الرقم التسلسلي في العمود 1
            number_item = QTableWidgetItem(str(row_index + 1))
            number_item.setTextAlignment(Qt.AlignCenter)
            table_timeline.setItem(row_index, 1, number_item)

            # باقي الأعمدة (من المرحلة إلى الحالة)
            for col_index, data in enumerate(row_data[1:], start=2):  # البدء من العمود 2
                display_value = str(data) if data else ""

                if col_index == 3 or col_index == 4:  # تاريخ_البدء أو تاريخ_الإنتهاء
                    if not data:
                        display_value = "غير محدد"
                        row_is_incomplete = True

                if col_index == 7:  # الحالة
                    if not data:
                        display_value = "لم يتم البدء"
                        row_is_incomplete = True

                item = QTableWidgetItem(display_value)
                item.setTextAlignment(Qt.AlignCenter)
                table_timeline.setItem(row_index, col_index, item)

            update_row_color(table_timeline, row_index, status)

        # إخفاء العمود 0 (id)
        table_timeline.setColumnHidden(0, True)

    except mysql.connector.Error as err:
        QMessageBox.warning(self, "خطأ", f"فشل الاتصال بقاعدة البيانات: {err}")
    finally:
        if cursor is not None:
            cursor.close()
        if conn is not None:
            conn.close()
    table_setting(table_timeline)

# دالة تحديث لون الصف بناءً على حالة الجدول الزالزمنيني 
def update_row_color(table, row, status):
    color = {
        "منتهي": QColor("#cdd7b9"),  # أخضر فاتح
        "متوقف": QColor("#dc8484"),  # أحمر
        "متأخر": QColor("#e9ad6e"),  # برتقالي
        "قيد الإنجاز": QColor("#eee0bd"),  # أزرق فاتح
       # "لم يتم البدء": QColor("#D3D3D3"),  # رمادي
      
    }
    for col in range(table.columnCount()):
        item = table.item(row, col)
        if item:
            item.setBackground(color.get(status, QColor("#FFFFFF")))

# دالة جلب بيانات مهام الموظفين
def load_employees_data(self, table_employees, client_id, selected_year):
    try:
        db_name = f"project_manager_V2"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        query = """
            SELECT المهندس, المرحلة, الطابق, نسبة_المهندس, مبلغ_المهندس, حالة_مبلغ_المهندس
            FROM التصميم
            WHERE معرف_العميل = %s
        """
        cursor.execute(query, (client_id,))
        rows = cursor.fetchall()

        table_employees.setRowCount(0)

        row_index = 0
        for row_data in rows:
            engineer_name = row_data[0]
            if engineer_name and "المدير" in engineer_name:
                continue

            table_employees.insertRow(row_index)
            for col_index, data in enumerate(row_data):
                item = QTableWidgetItem(str(data) if data else "")
                item.setTextAlignment(Qt.AlignCenter)
                if data == "غير مدرج":
                    item.setBackground(QColor("#e9ad6e"))  # اللون البرتقالي
                elif data == "تم الإدراج":
                    item.setBackground(QColor("#cdd7b9"))  # اللون البرتقالي

                table_employees.setItem(row_index, col_index + 1, item)

            # table_employees.setItem(row_index, 0, QTableWidgetItem(str(row_index + 1)))
            # row_index += 1

            number_item = QTableWidgetItem(str(row_index + 1))
            number_item.setTextAlignment(Qt.AlignCenter)
            table_employees.setItem(row_index, 0, number_item)
            row_index += 1


    except mysql.connector.Error as err:
        QMessageBox.warning(self, "خطأ", "يجب تحديد صف أولا")
        # create_database(self)
        # load_employees_data(self, table_employees, client_id, selected_year)
    finally:
        cursor.close()
        conn.close()
    table_setting(table_employees)


def load_employees_data(self, table_employees, client_id, selected_year):
    try:
        db_name = f"project_manager_V2"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        # إضافة id إلى الاستعلام
        query = """
            SELECT id, المهندس, المرحلة, الطابق, نسبة_المهندس, مبلغ_المهندس, حالة_مبلغ_المهندس
            FROM التصميم
            WHERE معرف_العميل = %s
        """
        cursor.execute(query, (client_id,))
        rows = cursor.fetchall()

        table_employees.setRowCount(0)

        row_index = 0
        for row_data in rows:
            engineer_name = row_data[1]  # المهندس في العمود 1 بعد id
            if engineer_name and "المدير" in engineer_name:
                continue

            table_employees.insertRow(row_index)

            # وضع id في العمود 0
            معرف_item = QTableWidgetItem(str(row_data[0]))  # id
            معرف_item.setTextAlignment(Qt.AlignCenter)
            table_employees.setItem(row_index, 0, معرف_item)

            # وضع الرقم التسلسلي في العمود 1
            number_item = QTableWidgetItem(str(row_index + 1))
            number_item.setTextAlignment(Qt.AlignCenter)
            table_employees.setItem(row_index, 1, number_item)

            # باقي الأعمدة (من المهندس إلى حالة_مبلغ_المهندس)
            for col_index, data in enumerate(row_data[1:], start=2):  # البدء من العمود 2
                item = QTableWidgetItem(str(data) if data else "")
                item.setTextAlignment(Qt.AlignCenter)
                if data == "غير مدرج":
                    item.setBackground(QColor("#e9ad6e"))  # اللون البرتقالي
                elif data == "تم الإدراج":
                    item.setBackground(QColor("#cdd7b9"))  # اللون الأخضر
                table_employees.setItem(row_index, col_index, item)

            row_index += 1
        # إخفاء العمود 0 (id)
        table_employees.setColumnHidden(0, True)
    except mysql.connector.Error as err:
        QMessageBox.warning(self, "خطأ", "يجب تحديد صف أولا")
        # create_database(self)
        # load_employees_data(self, table_employees, client_id, selected_year)
    finally:
        if cursor is not None:
            cursor.close()
        if conn is not None:
            conn.close()
    table_setting(table_employees)

# --------------------------دالة فتح نافذة التصميم--------------------------------------------------
def open_design_window(self, main_table, selected_year):
    selected_items = main_table.selectedItems()
    if not selected_items:
        QMessageBox.warning(self, "خطأ", "يجب تحديد صف أولا")
        return

    selected_row = main_table.currentRow()
    global client_code, client_name, project_name, project_total,client_id
    client_id = main_table.item(selected_row, 0).text()
    client_code = main_table.item(selected_row, 1).text()
    client_name = main_table.item(selected_row, 2).text()
    project_name = main_table.item(selected_row, 3).text()
    project_total = main_table.item(selected_row, 4).text()

    window = QDialog(self)
    window.setWindowTitle("نافذة التصميم")
    window.resize(1200, 650)
    window.setLayoutDirection(Qt.RightToLeft)
    window.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)

    tab_widget = QTabWidget(window)
    main_layout = QVBoxLayout(window)
    main_layout.addWidget(tab_widget)

    # تبويب "مراحل التصميم"
    tab_design = QDialog(self)
    tab_design_layout = QVBoxLayout(tab_design)

    clientface = QGroupBox("بيانات العميل")
    clientface.setStyleSheet("font-size: 14px; font-weight: bold;")
    clientface.setAlignment(Qt.AlignCenter)
    clientface.setLayoutDirection(Qt.RightToLeft)
    client_layout = QGridLayout()
    clientface.setLayout(client_layout)
    tab_design_layout.addWidget(clientface)

    client_name_label = QLabel("اسم العميل:", window)
    client_name_edit = QLineEdit(client_name, window)
    client_name_edit.setReadOnly(True)
    client_name_edit.setAlignment(Qt.AlignCenter)
    client_layout.addWidget(client_name_label, 0, 0)
    client_layout.addWidget(client_name_edit, 0, 1)

    project_name_label = QLabel("اسم المشروع:", window)
    project_name_edit = QLineEdit(project_name, window)
    project_name_edit.setReadOnly(True)
    project_name_edit.setAlignment(Qt.AlignCenter)
    client_layout.addWidget(project_name_label, 0, 2)
    client_layout.addWidget(project_name_edit, 0, 3)

    project_total_label = QLabel("إجمالي المشروع:", window)
    project_total_edit = QLineEdit(project_total, window)
    project_total_edit.setReadOnly(True)
    project_total_edit.setAlignment(Qt.AlignCenter)
    client_layout.addWidget(project_total_label, 0, 4)
    client_layout.addWidget(project_total_edit, 0, 5)

    total_layout = QHBoxLayout()
    #global total_cost_lineedit
    total_cost_label = QLabel("إجمالي المراحل:", window)
    total_employees = QLabel("إجمالي المهندسين:", window)
    total_compnue = QLabel("صافي الشركة:", window)
    #total_cost_lineedit.setAlignment(Qt.AlignCenter)
    total_layout.addWidget(total_cost_label)
    total_layout.addWidget(total_employees)
    total_layout.addWidget(total_compnue)
    tab_design_layout.addLayout(total_layout)

    global table_phases
    table_phases = QTableWidget(window)
    headers = ["id","الرقم", "                وصف المرحلة                ", "   الطابق   ", "  الوحدة  ", " المساحة/الكمية ", "  السعر  ", "  الإجمالي  ","  حالة الإجمالي  ", "  المهندس  ", "             ملاحظات            "]
    add_table_column(table_phases,headers)
    
    tab_design_layout.addWidget(table_phases)

    load_design_data(self, table_phases, client_id, selected_year)
    

    bottom_layout = QHBoxLayout()
    add_phase_button = QPushButton(qta.icon('fa5s.plus', color='darkgreen'), " إضافة مرحلة", window)
    add_phase_button.setMinimumSize(QSize(0, 40))
    add_phase_button.clicked.connect(lambda: open_phase_window(window, selected_year))
    bottom_layout.addWidget(add_phase_button)

    edit_button = QPushButton(qta.icon('fa5s.edit', color='gray'), "تعديل ", window)
    edit_button.setMinimumSize(QSize(0, 40))
    edit_button.setLayoutDirection(Qt.RightToLeft)
    edit_button.clicked.connect(lambda:edit_phase(window, table_phases, selected_year))
    bottom_layout.addWidget(edit_button)

    delete_button = QPushButton(qta.icon('fa5s.trash', color='crimson'), "حذف ", window)
    delete_button.setMinimumSize(QSize(0, 40))
    delete_button.setLayoutDirection(Qt.RightToLeft)
    delete_button.clicked.connect(lambda: delete_phase(table_phases, client_id, selected_year))
    bottom_layout.addWidget(delete_button)

    print_button = QPushButton(qta.icon('fa5s.print', color='navy'), "طباعة ", window)
    print_button.setMinimumSize(QSize(0, 40))
    print_button.setLayoutDirection(Qt.RightToLeft)
    bottom_layout.addWidget(print_button)

    update_project_total_button = QPushButton("إدراج الإجمالي", window)
    update_project_total_button.setMinimumSize(QSize(0, 40))
    update_project_total_button.clicked.connect(lambda: update_project_total(self, table_phases, selected_year))
    bottom_layout.addWidget(update_project_total_button)

    tab_design_layout.addLayout(bottom_layout)
    tab_widget.addTab(tab_design, "مراحل التصميم")

    # تبويب "الجدول الزمني"
    tab_timeline = QDialog(self)
    timeline_layout = QVBoxLayout(tab_timeline)

    global table_timeline
    table_timeline = QTableWidget(window)
    headers = ["id","الرقم", "                       وصف المرحلة                        ", "       الطابق       ", "             المهندس المسؤول             ", "  تاريخ البدء  ", "  تاريخ الانتهاء  ", "الوقت المتبقي", "   الحالة   "]
    add_table_column(table_timeline,headers)
    
    timeline_layout.addWidget(table_timeline)

    # إضافة أزرار الفلترة
    filter_layout = QHBoxLayout()
    status_filters = {
        "منتهي": QCheckBox("منتهي"),
        "متأخر": QCheckBox("متأخر"),
        "متوقف": QCheckBox("متوقف"),
        "قيد الإنجاز": QCheckBox("قيد الإنجاز"),
        "لم يتم البدء": QCheckBox("لم يتم البدء")
    }

    for status, checkbox in status_filters.items():
        checkbox.setChecked(True)
        filter_layout.addWidget(checkbox)
        checkbox.stateChanged.connect(lambda: apply_status_filter(self, window, table_timeline, client_code, selected_year, status_filters))

    timeline_layout.addLayout(filter_layout)

    bottom_layout = QHBoxLayout()
    btn_edit = QPushButton(qta.icon('fa5s.edit', color='gray'), " تعديل البدء والانتهاء", window)
    btn_edit.setMinimumSize(QSize(0, 40))
    btn_edit.clicked.connect(lambda: edit_start_end(window, table_timeline, selected_year))
    bottom_layout.addWidget(btn_edit)

    #دبل كلك لتعديل المرحلة
    table_timeline.doubleClicked.connect(lambda: edit_start_end(window, table_timeline, selected_year))

    btn_edit1 = QPushButton(qta.icon('fa5s.edit', color='gray'), "تعديل الحالة ", window)
    btn_edit1.setMinimumSize(QSize(0, 40))
    btn_edit1.clicked.connect(lambda: edit_status(window, table_timeline, selected_year))
    bottom_layout.addWidget(btn_edit1)

    btn_edit2 = QPushButton(qta.icon('fa5s.edit', color='gray'), "تم الإنتهاء ", window)
    btn_edit2.setMinimumSize(QSize(0, 40))
    btn_edit2.clicked.connect(lambda: set_row_completed(window, table_timeline, selected_year))
    bottom_layout.addWidget(btn_edit2)

    btn_edit3 = QPushButton(qta.icon('fa5s.edit', color='gray'), "إنتهاء جميع المراحل", window)
    btn_edit3.setMinimumSize(QSize(0, 40))
    btn_edit3.clicked.connect(lambda: set_all_rows_completed(window, table_timeline, selected_year))
    bottom_layout.addWidget(btn_edit3)

    timeline_layout.addLayout(bottom_layout)
    tab_widget.addTab(tab_timeline, "الجدول الزمني")

    # تبويب "مهام الموظفين"
    tab_employees = QDialog(self)
    employees_layout = QVBoxLayout(tab_employees)

    global table_employees
    table_employees = QTableWidget(window)
    headers = ["id","الرقم", "               المهندس               ", "                      وصف المرحلة                    ", "      الطابق      ", "  % النسبة  ", "  مبلغ المهندس  ", "  حالة المبلغ  "]
    add_table_column(table_employees,headers)
    
    employees_layout.addWidget(table_employees)

    bottom_layout = QHBoxLayout()
    btn_edit = QPushButton(qta.icon('fa5s.edit', color='gray'), "إدراج الرصيد", window)
    btn_edit.setMinimumSize(QSize(0, 40))
    btn_edit.clicked.connect(lambda: open_balance_dialog(window, table_employees,project_name, selected_year))
    bottom_layout.addWidget(btn_edit)

    btn_edit1 = QPushButton(qta.icon('fa5s.edit', color='gray'), "إدراج جميع الارصدة", window)
    btn_edit1.setMinimumSize(QSize(0, 40))
    btn_edit1.clicked.connect(lambda: open_balance_dialog(window, table_employees,project_name,  selected_year))
    bottom_layout.addWidget(btn_edit1)

    employees_layout.addLayout(bottom_layout)
    tab_widget.addTab(tab_employees, "حسابات المهندسين")

    def on_tab_changed(index):
        if tab_widget.tabText(index) == "الجدول الزمني":
            load_timeline_data(self, window, table_timeline, client_id, selected_year)
        elif tab_widget.tabText(index) == "حسابات المهندسين":
            load_employees_data(self, table_employees, client_id, selected_year)
        elif tab_widget.tabText(index) == "مراحل التصميم":
            load_design_data(self, table_phases, client_id, selected_year)
            design_update_totals(table_phases, table_employees, total_cost_label, total_employees, total_compnue)

    load_timeline_data(self, window, table_timeline, client_id, selected_year)
    load_employees_data(self, table_employees, client_id, selected_year)

    design_update_totals(table_phases, table_employees, total_cost_label, total_employees, total_compnue)
    tab_widget.currentChanged.connect(on_tab_changed)
    center_all_widgets(window)
    window.show()


# تحديث الإجماليات جدول التصميم  
def design_update_totals(table_phases, table_employees, total_cost_label, total_employees_label, total_compnue_label):
    # جمع الإجمالي من جدول مراحل التصميم
    total_phase_amount = 0
    for row in range(table_phases.rowCount()):
        try:
            val = int(table_phases.item(row, 6).text().replace(",", "").strip())
            total_phase_amount += val
        except:
            continue

    # جمع مبلغ المهندس من جدول المهندسين
    total_engineer_amount = 0
    for row in range(table_employees.rowCount()):
        try:
            val = int(table_employees.item(row, 5).text().replace(",", "").strip())
            total_engineer_amount += val
        except:
            continue

    # حساب صافي الشركة
    company_net = total_phase_amount - total_engineer_amount

    # تحديث الواجهات
    total_cost_label.setText(f"إجمالي المراحل: {total_phase_amount}")
    total_employees_label.setText(f"إجمالي المهندسين: {total_engineer_amount}")
    total_compnue_label.setText(f"صافي الشركة: {company_net}")


# إدراج الإجمالي الى مبلغ العميل
def update_project_total(self, table_phases, selected_year):
    conn = None
    cursor = None
    try:
        # التحقق من وجود صفوف محددة
        selected_rows = table_phases.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "خطأ", "يجب تحديد صف واحد على الأقل")
            return

        # جمع idات الصفوف المحددة وتجنب التكرار
        selected_ids = []
        total_amount = 0.0  # استخدام float للتعامل مع الأعداد العشرية
        processed_rows = set()  # لتتبع الصفوف التي تمت معالجتها
        for item in table_phases.selectedItems():
            row = item.row()
            if row in processed_rows:  # تجنب معالجة الصف أكثر من مرة
                continue
            processed_rows.add(row)

            # التأكد من أن الصف لم يتم إدراجه مسبقاً
            status_item = table_phases.item(row, 8)  # حالة_المبلغ
            if status_item and status_item.text() == "تم الإدراج":
                continue

            معرف_item = table_phases.item(row, 0)  # id (العمود المخفي)
            amount_item = table_phases.item(row, 7)  # الإجمالي
            if معرف_item and amount_item:
                try:
                    amount = float(amount_item.text().replace(",", "").strip())  # تحويل النص إلى عدد
                    selected_ids.append(int(معرف_item.text()))
                    total_amount += amount
                except ValueError:
                    QMessageBox.warning(self, "خطأ", f"القيمة في الصف {row + 1} غير صالحة")
                    continue

        if not selected_ids:
            QMessageBox.information(self, "معلومات", "جميع الصفوف المحددة تم إدراجها مسبقاً")
            return

        # الاتصال بقاعدة البيانات
        db_name = f"project_manager_V2"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        # تحديث حقل المبلغ في جدول المشاريع
        query_update = """
            UPDATE المشاريع
            SET المبلغ = المبلغ + %s
            WHERE id = %s
        """
        cursor.execute(query_update, (total_amount, client_id))

        # إعادة حساب الباقي (المبلغ - المدفوع)
        query_update_remaining = """
            UPDATE المشاريع
            SET الباقي = المبلغ - المدفوع
            WHERE id = %s
        """
        cursor.execute(query_update_remaining, (client_id,))

        # تحديث حالة_المبلغ للصفوف المحددة
        query_update_status = """
            UPDATE التصميم
            SET حالة_المبلغ = 'تم الإدراج'
            WHERE id IN (%s)
        """ % ','.join(['%s'] * len(selected_ids))
        cursor.execute(query_update_status, selected_ids)

        # تأكيد التغييرات
        conn.commit()

        # إعادة تحميل بيانات جدول التصميم
        load_design_data(self, table_phases, client_id, selected_year)

        # إعادة تحميل بيانات جدول المشاريع في الواجهة الرئيسية
        if hasattr(self, 'table') and self.Interface_combo.currentText() == "المشاريع":
            self.load_data()

        QMessageBox.information(self, "نجاح", f"تم إدراج المبلغ الإجمالي ({total_amount:.2f}) بنجاح")

    except mysql.connector.Error as err:
        QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث الإجمالي: {err}")
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {e}")
    finally:
        if cursor is not None:
            cursor.close()
        if conn is not None:
            conn.close()

# جلب أسماء المهندسين من جدول الموظفين
def populate_engineers(combo_box, selected_year):
    db_name = f"project_manager_V2"
    try:
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT التصنيف, اسم_الموظف, الوظيفة, النسبة 
            FROM الموظفين 
            WHERE الوظيفة NOT LIKE '%استقبال%' 
              AND الوظيفة NOT LIKE '%موظف%'
              AND الوظيفة NOT LIKE '%عامل%'
        """)
        engineers = cursor.fetchall()
        
        combo_box.clear()
        combo_box.addItem("")
        engineer_list = []
        for code, name, job, commission in engineers:
            display_text = f"{code} - {name} / {job}"
            engineer_list.append(display_text)
            combo_box.addItem(display_text)
            combo_box.setItemData(combo_box.count() - 1, commission)
        
        completer = QCompleter(engineer_list)
        completer.setCaseSensitivity(Qt.CaseInsensitive)
        completer.setFilterMode(Qt.MatchContains)
        combo_box.setCompleter(completer)
    
    except mysql.connector.Error as err:
        print("Database error:", err)
    finally:
        cursor.close()
        conn.close()

# دالة لتحديث نسبة المهندس في حقل المبلغ
def update_commission(engineer_edit, amount_edit):
    index = engineer_edit.currentIndex()
    if index > 0:
        commission = engineer_edit.itemData(index)
        if commission is not None:
            try:
                commission_val = int(commission)
                if 0 < commission_val <= 100:
                    amount_edit.setText(f"{commission_val}%")
                else:
                    amount_edit.setText(str(commission_val))
            except ValueError:
                amount_edit.clear()
        else:
            amount_edit.clear()
    else:
        amount_edit.clear()

# تحديث الإجماليات عند اضافة مرحلة
def update_totals(project_area_edit, price_edit, engineer_share_edit, total_label, total_engineer):
    try:
        price_val = int(price_edit.text())
        area_val = int(project_area_edit.text())
        total_price = price_val * area_val
    except ValueError:
        total_price = 0.0

    engineer_share_text = engineer_share_edit.text().strip()
    if "%" in engineer_share_text:
        try:
            percent_val = int(engineer_share_text.replace("%", "").strip())
            engineer_share_val = total_price * (percent_val / 100.0)
        except ValueError:
            engineer_share_val = 0.0
    else:
        try:
            engineer_share_val = int(engineer_share_text)
        except ValueError:
            engineer_share_val = 0.0

    total_label.setText(f"إجمالي مبلغ المرحلة :{total_price:.2f}")
    total_engineer.setText(f"إجمالي مبلغ المهندس :{engineer_share_val:.2f}")

#-------------------# دالة إضافة مرحلة-----------------------------------------------
def open_phase_window(window, selected_year):
    phase_window = QDialog(window)
    phase_window.setWindowTitle("إضافة مرحلة")
    phase_window.resize(900, 320)
    phase_window.setLayoutDirection(Qt.RightToLeft)

    phase_input_layout = QGridLayout(phase_window)

    total_label = QLabel("إجمالي التكلفة: ", phase_window)
    total_engineer = QLabel("مبلغ المهندس: ", phase_window)
    phase_input_layout.addWidget(total_label, 0, 0, 1, 2)
    phase_input_layout.addWidget(total_engineer, 0, 2, 1, 2)

    project_type_label = QLabel(" نوع التصميم: ", phase_window)
    project_type_combo = QComboBox(phase_window)
    project_type_combo.setMinimumWidth(280)
    
    project_type_combo.addItem("")
    project_type_combo.addItems(["خارجي", "داخلي", "حدائق", "السور"])
    project_type_combo.setStyleSheet("QComboBox { text-align: center; }")
    ComboBox_completer(project_type_combo)

    phase_input_layout.addWidget(project_type_label, 1, 0)
    phase_input_layout.addWidget(project_type_combo, 1, 1)

    phase_name_label = QLabel(" وصف المرحلة: ", phase_window)
    phase_name_combo = QComboBox(phase_window)
    phases_external = ["الرفع المساحي", "مقترح مبدئي 2D", "تصميم 2D", "تصميم 3D", "تصميم إنشائي", "خرائط كهربائية", "خرائط صحية", "(الرندر) الإظهار", "رسومات التنفيذية", "تشطيب اللوحات والطباعة", "خرائط التبريد والتكييف", "خرائط منظومة إطفاء الحرائق", "خرائط منظومة الكميرات", "خرائط التدفئة المركزية"]
    phases_internal = ["الرفع المساحي", "مقترح مبدئي 2D", "تصميم 2D", "تصميم 3D", "الرندر", "اللوحات التنفيذية", "تشطيب اللوحات والطباعة"]
    phase_name_combo.addItems(phases_external)
    ComboBox_completer(phase_name_combo)
    phase_name_combo.setStyleSheet("QComboBox { text-align: center; }")
    phase_input_layout.addWidget(phase_name_label, 1, 2)
    phase_input_layout.addWidget(phase_name_combo, 1, 3)

    floor_type_label = QLabel(" الطابق:", phase_window)
    floor_type_combo = QComboBox(phase_window)
    floor_type = ["الأرضي", "الأول", "الثاني", "الثالث", "الرابع", "الخامس", "السادس", "بدروم", "ملحق"]
    floor_type_combo.addItem("")
    floor_type_combo.addItems(floor_type)
    floor_type_combo.setStyleSheet("QComboBox { text-align: center; }")
    completer = QCompleter(floor_type_combo.model(), floor_type_combo)
    completer.setCaseSensitivity(Qt.CaseInsensitive)
    completer.setFilterMode(Qt.MatchContains)
    phase_input_layout.addWidget(floor_type_label, 2, 0)
    phase_input_layout.addWidget(floor_type_combo, 2, 1)

    price_type_label = QLabel("الوحدة:", phase_window)
    price_type_combo = QComboBox(phase_window)
    price_type_combo.addItems(["مقطوعية", "متر مربع", "متر طولي", "متر مكعب"])
    price_type_combo.setStyleSheet("QComboBox { text-align: center; }")
    phase_input_layout.addWidget(price_type_label, 3, 0)
    phase_input_layout.addWidget(price_type_combo, 3, 1)

    engineer_label = QLabel("المهندس:", phase_window)
    engineer_edit = QComboBox(phase_window)
    engineer_edit.setEditable(True)
    populate_engineers(engineer_edit, selected_year)
    engineer_edit.currentIndexChanged.connect(lambda: update_commission(engineer_edit, engineer_share_edit))
    phase_input_layout.addWidget(engineer_label, 4, 0)
    phase_input_layout.addWidget(engineer_edit, 4, 1)

    project_area_label = QLabel(" مساحة / كمية: ", phase_window)
    project_area_edit = QLineEdit(phase_window)
    project_area_edit.setPlaceholderText("أدخل المساحة أو الكمية")
    project_area_edit.setAlignment(Qt.AlignCenter)
    phase_input_layout.addWidget(project_area_label, 2, 2)
    phase_input_layout.addWidget(project_area_edit, 2, 3)

    price_label = QLabel("السعر:", phase_window)
    price_edit = QLineEdit(phase_window)
    price_edit.setPlaceholderText("أدخل السعر")
    price_edit.setAlignment(Qt.AlignCenter)
    phase_input_layout.addWidget(price_label, 3, 2)
    phase_input_layout.addWidget(price_edit, 3, 3)

    engineer_share_label = QLabel(" مبلغ المهندس: ", phase_window)
    engineer_share_edit = QLineEdit(phase_window)
    engineer_share_edit.setPlaceholderText("أدخل النسبة أو المبلغ")
    engineer_share_edit.setAlignment(Qt.AlignCenter)
    phase_input_layout.addWidget(engineer_share_label, 4, 2)
    phase_input_layout.addWidget(engineer_share_edit, 4, 3)

    note_label = QLabel(" ملاحظات: ", phase_window)
    note_edit = QLineEdit(phase_window)
    note_edit.setPlaceholderText("أدخل الملاحظات إن وجدت")
    note_edit.setAlignment(Qt.AlignCenter)
    phase_input_layout.addWidget(note_label, 5, 0)
    phase_input_layout.addWidget(note_edit, 5, 1,1,3)

    add_button = QPushButton(qta.icon('fa5s.plus', color='darkgreen'), "تأكيد ", phase_window)
    add_button.setObjectName("تأكيد")  # مهم جدا: نحدد اسم الزر
    #add_button.clicked.connect(lambda: add_phase_to_table(project_type_combo, floor_type_combo, project_area_edit, phase_name_combo, price_type_combo, price_edit, engineer_edit, engineer_share_edit, note_edit, phase_window, selected_year))
    add_button.clicked.connect(lambda:add_phase_to_table(project_type_combo, floor_type_combo, project_area_edit, phase_name_combo, price_type_combo, 
                      price_edit, engineer_edit, engineer_share_edit, note_edit, phase_window, selected_year,
                      client_id, client_name, project_name, table_phases, table_employees))
    
    phase_input_layout.addWidget(add_button, 6, 0, 1, 2)

    cancel_button = QPushButton("إلغاء", phase_window)
    cancel_button.clicked.connect(lambda: cancel_phase_window(phase_window))
    phase_input_layout.addWidget(cancel_button, 6, 2, 1, 2)

    center_all_widgets(phase_window)

    project_area_edit.textChanged.connect(lambda: update_totals(project_area_edit, price_edit, engineer_share_edit, total_label, total_engineer))
    price_edit.textChanged.connect(lambda: update_totals(project_area_edit, price_edit, engineer_share_edit, total_label, total_engineer))
    engineer_share_edit.textChanged.connect(lambda: update_totals(project_area_edit, price_edit, engineer_share_edit, total_label, total_engineer))
    project_type_combo.currentIndexChanged.connect(lambda: update_phase_options(project_type_combo, phase_name_combo, floor_type_combo, phases_external, phases_internal, floor_type))

    focus_widgets = [
        project_type_combo,
        floor_type_combo,
        price_type_combo,
        engineer_edit,
        phase_name_combo,
        project_area_edit,
        price_edit,
        engineer_share_edit,
        note_edit,
    ]    
    apply_enter_focus(phase_window, "تأكيد",focus_widgets)
    phase_window.exec_()

# تأكيد إضافة مرحلة
def add_phase_to_table(project_type_combo, floor_type_combo, project_area_edit, phase_name_combo, price_type_combo, 
                      price_edit, engineer_edit, engineer_share_edit, note_edit, phase_window, selected_year,
                      client_id, client_name, project_name, table_phases, table_employees):
    
    project_type = project_type_combo.currentText()
    floor_type = floor_type_combo.currentText()
    phase_name = phase_name_combo.currentText()
    project_area = project_area_edit.text()
    unit = price_type_combo.currentText()
    price = price_edit.text()
    engineer_share = engineer_share_edit.text()
    note = note_edit.text()
    phase = floor_type + " / " + project_type

    engineer_full_text = engineer_edit.currentText()
    engineer = parts[1].split("/")[0].strip() if len(parts := engineer_full_text.split("-")) >= 2 else engineer_full_text.strip()
    if not engineer:
        engineer = "المدير"

    if not price or not project_area:
        QMessageBox.warning(phase_window, "خطأ", "يرجى ملء الوصف والسعر والمساحة/الكمية")
        return

    try:
        price_val = float(price)
        area_val = float(project_area)
    except ValueError:
        QMessageBox.warning(phase_window, "خطأ", "السعر والمساحة/الكمية يجب أن يكونا أرقامًا")
        return

    total_price = price_val * area_val

    if "%" in engineer_share:
        try:
            percent_val = int(engineer_share.replace("%", "").strip())
        except ValueError:
            QMessageBox.warning(phase_window, "خطأ", "مبلغ المهندس يجب أن يكون رقمًا أو نسبة صحيحة")
            return
        computed_engineer_share = total_price * (percent_val / 100.0)
        engineer_share_final = str(computed_engineer_share)
        percentage = str(percent_val)
    else:
        percentage = "0"
        engineer_share_final = engineer_share if engineer_share else "0"
    
    # الحصول على معرف_المهندس
    db_name = f"project_manager_V2"
    try:
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()
        cursor.execute("SELECT id FROM الموظفين WHERE اسم_الموظف=%s", (engineer,))
        engineer_id = cursor.fetchone()
        engineer_id = engineer_id[0] if engineer_id else None
    except mysql.connector.Error as err:
        QMessageBox.critical(phase_window, "خطأ", f"حدث خطأ أثناء استرجاع id المهندس: {err}")
        return
    finally:
        cursor.close()
        conn.close()

    # التحقق من وجود معرف_العميل في جدول PROJECTS
    try:
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()
        cursor.execute("SELECT id FROM المشاريع WHERE id=%s", (client_id,))
        client_exists = cursor.fetchone()
        if not client_exists:
            QMessageBox.critical(phase_window, "خطأ", f"id العميل {client_id} غير موجود في جدول PROJECTS")
            return
    except mysql.connector.Error as err:
        QMessageBox.critical(phase_window, "خطأ", f"حدث خطأ أثناء التحقق من id العميل: {err}")
        return
    finally:
        cursor.close()
        conn.close()

    confirm = QMessageBox.question(
        phase_window, "تأكيد الإضافة",
        f"هل تريد إضافة المرحلة التالية؟\n"
        f"المرحلة: {phase_name}\n"
        f"المبلغ الإجمالي: {total_price:.2f}\n"
        f"المهندس: {engineer}\n"
        f"مبلغ المهندس: {engineer_share_final}",
        QMessageBox.Yes | QMessageBox.No
    )
    if confirm != QMessageBox.Yes:
        return

    try:
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        query = """
            INSERT INTO التصميم ( المرحلة, الطابق, الوحدة, الكمية, السعر, الإجمالي, حالة_المبلغ, المهندس, ملاحظات,
                نسبة_المهندس, مبلغ_المهندس, حالة_مبلغ_المهندس, معرف_المهندس, معرف_العميل, اسم_العميل, اسم_المشروع
            ) VALUES ( %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        values = (
            phase_name, phase, unit, project_area, price, str(total_price), "غير مدرج", engineer, note,
            percentage, engineer_share_final, "غير مدرج", engineer_id, client_id, client_name, project_name
        )
        cursor.execute(query, values)
        conn.commit()

    except mysql.connector.Error as err:
        QMessageBox.critical(phase_window, "خطأ", f"حدث خطأ أثناء تسجيل البيانات: {err}")
        return
    finally:
        cursor.close()
        conn.close()

    QMessageBox.information(phase_window, "تمت الإضافة", "تمت إضافة المرحلة بنجاح!")

    row_position = table_phases.rowCount()
    table_phases.insertRow(row_position)

    def create_item(text):
        item = QTableWidgetItem(str(text))
        item.setTextAlignment(Qt.AlignCenter)
        return item

    table_phases.setItem(row_position, 1, create_item(row_position + 1))
    table_phases.setItem(row_position, 2, create_item(phase_name))
    table_phases.setItem(row_position, 3, create_item(phase))
    table_phases.setItem(row_position, 4, create_item(unit))
    table_phases.setItem(row_position, 5, create_item(project_area))
    table_phases.setItem(row_position, 6, create_item(price))
    table_phases.setItem(row_position, 7, create_item(total_price))
    table_phases.setItem(row_position, 8, create_item("غير مدرج"))
    table_phases.setItem(row_position, 9, create_item(engineer))
    table_phases.setItem(row_position, 10, create_item(note))
    table_employees.setItem(row_position, 6, create_item("غير مدرج"))

    current_index = phase_name_combo.currentIndex()
    if current_index < phase_name_combo.count() - 1:
        phase_name_combo.setCurrentIndex(current_index + 1)

# إلغاء إضافة مرحلة
def cancel_phase_window(phase_window):
    phase_window.reject()

# تحديث قائمة المراحل بناءً على نوع المشروع
def update_phase_options(project_type_combo, phase_name_combo, floor_type_combo, phases_external, phases_internal, floor_type):
    project_type = project_type_combo.currentText()
    phase_name_combo.clear()
    floor_type_combo.clear()
    if project_type == "تصميم خارجي" or project_type == "خارجي":
        phase_name_combo.addItems(phases_external)
        floor_type_combo.addItems(floor_type)
    elif project_type == "تصميم داخلي" or project_type == "داخلي":
        phase_name_combo.addItems(phases_internal)
        floor_type_combo.addItems(floor_type)
    else:
        phase_name_combo.addItems(phases_internal)
        floor_type_combo.addItems("")


# دالة تعديل مرحلة
def edit_phase(window, table, selected_year):
    selected_items = table.selectedItems()
    if not selected_items:
        QMessageBox.warning(None, "خطأ", "يرجى تحديد مرحلة للتعديل")
        return
    selected_row = table.currentRow()

    phase_window = QDialog(window)
    phase_window.setWindowTitle("تعديل مرحلة")
    phase_window.resize(700, 320)
    phase_window.setLayoutDirection(Qt.RightToLeft)

    phase_input_layout = QGridLayout(phase_window)

    total_label = QLabel("إجمالي التكلفة: ", phase_window)
    total_engineer = QLabel("مبلغ المهندس: ", phase_window)
    phase_input_layout.addWidget(total_label, 0, 0, 1, 2)
    phase_input_layout.addWidget(total_engineer, 0, 2, 1, 2)

    project_type_label = QLabel(" نوع التصميم: ", phase_window)
    project_type_combo = QComboBox(phase_window)
    project_type_combo.setEditable(True)
    project_type_combo.addItem("")
    project_type_combo.addItems(["خارجي", "داخلي", "حدائق", "السور"])
    project_type_combo.setStyleSheet("QComboBox { text-align: center; }")
    completer = QCompleter(project_type_combo.model(), project_type_combo)
    completer.setCaseSensitivity(Qt.CaseInsensitive)
    completer.setFilterMode(Qt.MatchContains)
    project_type_combo.setCompleter(completer)
    phase_input_layout.addWidget(project_type_label, 1, 0)
    phase_input_layout.addWidget(project_type_combo, 1, 1)

    phase_name_label = QLabel(" وصف المرحلة: ", phase_window)
    phase_name_combo = QComboBox(phase_window)
    phases_external = ["الرفع المساحي", "مقترح مبدئي 2D", "تصميم 2D", "تصميم 3D", "تصميم إنشائي", "خرائط كهربائية", "خرائط صحية", "(الرندر) الإظهار", "رسومات التنفيذية", "تشطيب اللوحات والطباعة", "خرائط التبريد والتكييف", "خرائط منظومة إطفاء الحرائق", "خرائط منظومة الكميرات", "خرائط التدفئة المركزية"]
    phases_internal = ["الرفع المساحي", "مقترح مبدئي 2D", "تصميم 2D", "تصميم 3D", "الرندر", "اللوحات التنفيذية", "تشطيب اللوحات والطباعة"]
    phase_name_combo.addItems(phases_external)
    phase_name_combo.setStyleSheet("QComboBox { text-align: center; }")
    phase_input_layout.addWidget(phase_name_label, 1, 2)
    phase_input_layout.addWidget(phase_name_combo, 1, 3)

    floor_type_label = QLabel(" الطابق:", phase_window)
    floor_type_combo = QComboBox(phase_window)
    floor_type = ["الأرضي", "الأول", "الثاني", "الثالث", "الرابع", "الخامس", "السادس", "بدروم", "ملحق"]
    floor_type_combo.addItem("")
    floor_type_combo.addItems(floor_type)
    floor_type_combo.setStyleSheet("QComboBox { text-align: center; }")
    completer = QCompleter(floor_type_combo.model(), floor_type_combo)
    completer.setCaseSensitivity(Qt.CaseInsensitive)
    completer.setFilterMode(Qt.MatchContains)
    phase_input_layout.addWidget(floor_type_label, 2, 0)
    phase_input_layout.addWidget(floor_type_combo, 2, 1)

    price_type_label = QLabel("الوحدة:", phase_window)
    price_type_combo = QComboBox(phase_window)
    price_type_combo.addItems(["مقطوعية", "متر مربع", "متر طولي", "متر مكعب"])
    phase_input_layout.addWidget(price_type_label, 3, 0)
    phase_input_layout.addWidget(price_type_combo, 3, 1)

    engineer_label = QLabel("المهندس:", phase_window)
    engineer_edit = QComboBox(phase_window)
    engineer_edit.setEditable(True)
    populate_engineers(engineer_edit, selected_year)
    phase_input_layout.addWidget(engineer_label, 4, 0)
    phase_input_layout.addWidget(engineer_edit, 4, 1)

    project_area_label = QLabel(" مساحة / كمية: ", phase_window)
    project_area_edit = QLineEdit(phase_window)
    project_area_edit.setAlignment(Qt.AlignCenter)
    phase_input_layout.addWidget(project_area_label, 2, 2)
    phase_input_layout.addWidget(project_area_edit, 2, 3)

    price_label = QLabel("السعر:", phase_window)
    price_edit = QLineEdit(phase_window)
    price_edit.setAlignment(Qt.AlignCenter)
    phase_input_layout.addWidget(price_label, 3, 2)
    phase_input_layout.addWidget(price_edit, 3, 3)

    engineer_share_label = QLabel(" مبلغ المهندس: ", phase_window)
    engineer_share_edit = QLineEdit(phase_window)
    engineer_share_edit.setAlignment(Qt.AlignCenter)
    phase_input_layout.addWidget(engineer_share_label, 4, 2)
    phase_input_layout.addWidget(engineer_share_edit, 4, 3)

    note_label = QLabel(" ملاحظات: ", phase_window)
    note_edit = QLineEdit(phase_window)
    note_edit.setAlignment(Qt.AlignCenter)
    phase_input_layout.addWidget(note_label, 5, 0)
    phase_input_layout.addWidget(note_edit, 5, 1, 1, 3)

    save_button = QPushButton("حفظ", phase_window)
    save_button.clicked.connect(lambda: save_edited_phase(table, selected_row, project_type_combo, floor_type_combo, project_area_edit, phase_name_combo, price_type_combo, price_edit, engineer_edit, engineer_share_edit, note_edit, phase_window, selected_year))
    phase_input_layout.addWidget(save_button, 6, 0, 1, 2)

    cancel_button = QPushButton("إلغاء", phase_window)
    cancel_button.clicked.connect(lambda: phase_window.reject())
    phase_input_layout.addWidget(cancel_button, 6, 2, 1, 2)

    # تعبئة الحقول بالبيانات الحالية
    phase_name_combo.setCurrentText(table.item(selected_row, 1).text())
    floor_type_combo.setCurrentText(table.item(selected_row, 2).text().split(" / ")[0])
    project_type_combo.setCurrentText(table.item(selected_row, 2).text().split(" / ")[1])
    price_type_combo.setCurrentText(table.item(selected_row, 3).text())
    project_area_edit.setText(table.item(selected_row, 4).text())
    price_edit.setText(table.item(selected_row, 5).text())
    engineer_edit.setCurrentText(table.item(selected_row, 7).text())
    engineer_share_edit.setText(table.item(selected_row, 4).text() if "%" in table.item(selected_row, 4).text() else table.item(selected_row, 4).text())
    note_edit.setText(table.item(selected_row, 8).text())

    project_area_edit.textChanged.connect(lambda: update_totals(project_area_edit, price_edit, engineer_share_edit, total_label, total_engineer))
    price_edit.textChanged.connect(lambda: update_totals(project_area_edit, price_edit, engineer_share_edit, total_label, total_engineer))
    engineer_share_edit.textChanged.connect(lambda: update_totals(project_area_edit, price_edit, engineer_share_edit, total_label, total_engineer))
    project_type_combo.currentIndexChanged.connect(lambda: update_phase_options(project_type_combo, phase_name_combo, floor_type_combo, phases_external, phases_internal, floor_type))

    phase_window.exec_()

# دالة حفظ المرحلة المعدلة
def save_edited_phase(table, row, project_type_combo, floor_type_combo, project_area_edit, phase_name_combo, price_type_combo, price_edit, engineer_edit, engineer_share_edit, note_edit, phase_window, selected_year):
    project_type = project_type_combo.currentText()
    floor_type = floor_type_combo.currentText()
    phase_name = phase_name_combo.currentText()
    project_area = project_area_edit.text()
    unit = price_type_combo.currentText()
    price = price_edit.text()
    engineer_share = engineer_share_edit.text()
    note = note_edit.text()
    phase = floor_type + " / " + project_type

    engineer_full_text = engineer_edit.currentText()
    engineer = parts[1].split("/")[0].strip() if len(parts := engineer_full_text.split("-")) >= 2 else engineer_full_text.strip()
    if not engineer:
        engineer = "المدير"

    if not price or not project_area:
        QMessageBox.warning(phase_window, "خطأ", "يرجى ملء الوصف والسعر والمساحة/الكمية")
        return

    try:
        price_val = float(price)
        area_val = float(project_area)
    except ValueError:
        QMessageBox.warning(phase_window, "خطأ", "السعر والمساحة/الكمية يجب أن يكونا أرقامًا")
        return

    total_price = price_val * area_val

    if "%" in engineer_share:
        try:
            percent_val = int(engineer_share.replace("%", "").strip())
        except ValueError:
            QMessageBox.warning(phase_window, "خطأ", "مبلغ المهندس يجب أن يكون رقمًا أو نسبة صحيحة")
            return
        computed_engineer_share = total_price * (percent_val / 100.0)
        engineer_share_final = str(computed_engineer_share)
        percentage = str(percent_val)
    else:
        percentage = "0"
        engineer_share_final = engineer_share if engineer_share else "0"

    try:
        db_name = f"project_manager_V2"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()
        cursor.execute("SELECT id FROM الموظفين WHERE اسم_الموظف=%s", (engineer,))
        engineer_id = cursor.fetchone()
        engineer_id = engineer_id[0] if engineer_id else None
    except mysql.connector.Error as err:
        QMessageBox.critical(phase_window, "خطأ", f"حدث خطأ أثناء استرجاع id المهندس: {err}")
        return

    try:
        query = """
            UPDATE المشاريع_المراحل
            SET المرحلة=%s, الطابق=%s, الوحدة=%s, الكمية=%s, السعر=%s, الإجمالي=%s, المهندس=%s, ملاحظات=%s,
                نسبة_المهندس=%s, مبلغ_المهندس=%s, معرف_المهندس=%s
            WHERE معرف_العميل=%s AND المرحلة=%s
        """
        values = (
            phase_name, phase, unit, project_area, price, str(total_price), engineer, note,
            percentage, engineer_share_final, engineer_id, client_code, table.item(row, 1).text()
        )
        cursor.execute(query, values)
        conn.commit()

        def create_item(text):
            item = QTableWidgetItem(text)
            item.setTextAlignment(Qt.AlignCenter)
            if text == "غير مدرج":
                item.setBackground(QColor("#e9ad6e"))
            elif text == "تم الإدراج":
                    item.setBackground(QColor("#cdd7b9"))  # اللون البرتقالي
            return item

        table.setItem(row, 1, create_item(phase_name))
        table.setItem(row, 2, create_item(phase))
        table.setItem(row, 3, create_item(unit))
        table.setItem(row, 4, create_item(project_area))
        table.setItem(row, 5, create_item(price))
        table.setItem(row, 6, create_item(str(total_price)))
        table.setItem(row, 7, create_item(engineer))
        table.setItem(row, 8, create_item(note))

        QMessageBox.information(phase_window, "نجاح", "تم تعديل المرحلة بنجاح")
        phase_window.accept()

    except mysql.connector.Error as err:
        QMessageBox.critical(phase_window, "خطأ", f"حدث خطأ أثناء تعديل البيانات: {err}")
    finally:
        cursor.close()
        conn.close()

# دالة حذف مرحلة
def delete_phase(table_phases, client_id, selected_year):
    selected_items = table_phases.selectedItems()
    if not selected_items:
        QMessageBox.warning(None, "خطأ", "يرجى تحديد مرحلة أو أكثر لحذفها")
        return

    selected_rows = list(set(item.row() for item in selected_items))
    
    confirm = QMessageBox.question(
        None, "تأكيد الحذف",
        f"هل أنت متأكد من حذف {len(selected_rows)} مرحلة؟",
        QMessageBox.Yes | QMessageBox.No
    )
    if confirm != QMessageBox.Yes:
        return

    try:
        db_name = f"project_manager_V2"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        phase_ids = []
        for row in selected_rows:
            phase_معرف_item = table_phases.item(row,0)
            if phase_معرف_item:
                phase_ids.append(phase_معرف_item.text())

        for phase_id in phase_ids:
            query = "DELETE FROM المشاريع_المراحل WHERE id = %s AND معرف_العميل = %s"
            cursor.execute(query, (phase_id, client_id))

        conn.commit()

        for row in sorted(selected_rows, reverse=True):
            table_phases.removeRow(row)

        load_timeline_data(None, None, table_timeline, client_code, selected_year)
        load_employees_data(None, table_employees, client_code, selected_year)

        QMessageBox.information(None, "نجاح", "تم حذف المراحل المحددة بنجاح")

    except mysql.connector.Error as err:
        QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء الحذف: {err}")
    finally:
        cursor.close()
        conn.close()


#الجدول الزمني اجراءات/////////////////////////////////////
# دالة فتح نافذة تعديل البدء والانتهاء
def edit_start_end(window, table_timeline, selected_year):
    selected_items = table_timeline.selectedItems()
    if not selected_items:
        QMessageBox.warning(None, "خطأ", "يرجى تحديد صف واحد لتعديل البدء والانتهاء")
        return
    selected_row = table_timeline.currentRow()
    open_edit_dialog(window, table_timeline, selected_row, selected_year)

# دالة فتح نافذة تعديل الحالة
def edit_status(window, table_timeline, selected_year):
    selected_items = table_timeline.selectedItems()
    if not selected_items:
        QMessageBox.warning(None, "خطأ", "يرجى تحديد صف واحد لتغيير الحالة")
        return
    selected_row = table_timeline.currentRow()

    dialog = QDialog(window)
    dialog.setWindowTitle("تغيير الحالة")
    dialog.setLayoutDirection(Qt.RightToLeft)
    layout = QVBoxLayout(dialog)

    status_combo = QComboBox()
    status_combo.addItems(["لم يتم البدء", "قيد الإنجاز", "منتهي", "متأخر", "متوقف"])
    status_combo.setCurrentText(table_timeline.item(selected_row, 7).text() if table_timeline.item(selected_row, 7) else "لم يتم البدء")

    btn_save = QPushButton("حفظ")
    btn_save.clicked.connect(lambda: save_status_only(table_timeline, selected_row, status_combo, dialog, client_code, selected_year))

    layout.addWidget(QLabel("الحالة:"))
    layout.addWidget(status_combo)
    layout.addWidget(btn_save)

    dialog.exec_()

# دالة فتح نافذة تعديل الجدول الزمني
def open_edit_dialog(window, table, row, selected_year):
    dialog = QDialog(window)
    dialog.setWindowTitle("تعديل المرحلة")
    dialog.setLayoutDirection(Qt.RightToLeft)
    layout = QVBoxLayout(dialog)

    start_date = QDateEdit()
    start_date.setCalendarPopup(True)
    start_date.setDate(QDate.fromString(table.item(row, 4).text(), "yyyy-MM-dd") if table.item(row, 4) and table.item(row, 4).text() != "غير محدد" else QDate.currentDate())

    end_date = QDateEdit()
    end_date.setCalendarPopup(True)
    end_date.setDate(QDate.fromString(table.item(row, 5).text(), "yyyy-MM-dd") if table.item(row, 5) and table.item(row, 5).text() != "غير محدد" else QDate.currentDate())

    status_combo = QComboBox()
    status_combo.addItems(["لم يتم البدء", "قيد الإنجاز", "منتهي", "متأخر", "متوقف"])
    status_combo.setCurrentText(table.item(row, 7).text() if table.item(row, 7) else "لم يتم البدء")

    btn_save = QPushButton("حفظ")
    btn_save.clicked.connect(lambda: save_timeline_changes(table, row, start_date, end_date, status_combo, dialog, client_code, selected_year))

    layout.addWidget(QLabel("تاريخ البدء:"))
    layout.addWidget(start_date)
    layout.addWidget(QLabel("تاريخ الانتهاء:"))
    layout.addWidget(end_date)
    layout.addWidget(QLabel("الحالة:"))
    layout.addWidget(status_combo)
    layout.addWidget(btn_save)

    dialog.exec_()

# دالة حفظ تغييرات الجدول الزمني
def save_timeline_changes(table, row, start_widget, end_widget, status_widget, dialog, client_code, selected_year):
    start = start_widget.date()
    end = end_widget.date()
    status = status_widget.currentText()
    phase_name = table.item(row, 1).text()

    try:
        db_name = f"project_manager_V2"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        query = """
            UPDATE التصميم
            SET تاريخ_البدء = %s, تاريخ_الإنتهاء = %s, عدد_الأيام = %s,الحالة= %s
            WHERE معرف_العميل = %s AND المرحلة = %s
        """
        values = (start.toString("yyyy-MM-dd"), end.toString("yyyy-MM-dd"), str(start.daysTo(end)), status, client_code, phase_name)
        cursor.execute(query, values)
        conn.commit()

        table.item(row, 4).setText(start.toString("yyyy-MM-dd"))
        table.item(row, 5).setText(end.toString("yyyy-MM-dd"))
        table.item(row, 6).setText(str(start.daysTo(end)))
        table.item(row, 7).setText(status)
        update_row_color(table, row, status)

        QMessageBox.information(None, "نجاح", "تم حفظ التغييرات بنجاح")

    except mysql.connector.Error as err:
        QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء حفظ التغييرات: {err}")
    finally:
        cursor.close()
        conn.close()
        dialog.accept()

# دالة حفظ الحالة فقط
def save_status_only(table, row, status_widget, dialog, client_code, selected_year):
    status = status_widget.currentText()
    phase_name = table.item(row, 1).text()

    try:
        db_name = f"project_manager_V2"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        query = """
            UPDATE التصميم
            SETالحالة= %s
            WHERE معرف_العميل = %s AND المرحلة = %s
        """
        cursor.execute(query, (status, client_code, phase_name))
        conn.commit()

        table.item(row, 7).setText(status)
        update_row_color(table, row, status)

        QMessageBox.information(None, "نجاح", "تم تحديث الحالة بنجاح")

    except mysql.connector.Error as err:
        QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء تحديث الحالة: {err}")
    finally:
        cursor.close()
        conn.close()
        dialog.accept()

# دالة تغيير حالة صف إلى "منتهي"
def set_row_completed(window, table_timeline, selected_year):
    selected_items = table_timeline.selectedItems()
    if not selected_items:
        QMessageBox.warning(None, "خطأ", "يرجى تحديد صف واحد لتغيير الحالة إلى منتهي")
        return
    selected_row = table_timeline.currentRow()
    phase_name = table_timeline.item(selected_row, 1).text()

    try:
        db_name = f"project_manager_V2"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        query = """
            UPDATE التصميم
            SETالحالة= %s, تاريخ_الإنتهاء = %s
            WHERE معرف_العميل = %s AND المرحلة = %s
        """
        cursor.execute(query, ("منتهي", QDate.currentDate().toString("yyyy-MM-dd"), client_code, phase_name))
        conn.commit()

        table_timeline.item(selected_row, 7).setText("منتهي")
        table_timeline.item(selected_row, 5).setText(QDate.currentDate().toString("yyyy-MM-dd"))
        update_row_color(table_timeline, selected_row, "منتهي")

        QMessageBox.information(None, "نجاح", "تم تغيير الحالة إلى منتهي بنجاح")

    except mysql.connector.Error as err:
        QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء تحديث الحالة: {err}")
    finally:
        cursor.close()
        conn.close()

# دالة تغيير حالة جميع الصفوف إلى "منتهي"
def set_all_rows_completed(window, table_timeline, selected_year):
    confirm = QMessageBox.question(
        None, "تأكيد",
        "هل أنت متأكد من تغيير حالة جميع المراحل إلى منتهي؟",
        QMessageBox.Yes | QMessageBox.No
    )
    if confirm != QMessageBox.Yes:
        return

    try:
        db_name = f"project_manager_V2"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        query = """
            UPDATE التصميم
            SETالحالة= %s, تاريخ_الإنتهاء = %s
            WHERE معرف_العميل = %s
        """
        cursor.execute(query, ("منتهي", QDate.currentDate().toString("yyyy-MM-dd"), client_code))
        conn.commit()

        for row in range(table_timeline.rowCount()):
            table_timeline.item(row, 7).setText("منتهي")
            table_timeline.item(row, 5).setText(QDate.currentDate().toString("yyyy-MM-dd"))
            update_row_color(table_timeline, row, "منتهي")

        QMessageBox.information(None, "نجاح", "تم تغيير حالة جميع المراحل إلى منتهي بنجاح")

    except mysql.connector.Error as err:
        QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء تحديث الحالة: {err}")
    finally:
        cursor.close()
        conn.close()

# دالة تطبيق مرشح الحالة
def apply_status_filter(self, window, table_timeline, client_code, selected_year, status_filters):
    selected_statuses = [status for status, checkbox in status_filters.items() if checkbox.isChecked()]
    load_timeline_data(self, window, table_timeline, client_code, selected_year, selected_statuses if selected_statuses else None)


#اجراءات حسابات المهندسين ------------------------------------------
# دالة فتح نافذة إدراج الرصيد
def open_balance_dialog(window, table,project_name,  selected_year):
    selected_items = table.selectedItems()
    if not selected_items:
        QMessageBox.warning(None, "خطأ", "يرجى تحديد صف واحد لإدراج الرصيد")
        return
    selected_row = table.currentRow()

    dialog = QDialog(window)
    dialog.setWindowTitle("إدراج الرصيد")
    dialog.setLayoutDirection(Qt.RightToLeft)
    layout = QVBoxLayout(dialog)

    status_combo = QComboBox()
    status_combo.addItems(["غير مدرج", "تم الإدراج", "ملغى"])
    status_combo.setCurrentText(table.item(selected_row, 9).text() if table.item(selected_row, 9) else "غير مدرج")

    btn_save = QPushButton("حفظ")
    btn_save.clicked.connect(lambda: save_employee_balance(table,project_name, selected_row, status_combo, dialog, client_code, selected_year))

    layout.addWidget(QLabel("حالة مبلغ المهندس:"))
    layout.addWidget(status_combo)
    layout.addWidget(btn_save)

    dialog.exec_()

# دالة حفظ حالة الرصيد
def save_employee_balance(table,project_name,  row, status_widget, dialog, client_code, selected_year):
    status = status_widget.currentText()
    id = table.item(row, 0).text()
    phase_name = table.item(row, 3).text()
    engineer = table.item(row, 2).text()
    engineer_amount = int(table.item(row, 6).text())
    #percentage = table.item(row, 4).text().replace("%", "") if "%" in table.item(row, 4).text() else "0"

    percentage = table.item(row, 5).text()if table.item(row, 5).text() else "0"

    floor = table.item(row, 4).text()
    #project_name = table.item(row, 3).text()

    try:
        db_name = f"project_manager_V2"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        # تحديث حالة مبلغ المهندس
        query = """
            UPDATE التصميم
            SET حالة_مبلغ_المهندس = %s
            WHERE معرف_العميل = %s AND id = %s
        """
        cursor.execute(query, (status, client_id, id ))

        # إذا تم الإدراج، أضف المعاملة إلى تقارير الموظفين
        if status == "تم الإدراج":
            cursor.execute("SELECT id, التصنيف, اسم_الموظف, الوظيفة, الرصيد FROM الموظفين WHERE اسم_الموظف=%s", (engineer,))
            employee = cursor.fetchone()
            if employee:
                employee_id, employee_code, employee_name, job_title, current_balance = employee
                transaction_type = "إضافة نسبة" if percentage != "0" else "إضافة رصيد"
                description = f"{client_name} / {project_name} / {phase_name} / {floor} "
                date = datetime.now().strftime("%Y-%m-%d")

                # إضافة سجل إلى تقارير الموظفين
                cursor.execute('''
                    INSERT INTO الموظفين_معاملات_مالية (التصنيف, اسم_الموظف, الوظيفة, نوع_المعاملة, المبلغ, التاريخ, النسبة, الوصف, معرف_الموظف)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                ''', (employee_code, employee_name, job_title, transaction_type, engineer_amount, date, percentage, description, employee_id))

                # تحديث الرصيد
                new_balance = current_balance + engineer_amount
                cursor.execute("UPDATE الموظفين SET الرصيد=%s WHERE id=%s", (new_balance, employee_id))

        conn.commit()

        table_item = table.item(row, 7)
        table_item.setText(status)
        if status == "غير مدرج":
            table_item.setBackground(QColor("#e9ad6e"))  # برتقالي
        else:
            table_item.setBackground(QColor("#cdd7b9"))  # أبيض

        QMessageBox.information(None, "نجاح", "تم تحديث حالة الرصيد بنجاح")

    except mysql.connector.Error as err:
        QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء تحديث حالة الرصيد: {err}")
    finally:
        cursor.close()
        conn.close()
        dialog.accept()


