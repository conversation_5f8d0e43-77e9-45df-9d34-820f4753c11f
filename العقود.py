#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة العقود
"""

import sys
import os
from datetime import datetime, date
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QPushButton, QLabel, QLineEdit, QDateEdit, QTextEdit, QComboBox, QSpinBox,
    QDoubleSpinBox, QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QDialog, QFileDialog, QTabWidget, QGroupBox, QRadioButton, QCheckBox
)
from PySide6.QtCore import Qt, QDate, Signal, Slot
from PySide6.QtGui import QIcon, QPixmap, QFont, QColor

# استيراد الدوال المساعدة من المشروع الرئيسي
try:
    from functions import *
    from db import *
    from ستايل import *
    from الطباعة import *
except ImportError:
    print("تعذر استيراد الوحدات المطلوبة. تأكد من وجودها في المسار الصحيح.")

# استيراد مكون اختيار الحساب من شجرة الحسابات
try:
    from account_selector import AccountSelector
    from account_linking import link_project_to_account
    ACCOUNT_SELECTOR_AVAILABLE = True
except ImportError:
    ACCOUNT_SELECTOR_AVAILABLE = False
    print("تعذر استيراد مكون اختيار الحساب. لن يتم ربط العقود بشجرة الحسابات.")

class ContractsApp(QMainWindow):
    """تطبيق إدارة العقود"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("نظام إدارة العقود")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء قاعدة البيانات وجداول العقود إذا لم تكن موجودة
        self.setup_database()
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
        # تحميل بيانات العقود
        self.load_contracts()
    
    def setup_database(self):
        """إنشاء جداول قاعدة البيانات اللازمة إذا لم تكن موجودة"""
        try:
            conn, cursor = con_db()
            
            # إنشاء جدول العقود
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `العقود` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `التصنيف` VARCHAR(20) UNIQUE,
                    `نوع_العقد` VARCHAR(50) NOT NULL,
                    `الطرف_الأول` VARCHAR(255) NOT NULL,
                    `الطرف_الثاني` VARCHAR(255) NOT NULL,
                    `موضوع_العقد` TEXT NOT NULL,
                    `قيمة_العقد` DECIMAL(15,2) NOT NULL,
                    `تاريخ_البداية` DATE NOT NULL,
                    `تاريخ_النهاية` DATE,
                    `المدة` INT,
                    `حالة_العقد` VARCHAR(50) DEFAULT 'ساري',
                    `ملاحظات` TEXT,
                    `مسار_الملف` VARCHAR(255),
                    `كود_الحساب_الشجرة` VARCHAR(20),
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `السنة` INT,
                    INDEX `idx_كود_الحساب_الشجرة` (`كود_الحساب_الشجرة`),
                    INDEX `idx_المستخدم` (`المستخدم`),
                    INDEX `idx_السنة` (`السنة`)
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)
            
            # إنشاء جدول دفعات العقود
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `دفعات_العقود` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `معرف_العقد` INT NOT NULL,
                    `وصف_الدفعة` VARCHAR(255) NOT NULL,
                    `المبلغ` DECIMAL(15,2) NOT NULL,
                    `تاريخ_الدفعة` DATE NOT NULL,
                    `حالة_الدفعة` VARCHAR(50) DEFAULT 'غير مدفوعة',
                    `تاريخ_السداد` DATE,
                    `ملاحظات` TEXT,
                    `كود_الحساب_الشجرة` VARCHAR(20),
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `السنة` INT,
                    CONSTRAINT `fk_دفعات_العقود_معرف_العقد`
                    FOREIGN KEY (`معرف_العقد`)
                    REFERENCES `العقود`(`id`)
                    ON DELETE CASCADE,
                    INDEX `idx_كود_الحساب_الشجرة` (`كود_الحساب_الشجرة`),
                    INDEX `idx_المستخدم` (`المستخدم`),
                    INDEX `idx_السنة` (`السنة`)
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)
            
            conn.commit()
            cursor.close()
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في قاعدة البيانات", f"حدث خطأ أثناء إعداد قاعدة البيانات: {str(e)}")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        # إنشاء الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # شريط الأدوات العلوي
        toolbar_layout = QHBoxLayout()
        
        # زر إضافة عقد جديد
        self.add_contract_btn = QPushButton("إضافة عقد جديد")
        self.add_contract_btn.clicked.connect(self.add_contract)
        toolbar_layout.addWidget(self.add_contract_btn)
        
        # زر تعديل عقد
        self.edit_contract_btn = QPushButton("تعديل عقد")
        self.edit_contract_btn.clicked.connect(self.edit_contract)
        toolbar_layout.addWidget(self.edit_contract_btn)
        
        # زر حذف عقد
        self.delete_contract_btn = QPushButton("حذف عقد")
        self.delete_contract_btn.clicked.connect(self.delete_contract)
        toolbar_layout.addWidget(self.delete_contract_btn)
        
        # زر عرض دفعات العقد
        self.view_payments_btn = QPushButton("عرض دفعات العقد")
        self.view_payments_btn.clicked.connect(self.view_contract_payments)
        toolbar_layout.addWidget(self.view_payments_btn)
        
        # زر طباعة العقد
        self.print_contract_btn = QPushButton("طباعة العقد")
        self.print_contract_btn.clicked.connect(self.print_contract)
        toolbar_layout.addWidget(self.print_contract_btn)
        
        # زر تحميل ملف العقد
        self.upload_contract_btn = QPushButton("تحميل ملف العقد")
        self.upload_contract_btn.clicked.connect(self.upload_contract_file)
        toolbar_layout.addWidget(self.upload_contract_btn)
        
        # زر فتح ملف العقد
        self.open_contract_btn = QPushButton("فتح ملف العقد")
        self.open_contract_btn.clicked.connect(self.open_contract_file)
        toolbar_layout.addWidget(self.open_contract_btn)
        
        main_layout.addLayout(toolbar_layout)
        
        # حقل البحث
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        search_layout.addWidget(search_label)
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("اكتب للبحث في العقود...")
        self.search_edit.textChanged.connect(self.filter_contracts)
        search_layout.addWidget(self.search_edit)
        
        main_layout.addLayout(search_layout)
        
        # جدول العقود
        self.contracts_table = QTableWidget()
        self.contracts_table.setColumnCount(10)
        self.contracts_table.setHorizontalHeaderLabels([
            التصنيف, "نوع العقد", "الطرف الأول", "الطرف الثاني", 
            "موضوع العقد", "قيمة العقد", "تاريخ البداية", 
            "تاريخ النهاية", "المدة", "حالة العقد"
        ])
        self.contracts_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.contracts_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.contracts_table.setSelectionMode(QTableWidget.SingleSelection)
        self.contracts_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.contracts_table.doubleClicked.connect(self.edit_contract)
        main_layout.addWidget(self.contracts_table)
        
        # شريط الحالة
        self.status_label = QLabel("جاهز")
        main_layout.addWidget(self.status_label)
    
    def load_contracts(self):
        """تحميل بيانات العقود من قاعدة البيانات"""
        try:
            conn, cursor = con_db()
            
            cursor.execute("""
                SELECT التصنيف, نوع_العقد, الطرف_الأول, الطرف_الثاني, موضوع_العقد, 
                       قيمة_العقد, تاريخ_البداية, تاريخ_النهاية, المدة, حالة_العقد, id
                FROM العقود
                ORDER BY تاريخ_البداية DESC
            """)
            
            contracts = cursor.fetchall()
            
            self.contracts_table.setRowCount(len(contracts))
            
            for row, contract in enumerate(contracts):
                code, contract_type, party1, party2, subject, amount, start_date, end_date, duration, status, contract_id = contract
                
                self.contracts_table.setItem(row, 0, QTableWidgetItem(code))
                self.contracts_table.setItem(row, 1, QTableWidgetItem(contract_type))
                self.contracts_table.setItem(row, 2, QTableWidgetItem(party1))
                self.contracts_table.setItem(row, 3, QTableWidgetItem(party2))
                self.contracts_table.setItem(row, 4, QTableWidgetItem(subject))
                self.contracts_table.setItem(row, 5, QTableWidgetItem(f"{amount:,.2f}"))
                self.contracts_table.setItem(row, 6, QTableWidgetItem(start_date.strftime("%Y-%m-%d") if start_date else ""))
                self.contracts_table.setItem(row, 7, QTableWidgetItem(end_date.strftime("%Y-%m-%d") if end_date else ""))
                self.contracts_table.setItem(row, 8, QTableWidgetItem(str(duration) if duration else ""))
                self.contracts_table.setItem(row, 9, QTableWidgetItem(status))
                
                # تخزين id العقد كبيانات إضافية
                self.contracts_table.item(row, 0).setData(Qt.UserRole, contract_id)
                
                # تلوين الصفوف حسب حالة العقد
                if status == "ساري":
                    for col in range(self.contracts_table.columnCount()):
                        self.contracts_table.item(row, col).setBackground(QColor("#e3f2fd"))  # أزرق فاتح
                elif status == "منتهي":
                    for col in range(self.contracts_table.columnCount()):
                        self.contracts_table.item(row, col).setBackground(QColor("#ffebee"))  # أحمر فاتح
                elif status == "ملغي":
                    for col in range(self.contracts_table.columnCount()):
                        self.contracts_table.item(row, col).setBackground(QColor("#fafafa"))  # رمادي فاتح
            
            self.status_label.setText(f"تم تحميل {len(contracts)} عقد")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل بيانات العقود: {str(e)}")
    
    def filter_contracts(self):
        """تصفية العقود بناءً على نص البحث"""
        search_text = self.search_edit.text().lower()
        
        for row in range(self.contracts_table.rowCount()):
            match_found = False
            
            for col in range(self.contracts_table.columnCount()):
                item = self.contracts_table.item(row, col)
                if item and search_text in item.text().lower():
                    match_found = True
                    break
            
            self.contracts_table.setRowHidden(row, not match_found)
    
    def add_contract(self):
        """إضافة عقد جديد"""
        dialog = ContractDialog(self)
        if dialog.exec_():
            self.load_contracts()
    
    def edit_contract(self):
        """تعديل العقد المحدد"""
        selected_rows = self.contracts_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد عقد للتعديل")
            return
        
        selected_row = self.contracts_table.currentRow()
        contract_id = self.contracts_table.item(selected_row, 0).data(Qt.UserRole)
        
        dialog = ContractDialog(self, contract_id)
        if dialog.exec_():
            self.load_contracts()
    
    def delete_contract(self):
        """حذف العقد المحدد"""
        selected_rows = self.contracts_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد عقد للحذف")
            return
        
        selected_row = self.contracts_table.currentRow()
        contract_id = self.contracts_table.item(selected_row, 0).data(Qt.UserRole)
        contract_code = self.contracts_table.item(selected_row, 0).text()
        
        reply = QMessageBox.question(self, "تأكيد الحذف", f"هل أنت متأكد من حذف العقد {contract_code}؟",
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                conn, cursor = con_db()
                
                cursor.execute("DELETE FROM العقود WHERE id = %s", (contract_id,))
                conn.commit()
                
                cursor.close()
                conn.close()
                
                self.load_contracts()
                self.status_label.setText(f"تم حذف العقد {contract_code} بنجاح")
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ في حذف البيانات", f"حدث خطأ أثناء حذف العقد: {str(e)}")
    
    def view_contract_payments(self):
        """عرض دفعات العقد المحدد"""
        selected_rows = self.contracts_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد عقد لعرض دفعاته")
            return
        
        selected_row = self.contracts_table.currentRow()
        contract_id = self.contracts_table.item(selected_row, 0).data(Qt.UserRole)
        contract_code = self.contracts_table.item(selected_row, 0).text()
        
        dialog = ContractPaymentsDialog(self, contract_id, contract_code)
        dialog.exec_()
    
    def print_contract(self):
        """طباعة العقد المحدد"""
        selected_rows = self.contracts_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد عقد للطباعة")
            return
        
        selected_row = self.contracts_table.currentRow()
        contract_id = self.contracts_table.item(selected_row, 0).data(Qt.UserRole)
        
        # هنا يمكن إضافة كود لطباعة العقد
        QMessageBox.information(self, "طباعة العقد", "سيتم طباعة العقد المحدد")
    
    def upload_contract_file(self):
        """تحميل ملف العقد"""
        selected_rows = self.contracts_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد عقد لتحميل ملفه")
            return
        
        selected_row = self.contracts_table.currentRow()
        contract_id = self.contracts_table.item(selected_row, 0).data(Qt.UserRole)
        contract_code = self.contracts_table.item(selected_row, 0).text()
        
        file_path, _ = QFileDialog.getOpenFileName(self, "اختر ملف العقد", "", "جميع الملفات (*.*)")
        
        if file_path:
            try:
                # إنشاء مجلد للعقود إذا لم يكن موجوداً
                contracts_dir = os.path.join(os.getcwd(), "contracts_files")
                os.makedirs(contracts_dir, exist_ok=True)
                
                # نسخ الملف إلى مجلد العقود
                file_name = os.path.basename(file_path)
                new_file_path = os.path.join(contracts_dir, f"{contract_code}_{file_name}")
                
                import shutil
                shutil.copy2(file_path, new_file_path)
                
                # تحديث مسار الملف في قاعدة البيانات
                conn, cursor = con_db()
                cursor.execute("UPDATE العقود SET مسار_الملف = %s WHERE id = %s", (new_file_path, contract_id))
                conn.commit()
                cursor.close()
                conn.close()
                
                self.status_label.setText(f"تم تحميل ملف العقد {contract_code} بنجاح")
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ في تحميل الملف", f"حدث خطأ أثناء تحميل ملف العقد: {str(e)}")
    
    def open_contract_file(self):
        """فتح ملف العقد"""
        selected_rows = self.contracts_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد عقد لفتح ملفه")
            return
        
        selected_row = self.contracts_table.currentRow()
        contract_id = self.contracts_table.item(selected_row, 0).data(Qt.UserRole)
        
        try:
            conn, cursor = con_db()
            cursor.execute("SELECT مسار_الملف FROM العقود WHERE id = %s", (contract_id,))
            result = cursor.fetchone()
            cursor.close()
            conn.close()
            
            if result and result[0]:
                file_path = result[0]
                if os.path.exists(file_path):
                    import subprocess
                    if sys.platform == 'win32':
                        os.startfile(file_path)
                    elif sys.platform == 'darwin':
                        subprocess.call(['open', file_path])
                    else:
                        subprocess.call(['xdg-open', file_path])
                else:
                    QMessageBox.warning(self, "تحذير", "ملف العقد غير موجود")
            else:
                QMessageBox.warning(self, "تحذير", "لم يتم تحميل ملف للعقد")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في فتح الملف", f"حدث خطأ أثناء فتح ملف العقد: {str(e)}")


# تشغيل التطبيق
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ContractsApp()
    window.show()
    sys.exit(app.exec_())
