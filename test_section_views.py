#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for section-specific view functionality
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QSettings

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_section_view_preferences():
    """Test the section-specific view preference functionality"""
    
    # Initialize QApplication for QSettings
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # Initialize QSettings
    settings = QSettings("ProjectManager", "V2.0")
    
    print("🧪 Testing Section-Specific View Preferences")
    print("=" * 50)
    
    # Test 1: Default view preferences
    print("\n📋 Test 1: Default View Preferences")
    section_default_views = {
        "الحسابات": "table",  # الحسابات افتراضياً عرض جدول
        "المشاريع": "cards",
        "المقاولات": "cards", 
        "العملاء": "cards",
        "الموظفين": "cards",
        "العقارات": "cards",
        "التدريب": "cards",
        "التقارير": "cards"
    }
    
    for section_name, expected_default in section_default_views.items():
        # Get saved preference or use default
        saved_view = settings.value(f"section_view_{section_name}", expected_default, type=str)
        
        if saved_view == expected_default:
            print(f"✅ {section_name}: {saved_view} (matches default)")
        else:
            print(f"🔄 {section_name}: {saved_view} (custom preference)")
    
    # Test 2: Setting and retrieving preferences
    print("\n📋 Test 2: Setting and Retrieving Preferences")
    
    # Test setting preferences
    test_preferences = {
        "المشاريع": "table",
        "الحسابات": "cards",
        "العملاء": "table"
    }
    
    for section_name, new_view in test_preferences.items():
        # Set preference
        settings.setValue(f"section_view_{section_name}", new_view)
        settings.sync()
        
        # Retrieve and verify
        retrieved_view = settings.value(f"section_view_{section_name}", "cards", type=str)
        
        if retrieved_view == new_view:
            print(f"✅ {section_name}: Set to {new_view}, retrieved {retrieved_view}")
        else:
            print(f"❌ {section_name}: Set to {new_view}, but retrieved {retrieved_view}")
    
    # Test 3: Verify persistence
    print("\n📋 Test 3: Verify Persistence")
    settings.sync()  # Force save
    
    # Create new settings instance to simulate app restart
    new_settings = QSettings("ProjectManager", "V2.0")
    
    for section_name, expected_view in test_preferences.items():
        retrieved_view = new_settings.value(f"section_view_{section_name}", "cards", type=str)
        
        if retrieved_view == expected_view:
            print(f"✅ {section_name}: Persisted correctly as {retrieved_view}")
        else:
            print(f"❌ {section_name}: Expected {expected_view}, but got {retrieved_view}")
    
    # Test 4: Reset to defaults
    print("\n📋 Test 4: Reset to Defaults")
    
    for section_name, default_view in section_default_views.items():
        settings.setValue(f"section_view_{section_name}", default_view)
    
    settings.sync()
    print("✅ All sections reset to default preferences")
    
    # Final verification
    print("\n📋 Final Verification")
    for section_name, expected_default in section_default_views.items():
        saved_view = settings.value(f"section_view_{section_name}", expected_default, type=str)
        
        if saved_view == expected_default:
            print(f"✅ {section_name}: {saved_view}")
        else:
            print(f"❌ {section_name}: Expected {expected_default}, got {saved_view}")
    
    print("\n" + "=" * 50)
    print("🎉 Section View Preferences Test Complete!")
    
    return True

def test_view_functionality_structure():
    """Test the structure and availability of view functions"""
    
    print("\n🔧 Testing View Functionality Structure")
    print("=" * 50)
    
    try:
        # Try to import the main window
        from منظومة_المهندس import MainWindow
        
        print("✅ MainWindow imported successfully")
        
        # Check if the required methods exist
        required_methods = [
            'get_section_view_preference',
            'set_section_view_preference', 
            'get_all_section_view_preferences',
            'toggle_view',
            'toggle_section_view_and_update_button',
            'update_section_toggle_button',
            'apply_view_to_section'
        ]
        
        for method_name in required_methods:
            if hasattr(MainWindow, method_name):
                print(f"✅ Method {method_name} exists")
            else:
                print(f"❌ Method {method_name} missing")
        
        # Check if section_default_views exists
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            
        # Create a temporary instance to check attributes
        try:
            window = MainWindow()
            
            if hasattr(window, 'section_default_views'):
                print("✅ section_default_views attribute exists")
                print(f"   Default views: {window.section_default_views}")
            else:
                print("❌ section_default_views attribute missing")
                
            window.close()
            
        except Exception as e:
            print(f"⚠️  Could not create MainWindow instance: {e}")
            print("   This is expected if database connection is not available")
        
    except ImportError as e:
        print(f"❌ Could not import MainWindow: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Structure Test Complete!")
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Section-Specific View Tests")
    print("=" * 60)
    
    # Run tests
    test1_result = test_section_view_preferences()
    test2_result = test_view_functionality_structure()
    
    if test1_result and test2_result:
        print("\n🎉 All tests passed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
