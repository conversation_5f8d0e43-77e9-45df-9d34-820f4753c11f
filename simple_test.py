#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط للتحقق من وجود الدوال
"""

import inspect

def test_methods():
    """اختبار وجود الدوال"""
    try:
        # قراءة محتوى الملف
        with open('مراحل_المشروع.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # قائمة الدوال المطلوبة
        required_methods = [
            'def insert_contractor_balance',
            'def insert_all_contractor_balances', 
            'def filter_contractors_by_name',
            'def insert_worker_balance',
            'def insert_all_worker_balances',
            'def filter_workers_by_name',
            'def insert_supplier_balance',
            'def insert_all_supplier_balances',
            'def filter_suppliers_by_name'
        ]
        
        print("🔍 فحص وجود الدوال في الملف...")
        
        missing_methods = []
        existing_methods = []
        
        for method in required_methods:
            if method in content:
                existing_methods.append(method)
                print(f"✅ {method}")
            else:
                missing_methods.append(method)
                print(f"❌ {method}")
        
        print(f"\n📊 النتائج:")
        print(f"✅ الدوال الموجودة: {len(existing_methods)}")
        print(f"❌ الدوال المفقودة: {len(missing_methods)}")
        
        if missing_methods:
            print(f"\n❌ الدوال المفقودة:")
            for method in missing_methods:
                print(f"   - {method}")
            return False
        else:
            print(f"\n🎉 جميع الدوال المطلوبة موجودة!")
            return True
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    print("🧪 بدء اختبار وجود الدوال...")
    success = test_methods()
    
    if success:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النظام جاهز للاستخدام من قسم المقاولات")
    else:
        print("\n💥 فشل في بعض الاختبارات")
