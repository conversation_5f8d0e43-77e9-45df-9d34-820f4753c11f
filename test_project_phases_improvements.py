#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار التحسينات على نافذة مراحل المشروع
==============================================

يختبر هذا الملف التحسينات التالية:
1. إضافة عمود الرقم التلقائي
2. إضافة عمود اسم المرحلة
3. تحسين تخطيط أزرار التابات
4. تطبيق الألوان المطلوبة للأزرار
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from مراحل_المشروع import ProjectPhasesWindow
    print("✅ تم استيراد نافذة مراحل المشروع بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد نافذة مراحل المشروع: {e}")
    sys.exit(1)

class TestMainWindow(QMainWindow):
    """نافذة اختبار رئيسية"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار تحسينات نافذة مراحل المشروع")
        self.setGeometry(100, 100, 400, 300)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد الواجهة
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # زر اختبار مشروع تصميم
        design_btn = QPushButton("اختبار مشروع تصميم")
        design_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5dade2;
            }
        """)
        design_btn.clicked.connect(self.test_design_project)
        layout.addWidget(design_btn)
        
        # زر اختبار مشروع مقاولات
        contracting_btn = QPushButton("اختبار مشروع مقاولات")
        contracting_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)
        contracting_btn.clicked.connect(self.test_contracting_project)
        layout.addWidget(contracting_btn)
        
        # زر اختبار بيانات وهمية
        dummy_data_btn = QPushButton("اختبار بيانات وهمية")
        dummy_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        dummy_data_btn.clicked.connect(self.test_with_dummy_data)
        layout.addWidget(dummy_data_btn)
        
    def test_design_project(self):
        """اختبار مشروع تصميم"""
        project_data = {
            'id': 1,
            'معرف_العميل': 1,
            'اسم_المشروع': 'مشروع تصميم معماري تجريبي',
            'المبلغ': 50000,
            'المدفوع': 20000,
            'الحالة': 'قيد الإنجاز'
        }
        
        try:
            self.phases_window = ProjectPhasesWindow(
                parent=self,
                project_data=project_data,
                project_type="تصميم"
            )
            self.phases_window.show()
            print("✅ تم فتح نافذة مشروع التصميم بنجاح")
        except Exception as e:
            print(f"❌ خطأ في فتح نافذة مشروع التصميم: {e}")
    
    def test_contracting_project(self):
        """اختبار مشروع مقاولات"""
        project_data = {
            'id': 2,
            'معرف_العميل': 2,
            'اسم_المشروع': 'مشروع مقاولات تجريبي',
            'المبلغ': 100000,
            'المدفوع': 30000,
            'الحالة': 'قيد الإنجاز'
        }
        
        try:
            self.phases_window = ProjectPhasesWindow(
                parent=self,
                project_data=project_data,
                project_type="مقاولات"
            )
            self.phases_window.show()
            print("✅ تم فتح نافذة مشروع المقاولات بنجاح")
        except Exception as e:
            print(f"❌ خطأ في فتح نافذة مشروع المقاولات: {e}")
    
    def test_with_dummy_data(self):
        """اختبار مع بيانات وهمية"""
        project_data = {
            'id': 999,  # معرف وهمي
            'معرف_العميل': 999,
            'اسم_المشروع': 'مشروع اختبار التحسينات',
            'المبلغ': 75000,
            'المدفوع': 25000,
            'الحالة': 'قيد الإنجاز'
        }
        
        try:
            self.phases_window = ProjectPhasesWindow(
                parent=self,
                project_data=project_data,
                project_type="تصميم"
            )
            
            # محاولة إضافة بيانات وهمية للجدول لاختبار الرقم التلقائي
            self.add_dummy_phases_data()
            
            self.phases_window.show()
            print("✅ تم فتح نافذة الاختبار مع البيانات الوهمية بنجاح")
        except Exception as e:
            print(f"❌ خطأ في فتح نافذة الاختبار: {e}")
    
    def add_dummy_phases_data(self):
        """إضافة بيانات وهمية لاختبار الجدول"""
        try:
            # إضافة بيانات وهمية للجدول
            table = self.phases_window.phases_table
            table.setRowCount(3)
            
            # البيانات الوهمية
            dummy_data = [
                ["1", "1", "مرحلة التصميم الأولي", "تصميم المخططات الأولية", "متر مربع", "100", "50", "5000", "غير مدرج", "مرحلة أساسية"],
                ["2", "2", "مرحلة التصميم التفصيلي", "تصميم المخططات التفصيلية", "متر مربع", "100", "75", "7500", "تم الإدراج", "مرحلة متقدمة"],
                ["3", "3", "مرحلة المراجعة النهائية", "مراجعة وتدقيق المخططات", "مشروع", "1", "2500", "2500", "غير مدرج", "مرحلة نهائية"]
            ]
            
            for row_idx, row_data in enumerate(dummy_data):
                for col_idx, data in enumerate(row_data):
                    from PySide6.QtWidgets import QTableWidgetItem
                    item = QTableWidgetItem(str(data))
                    if col_idx == 1:  # عمود الرقم
                        item.setTextAlignment(Qt.AlignCenter)
                    table.setItem(row_idx, col_idx, item)
            
            print("✅ تم إضافة البيانات الوهمية للجدول")
            
        except Exception as e:
            print(f"❌ خطأ في إضافة البيانات الوهمية: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار تحسينات نافذة مراحل المشروع...")
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    try:
        from PySide6.QtGui import QFont
        font = QFont("Janna LT", 12)
        app.setFont(font)
        print("✅ تم تطبيق الخط العربي")
    except:
        print("⚠️ تعذر تطبيق الخط العربي")
    
    # إنشاء النافذة الرئيسية
    window = TestMainWindow()
    window.show()
    
    print("✅ تم تشغيل تطبيق الاختبار بنجاح")
    print("\nالتحسينات المطبقة:")
    print("1. ✅ إضافة عمود الرقم التلقائي")
    print("2. ✅ إضافة عمود اسم المرحلة")
    print("3. ✅ تحسين تخطيط أزرار التابات")
    print("4. ✅ تطبيق الألوان المطلوبة (أخضر، أزرق، أحمر)")
    print("5. ✅ تطبيق التحسينات على جميع الجداول")
    print("\nالتابات المحدثة:")
    print("   - ✅ تاب مراحل المشروع")
    print("   - ✅ تاب مهام المهندسين")
    print("   - ✅ تاب الجدول الزمني")
    print("   - ✅ تاب الملفات والمرفقات")
    print("   - ✅ تاب المصروفات (للمقاولات)")
    print("   - ✅ تاب العهد المالية (للمقاولات)")
    print("   - ✅ تاب دفعات العهد (للمقاولات)")
    print("   - ✅ تاب المقاولين (للمقاولات)")
    print("   - ✅ تاب العمال (للمقاولات)")
    print("   - ✅ تاب الموردين (للمقاولات)")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
