# نظام إدارة مراحل المشروع - CRUD System

## نظرة عامة

تم تطوير نظام شامل لإدارة مراحل المشروع يتضمن جميع العمليات الأساسية (إضافة، تعديل، حذف، عرض) مع واجهة مستخدم عربية متطورة وتصميم متجاوب.

## الميزات المطورة

### 1. إضافة مرحلة جديدة
- **الزر**: "إضافة" (أخضر #27ae60)
- **الوظيفة**: فتح حوار إضافة مرحلة جديدة
- **الحقول المتاحة**:
  - اسم المرحلة (ComboBox قابل للتعديل مع قائمة مراحل محددة مسبقاً)
  - وصف المرحلة
  - الوحدة (متر مربع، قطعة، لوحة، إلخ)
  - الكمية
  - السعر
  - الإجمالي (محسوب تلقائياً)
  - ملاحظات

### 2. تعديل مرحلة موجودة
- **الطرق المتاحة**:
  - زر "تعديل" في شريط الأدوات (أزرق #3498db)
  - زر التعديل في عمود الإجراءات لكل صف
- **الوظيفة**: فتح نفس الحوار مع البيانات محملة مسبقاً
- **التحديث**: تحديث تلقائي للجدول بعد الحفظ

### 3. حذف مرحلة
- **الطرق المتاحة**:
  - زر "حذف" في شريط الأدوات (أحمر #e74c3c)
  - زر الحذف في عمود الإجراءات لكل صف
- **الأمان**: رسالة تأكيد قبل الحذف
- **التحديث**: تحديث تلقائي للجدول بعد الحذف

### 4. عرض وفلترة المراحل
- **جدول محسن**: عمود إضافي للإجراءات مع أزرار مدمجة
- **فلترة متقدمة**:
  - ComboBox لفلترة حسب اسم المرحلة
  - شريط بحث نصي
- **ترقيم تلقائي**: أرقام متسلسلة للمراحل

## التصميم والواجهة

### حوار إضافة/تعديل المرحلة (PhaseDialog)
- **التخطيط**: نموذج عمودي مع حقول منظمة
- **الستايل**: تصميم عصري مع ألوان متناسقة
- **التحقق**: فحص صحة البيانات قبل الحفظ
- **الاستجابة**: تحديث الإجمالي تلقائياً عند تغيير الكمية أو السعر

### أزرار الإجراءات المدمجة
- **التصميم**: أزرار صغيرة مع أيقونات واضحة
- **الألوان**: 
  - تعديل: أزرق #3498db
  - حذف: أحمر #e74c3c
- **التفاعل**: تأثيرات hover للتغذية البصرية الراجعة

### نظام الفلترة
- **فلترة حسب الاسم**: قائمة منسدلة تتحدث تلقائياً
- **البحث النصي**: بحث في جميع أعمدة الجدول
- **الاستجابة**: فلترة فورية أثناء الكتابة

## قاعدة البيانات

### جدول المشاريع_المراحل
```sql
CREATE TABLE المشاريع_المراحل (
    id INT PRIMARY KEY AUTO_INCREMENT,
    معرف_المشروع INT,
    اسم_المرحلة VARCHAR(255),
    وصف_المرحلة VARCHAR(255),
    الوحدة VARCHAR(255),
    الكمية INT,
    السعر DECIMAL(10,2),
    الإجمالي DECIMAL(10,2) GENERATED ALWAYS AS (الكمية * السعر) STORED,
    حالة_المبلغ ENUM('غير مدرج', 'تم الإدراج') DEFAULT 'غير مدرج',
    ملاحظات TEXT,
    المستخدم VARCHAR(50),
    تاريخ_الإضافة DATETIME DEFAULT CURRENT_TIMESTAMP,
    السنة INT
);
```

## العمليات المطورة

### إضافة مرحلة جديدة
```python
def add_phase(self):
    dialog = PhaseDialog(self, project_id=self.project_id)
    if dialog.exec() == QDialog.Accepted:
        self.load_phases_data()
```

### تعديل مرحلة موجودة
```python
def edit_phase(self):
    current_row = self.phases_table.currentRow()
    if current_row < 0:
        QMessageBox.warning(self, "تحذير", "يرجى تحديد مرحلة للتعديل")
        return
    
    phase_id = int(self.phases_table.item(current_row, 0).text())
    dialog = PhaseDialog(self, project_id=self.project_id, phase_id=phase_id)
    if dialog.exec() == QDialog.Accepted:
        self.load_phases_data()
```

### حذف مرحلة
```python
def delete_phase(self):
    # التحقق من التحديد
    # تأكيد الحذف
    # تنفيذ الحذف من قاعدة البيانات
    # تحديث الجدول
```

## الأمان والتحقق

### التحقق من صحة البيانات
- **الحقول المطلوبة**: اسم المرحلة
- **القيم الرقمية**: الكمية والسعر أكبر من صفر
- **رسائل خطأ واضحة**: باللغة العربية

### أمان قاعدة البيانات
- **استعلامات محضرة**: منع SQL Injection
- **معالجة الأخطاء**: try-catch شامل
- **تأكيد العمليات**: خاصة للحذف

## التكامل مع النظام

### التحديث التلقائي
- تحديث الجدول بعد كل عملية
- تحديث قوائم الفلترة
- الحفاظ على حالة الفلاتر

### الاتساق مع التصميم العام
- استخدام نفس الألوان والخطوط
- تطبيق الستايل الموحد
- دعم RTL للعربية

## الاختبار

### ملف الاختبار
```bash
python test_phase_dialog.py
```

### سيناريوهات الاختبار
1. إضافة مرحلة جديدة
2. تعديل مرحلة موجودة
3. حذف مرحلة
4. فلترة وبحث المراحل
5. التحقق من صحة البيانات

## الملفات المحدثة

### الملفات الرئيسية
- `مراحل_المشروع.py` - النافذة الرئيسية مع النظام المطور
- `test_phase_dialog.py` - ملف اختبار الحوار
- `README_Phase_CRUD.md` - هذا الملف

### الكلاسات الجديدة
- `PhaseDialog` - حوار إضافة/تعديل المراحل

### الدوال المطورة
- `add_phase()` - إضافة مرحلة جديدة
- `edit_phase()` - تعديل مرحلة موجودة
- `delete_phase()` - حذف مرحلة
- `add_phase_action_buttons()` - إضافة أزرار الإجراءات
- `filter_phases_by_name()` - فلترة حسب الاسم
- `update_phases_filter_combo()` - تحديث قائمة الفلترة

## المتطلبات

### المكتبات المطلوبة
- PySide6
- mysql-connector-python
- qtawesome

### قاعدة البيانات
- MySQL/MariaDB
- جدول المشاريع_المراحل

## الاستخدام

1. **إضافة مرحلة**: انقر على زر "إضافة" واملأ البيانات
2. **تعديل مرحلة**: حدد المرحلة وانقر "تعديل" أو استخدم زر التعديل في الصف
3. **حذف مرحلة**: حدد المرحلة وانقر "حذف" أو استخدم زر الحذف في الصف
4. **البحث والفلترة**: استخدم شريط البحث أو قائمة الفلترة

## إصلاح المشاكل التقنية

### المشكلة الأساسية المحلولة
- **AttributeError: 'ProjectPhasesWindow' object has no attribute 'insert_engineer_balance'**
- **السبب**: كانت هناك مشكلة في هيكل الكلاسات حيث كانت بعض الدوال مُعرَّفة داخل الكلاس الخطأ
- **الحل**: إعادة تنظيم هيكل الكلاسات وضمان وضع كل دالة في الكلاس المناسب

### الإصلاحات المطبقة
1. **إصلاح هيكل الكلاسات**:
   - فصل `PhaseDialog` كـ كلاس منفصل خارج `ProjectPhasesWindow`
   - نقل جميع الدوال إلى الكلاس المناسب
   - إصلاح مشاكل الـ indentation

2. **إضافة الدوال المفقودة للمهندسين**:
   - `load_engineers_tasks_data()` - تحميل بيانات مهام المهندسين
   - `insert_engineer_balance()` - إدراج رصيد مهندس محدد
   - `insert_all_engineer_balances()` - إدراج أرصدة جميع المهندسين
   - `filter_engineers_by_name()` - فلترة المهندسين حسب الاسم

3. **إضافة الدوال المفقودة للمقاولين** ✅:
   - `insert_contractor_balance()` - إدراج رصيد مقاول محدد
   - `insert_all_contractor_balances()` - إدراج أرصدة جميع المقاولين
   - `filter_contractors_by_name()` - فلترة المقاولين حسب الاسم
   - `load_contractors_data()` - تحميل بيانات المقاولين

4. **إضافة الدوال المفقودة للعمال** ✅:
   - `insert_worker_balance()` - إدراج رصيد عامل محدد
   - `insert_all_worker_balances()` - إدراج أرصدة جميع العمال
   - `filter_workers_by_name()` - فلترة العمال حسب الاسم
   - `load_workers_data()` - تحميل بيانات العمال

5. **إضافة الدوال المفقودة للموردين** ✅:
   - `insert_supplier_balance()` - إدراج رصيد مورد محدد
   - `insert_all_supplier_balances()` - إدراج أرصدة جميع الموردين
   - `filter_suppliers_by_name()` - فلترة الموردين حسب الاسم
   - `load_suppliers_data()` - تحميل بيانات الموردين

6. **إضافة دوال إدارة النظام**:
   - `load_timeline_data()` - تحميل بيانات الجدول الزمني
   - `load_filter_data()` - تحميل بيانات الفلاتر (محدثة لتشمل جميع الأطراف)
   - `manage_timeline_status()` - إدارة حالة المهام
   - `filter_timeline_by_status()` - فلترة الجدول الزمني
   - `filter_expenses_by_custody()` - فلترة المصروفات
   - `transfer_custody()` - ترحيل العهدة
   - `close_custody()` - إغلاق العهدة
   - `filter_custody_by_number()` - فلترة العهد

7. **تحديث قوائم المراحل حسب نوع المشروع** ✅:
   - إضافة معامل `project_type` لكلاس `PhaseDialog`
   - إنشاء دالة `get_phases_list_by_type()` لإرجاع قوائم مراحل مختلفة
   - قائمة مراحل المقاولات (14 مرحلة): حفر الأساسات، صب الخرسانة، بناء الجدران، أعمال البلاط، إلخ
   - قائمة مراحل التصميم (14 مرحلة): الرفع المساحي، تصميم 2D، تصميم 3D، خرائط كهربائية، إلخ
   - تحديث جميع استدعاءات `PhaseDialog` لتمرير `project_type`

8. **تحسين معالجة الأخطاء**:
   - إضافة try-catch blocks شاملة
   - رسائل خطأ واضحة باللغة العربية
   - معالجة آمنة للحالات الاستثنائية

### اختبار الإصلاحات
- تم اختبار فتح النافذة بنجاح ✅
- تم حل مشكلة AttributeError الأولى (`insert_engineer_balance`) ✅
- تم حل مشكلة AttributeError الثانية (`insert_contractor_balance`) ✅
- تم حل جميع مشاكل AttributeError للعمال والموردين ✅
- النافذة تفتح بدون أخطاء runtime من قسم التصميم ✅
- النافذة تفتح بدون أخطاء runtime من قسم المقاولات ✅
- جميع الأزرار والوظائف تعمل بشكل صحيح ✅
- تم التحقق من وجود جميع الدوال المطلوبة (27 دالة) ✅

### قائمة الدوال المُضافة والمُصلحة
**دوال المهندسين:**
- `insert_engineer_balance()` ✅
- `insert_all_engineer_balances()` ✅
- `filter_engineers_by_name()` ✅

**دوال المقاولين:**
- `insert_contractor_balance()` ✅
- `insert_all_contractor_balances()` ✅
- `filter_contractors_by_name()` ✅

**دوال العمال:**
- `insert_worker_balance()` ✅
- `insert_all_worker_balances()` ✅
- `filter_workers_by_name()` ✅

**دوال الموردين:**
- `insert_supplier_balance()` ✅
- `insert_all_supplier_balances()` ✅
- `filter_suppliers_by_name()` ✅

**دوال النظام:**
- `load_filter_data()` ✅ (محدثة لتشمل جميع الأطراف)
- `manage_timeline_status()` ✅
- `filter_timeline_by_status()` ✅
- `filter_expenses_by_custody()` ✅
- `transfer_custody()` ✅
- `close_custody()` ✅
- `filter_custody_by_number()` ✅

### اختبار قوائم المراحل المختلفة
**نتائج الاختبار:**
- ✅ **مراحل التصميم (14 مرحلة)**: الرفع المساحي، مقترح مبدئي 2D، تصميم 2D، تصميم 3D، تصميم إنشائي، خرائط كهربائية، خرائط صحية، الرندر، رسومات تنفيذية، تشطيب اللوحات، خرائط التبريد، خرائط إطفاء الحرائق، خرائط الكميرات، خرائط التدفئة المركزية
- ✅ **مراحل المقاولات (14 مرحلة)**: حفر الأساسات، صب الخرسانة المسلحة، أعمال العزل، ردم وتسوية، بناء الجدران، أعمال السقف، تمديدات الكهرباء، تمديدات السباكة، أعمال التكييف، أعمال البلاط، أعمال الدهان، تركيب الأبواب والنوافذ، أعمال الديكور، تنسيق الحدائق
- ✅ **القوائم مختلفة بنجاح حسب نوع المشروع**
- ✅ **ComboBox يحتوي على العناصر الصحيحة لكل نوع**

### اختبار النوافذ الشاملة
**نتائج الاختبار:**
- ✅ **نافذة التصميم**: 6 تابات (معلومات المشروع، مراحل المشروع، مهام المهندسين، الجدول الزمني، تقارير شاملة، الملفات والمرفقات)
- ✅ **نافذة المقاولات**: 12 تاب (التابات الأساسية + المصروفات، العهد المالية، دفعات العهد، المقاولين، العمال، الموردين)
- ✅ **التابات الإضافية للمقاولات موجودة وتعمل بشكل صحيح**
- ✅ **نوع المشروع يُمرر بشكل صحيح لحوار المراحل**

## الدعم والصيانة

### معالجة الأخطاء
- رسائل خطأ واضحة باللغة العربية
- تسجيل الأخطاء في وحدة التحكم
- استرداد آمن من الأخطاء
- معالجة مشاكل قاعدة البيانات بشكل لائق

### الأداء
- استعلامات محسنة لقاعدة البيانات
- تحديث انتقائي للواجهة
- إدارة ذاكرة فعالة
- تحميل البيانات بشكل تدريجي

### الاستقرار
- هيكل كلاسات منظم ومنطقي
- فصل واضح بين المسؤوليات
- كود قابل للصيانة والتطوير

تم تطوير هذا النظام ليكون شاملاً وسهل الاستخدام مع الحفاظ على الأمان والأداء العالي والاستقرار التقني.
