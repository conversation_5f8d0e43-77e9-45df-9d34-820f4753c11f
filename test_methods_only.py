#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار وجود الدوال المطلوبة فقط بدون فتح واجهة المستخدم
"""

import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_methods_exist():
    """اختبار وجود الدوال المطلوبة"""
    try:
        # استيراد الكلاس
        from مراحل_المشروع import ProjectPhasesWindow
        
        print("✅ تم استيراد ProjectPhasesWindow بنجاح")
        
        # قائمة الدوال المطلوبة
        required_methods = [
            'insert_contractor_balance',
            'insert_all_contractor_balances', 
            'filter_contractors_by_name',
            'insert_worker_balance',
            'insert_all_worker_balances',
            'filter_workers_by_name',
            'insert_supplier_balance',
            'insert_all_supplier_balances',
            'filter_suppliers_by_name',
            'manage_timeline_status',
            'filter_timeline_by_status',
            'filter_expenses_by_custody',
            'transfer_custody',
            'close_custody',
            'filter_custody_by_number',
            'load_filter_data',
            'insert_engineer_balance',
            'insert_all_engineer_balances',
            'filter_engineers_by_name'
        ]
        
        print(f"\n🔍 فحص {len(required_methods)} دالة...")
        
        missing_methods = []
        existing_methods = []
        
        for method_name in required_methods:
            if hasattr(ProjectPhasesWindow, method_name):
                existing_methods.append(method_name)
                print(f"✅ {method_name}")
            else:
                missing_methods.append(method_name)
                print(f"❌ {method_name}")
        
        print(f"\n📊 النتائج:")
        print(f"✅ الدوال الموجودة: {len(existing_methods)}")
        print(f"❌ الدوال المفقودة: {len(missing_methods)}")
        
        if missing_methods:
            print(f"\n❌ الدوال المفقودة:")
            for method in missing_methods:
                print(f"   - {method}")
            return False
        else:
            print(f"\n🎉 جميع الدوال المطلوبة موجودة!")
            return True
            
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_phase_dialog_import():
    """اختبار استيراد PhaseDialog"""
    try:
        from مراحل_المشروع import PhaseDialog
        print("✅ تم استيراد PhaseDialog بنجاح")
        return True
    except ImportError as e:
        print(f"❌ خطأ في استيراد PhaseDialog: {e}")
        return False

if __name__ == "__main__":
    print("🧪 بدء اختبار وجود الدوال المطلوبة...")
    
    # اختبار استيراد PhaseDialog
    dialog_success = test_phase_dialog_import()
    
    # اختبار وجود الدوال
    methods_success = test_methods_exist()
    
    if methods_success and dialog_success:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النظام جاهز للاستخدام من قسم المقاولات")
        sys.exit(0)
    else:
        print("\n💥 فشل في بعض الاختبارات")
        sys.exit(1)
