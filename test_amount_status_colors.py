#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تنسيق ألوان حالة المبلغ في جداول مراحل المشروع
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTableWidget, QTableWidgetItem, QLabel
from PySide6.QtGui import QColor
from PySide6.QtCore import Qt

class TestAmountStatusColors(QMainWindow):
    """نافذة اختبار ألوان حالة المبلغ"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار ألوان حالة المبلغ")
        self.setGeometry(100, 100, 800, 600)
        
        # إعداد الواجهة
        self.setup_ui()
        
        # إضافة بيانات تجريبية
        self.add_test_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # عنوان
        title = QLabel("اختبار ألوان حالة المبلغ")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # جدول المراحل
        phases_label = QLabel("جدول مراحل المشروع:")
        phases_label.setStyleSheet("font-size: 14px; font-weight: bold; margin: 5px;")
        layout.addWidget(phases_label)
        
        self.phases_table = QTableWidget()
        self.phases_table.setColumnCount(4)
        self.phases_table.setHorizontalHeaderLabels(["الرقم", "اسم المرحلة", "المبلغ", "حالة المبلغ"])
        layout.addWidget(self.phases_table)
        
        # جدول مهام المهندسين
        engineers_label = QLabel("جدول مهام المهندسين:")
        engineers_label.setStyleSheet("font-size: 14px; font-weight: bold; margin: 5px;")
        layout.addWidget(engineers_label)
        
        self.engineers_table = QTableWidget()
        self.engineers_table.setColumnCount(4)
        self.engineers_table.setHorizontalHeaderLabels(["الرقم", "المهندس", "المبلغ", "حالة المبلغ"])
        layout.addWidget(self.engineers_table)
    
    def apply_amount_status_color(self, item, status):
        """تطبيق ألوان مخصصة لحالة المبلغ"""
        if status == "غير مدرج":
            item.setForeground(QColor(231, 76, 60))  # أحمر
        elif status == "تم الإدراج":
            item.setForeground(QColor(46, 125, 50))  # أخضر
        # الحفاظ على اللون الافتراضي للحالات الأخرى
    
    def add_test_data(self):
        """إضافة بيانات تجريبية للاختبار"""
        # بيانات جدول المراحل
        phases_data = [
            ["1", "مرحلة التصميم", "5000", "تم الإدراج"],
            ["2", "مرحلة التنفيذ", "10000", "غير مدرج"],
            ["3", "مرحلة الإشراف", "3000", "تم الإدراج"],
            ["4", "مرحلة التسليم", "2000", "غير مدرج"],
        ]
        
        self.phases_table.setRowCount(len(phases_data))
        for row_idx, row_data in enumerate(phases_data):
            for col_idx, data in enumerate(row_data):
                item = QTableWidgetItem(str(data))
                item.setTextAlignment(Qt.AlignCenter)
                
                # تطبيق الألوان على عمود حالة المبلغ
                if col_idx == 3:  # عمود حالة المبلغ
                    self.apply_amount_status_color(item, data)
                
                self.phases_table.setItem(row_idx, col_idx, item)
        
        # بيانات جدول مهام المهندسين
        engineers_data = [
            ["1", "أحمد محمد", "2500", "تم الإدراج"],
            ["2", "فاطمة علي", "3000", "غير مدرج"],
            ["3", "محمد حسن", "1500", "تم الإدراج"],
            ["4", "سارة أحمد", "2000", "غير مدرج"],
        ]
        
        self.engineers_table.setRowCount(len(engineers_data))
        for row_idx, row_data in enumerate(engineers_data):
            for col_idx, data in enumerate(row_data):
                item = QTableWidgetItem(str(data))
                item.setTextAlignment(Qt.AlignCenter)
                
                # تطبيق الألوان على عمود حالة المبلغ
                if col_idx == 3:  # عمود حالة المبلغ
                    self.apply_amount_status_color(item, data)
                
                self.engineers_table.setItem(row_idx, col_idx, item)

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تطبيق ستايل عربي أساسي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = TestAmountStatusColors()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
