#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة إدارة القيود المحاسبية
نظام شامل لإدارة وعرض القيود المحاسبية مع إمكانيات البحث والتصفية والطباعة
"""

import sys
import os
import json
import shutil
import tempfile
import webbrowser
from datetime import datetime, date
from decimal import Decimal
import mysql.connector
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QDateEdit, QDoubleSpinBox, QTextEdit, QPushButton,
    QTableWidget, QTableWidgetItem, QComboBox, QMessageBox, QHeaderView,
    QTabWidget, QWidget, QSplitter, QFrame, QGroupBox, QGridLayout,
    QSpacerItem, QSizePolicy, QCheckBox, QSpinBox, QScrollArea, QFileDialog,
    QListWidget, QListWidgetItem, QProgressDialog, QTextBrowser, QTreeWidget,
    QTreeWidgetItem
)
from PySide6.QtCore import Qt, QDate, Signal, QTimer, QUrl, QThread
from PySide6.QtGui import QFont, QIcon, QPixmap, QPalette, QColor, QDesktopServices, QPainter
from PySide6.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog

# استيراد الوحدات المطلوبة
try:
    from for_all import *
    from ستايل import *
    from ui_boton import *
    from journal_entries import JournalEntryManager, AutoJournalEntries
    from account_selector import AccountSelectorDialog
except ImportError as e:
    print(f"تحذير: فشل في استيراد بعض الوحدات: {e}")

# دالة الاتصال بقاعدة البيانات
def con_db():
    """دالة الاتصال بقاعدة البيانات"""
    try:
        import mysql.connector
        from متغيرات import host, user, password

        db_name = "project_manager_V2"
        conn = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=db_name
        )
        cursor = conn.cursor()
        return conn, cursor
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None, None

def ensure_accounting_tables():
    """التأكد من وجود الجداول المحاسبية المطلوبة"""
    try:
        conn, cursor = con_db()
        if not conn:
            return False

        # إنشاء جدول القيود المحاسبية
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS القيود_المحاسبية (
                id INT AUTO_INCREMENT PRIMARY KEY,
                رقم_القيد VARCHAR(50) UNIQUE NOT NULL,
                تاريخ_القيد DATE NOT NULL,
                وصف_القيد TEXT NOT NULL,
                إجمالي_مدين DECIMAL(15,2) DEFAULT 0,
                إجمالي_دائن DECIMAL(15,2) DEFAULT 0,
                حالة_القيد ENUM('مسودة', 'معتمد', 'ملغي') DEFAULT 'مسودة',
                نوع_القيد VARCHAR(50) DEFAULT 'عام',
                المرجع_الخارجي VARCHAR(100),
                مركز_التكلفة VARCHAR(50),
                ملاحظات TEXT,
                المستخدم VARCHAR(50),
                السنة INT,
                تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                تاريخ_التعديل TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # إنشاء جدول تفاصيل القيود المحاسبية
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS تفاصيل_القيود_المحاسبية (
                id INT AUTO_INCREMENT PRIMARY KEY,
                معرف_القيد INT NOT NULL,
                رقم_القيد VARCHAR(50) NOT NULL,
                كود_الحساب VARCHAR(20) NOT NULL,
                اسم_الحساب VARCHAR(200),
                وصف_التفصيل TEXT,
                مدين DECIMAL(15,2) DEFAULT 0,
                دائن DECIMAL(15,2) DEFAULT 0,
                مركز_التكلفة VARCHAR(50),
                ملاحظات TEXT,
                ترتيب_السطر INT DEFAULT 1,
                FOREIGN KEY (معرف_القيد) REFERENCES القيود_المحاسبية(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # إنشاء جدول حركات الحسابات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS حركات_الحسابات (
                id INT AUTO_INCREMENT PRIMARY KEY,
                رقم_القيد VARCHAR(50) NOT NULL,
                تاريخ_القيد DATE NOT NULL,
                كود_الحساب VARCHAR(20) NOT NULL,
                وصف_الحركة TEXT,
                مدين DECIMAL(15,2) DEFAULT 0,
                دائن DECIMAL(15,2) DEFAULT 0,
                المرجع VARCHAR(100),
                نوع_المستند VARCHAR(50),
                رقم_المستند VARCHAR(50),
                مركز_التكلفة VARCHAR(50),
                ملاحظات TEXT,
                المستخدم VARCHAR(50),
                السنة INT,
                تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # إنشاء جدول مرفقات القيود المحاسبية
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS مرفقات_القيود_المحاسبية (
                id INT AUTO_INCREMENT PRIMARY KEY,
                رقم_القيد VARCHAR(50) NOT NULL,
                اسم_الملف_الأصلي VARCHAR(255) NOT NULL,
                اسم_الملف_المحفوظ VARCHAR(255) NOT NULL,
                مسار_الملف TEXT NOT NULL,
                نوع_الملف VARCHAR(10),
                حجم_الملف BIGINT,
                تاريخ_الرفع TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                المستخدم VARCHAR(50),
                ملاحظات TEXT,
                INDEX idx_entry_number (رقم_القيد)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        conn.commit()
        return True

    except Exception as e:
        print(f"خطأ في إنشاء الجداول المحاسبية: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

class JournalEntryDialog(QDialog):
    """حوار إضافة/تعديل قيد محاسبي"""
    
    def __init__(self, parent=None, entry_data=None):
        super().__init__(parent)
        self.entry_data = entry_data
        self.is_edit_mode = entry_data is not None
        self.details_rows = []
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_entry_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة قيد محاسبي" if not self.is_edit_mode else "تعديل قيد محاسبي")
        self.setModal(True)
        self.resize(900, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # معلومات القيد الأساسية
        self.setup_header_section(main_layout)
        
        # تفاصيل القيد
        self.setup_details_section(main_layout)

        # قسم المرفقات
        self.setup_attachments_section(main_layout)

        # أزرار الإجراءات
        self.setup_buttons_section(main_layout)
        
        # تطبيق الستايل
        if 'apply_stylesheet' in globals():
            apply_stylesheet(self)
    
    def setup_header_section(self, main_layout):
        """إعداد قسم معلومات القيد الأساسية"""
        header_group = QGroupBox("معلومات القيد")
        header_layout = QFormLayout(header_group)
        
        # رقم القيد
        self.entry_number_edit = QLineEdit()
        self.entry_number_edit.setReadOnly(True)
        self.entry_number_edit.setPlaceholderText("سيتم توليده تلقائياً")
        header_layout.addRow("رقم القيد:", self.entry_number_edit)
        
        # تاريخ القيد
        self.entry_date_edit = QDateEdit()
        self.entry_date_edit.setDate(QDate.currentDate())
        self.entry_date_edit.setCalendarPopup(True)
        header_layout.addRow("تاريخ القيد:", self.entry_date_edit)
        
        # وصف القيد
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("وصف القيد المحاسبي")
        header_layout.addRow("وصف القيد:", self.description_edit)
        
        # نوع القيد
        self.entry_type_combo = QComboBox()
        self.entry_type_combo.addItems(["عام", "دفعة", "مصروف", "راتب", "تحويل"])
        header_layout.addRow("نوع القيد:", self.entry_type_combo)
        
        # المرجع الخارجي
        self.reference_edit = QLineEdit()
        self.reference_edit.setPlaceholderText("رقم المرجع أو الفاتورة")
        header_layout.addRow("المرجع:", self.reference_edit)
        
        # مركز التكلفة
        self.cost_center_combo = QComboBox()
        self.cost_center_combo.setEditable(True)
        self.cost_center_combo.addItems(["", "مشاريع", "إدارة", "تسويق", "صيانة"])
        header_layout.addRow("مركز التكلفة:", self.cost_center_combo)
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية")
        header_layout.addRow("ملاحظات:", self.notes_edit)
        
        main_layout.addWidget(header_group)
    
    def setup_details_section(self, main_layout):
        """إعداد قسم تفاصيل القيد"""
        details_group = QGroupBox("تفاصيل القيد")
        details_layout = QVBoxLayout(details_group)
        
        # أزرار إدارة التفاصيل
        buttons_layout = QHBoxLayout()
        
        self.add_detail_btn = QPushButton("إضافة سطر")
        self.add_detail_btn.setIcon(QIcon("icons/add.png") if os.path.exists("icons/add.png") else QIcon())
        buttons_layout.addWidget(self.add_detail_btn)
        
        self.remove_detail_btn = QPushButton("حذف السطر المحدد")
        self.remove_detail_btn.setIcon(QIcon("icons/remove.png") if os.path.exists("icons/remove.png") else QIcon())
        buttons_layout.addWidget(self.remove_detail_btn)
        
        buttons_layout.addStretch()
        
        # ملصقات الإجماليات
        self.total_debit_label = QLabel("إجمالي المدين: 0.00")
        self.total_credit_label = QLabel("إجمالي الدائن: 0.00")
        self.balance_label = QLabel("الرصيد: 0.00")
        
        buttons_layout.addWidget(self.total_debit_label)
        buttons_layout.addWidget(self.total_credit_label)
        buttons_layout.addWidget(self.balance_label)
        
        details_layout.addLayout(buttons_layout)
        
        # جدول التفاصيل
        self.details_table = QTableWidget()
        self.details_table.setColumnCount(6)
        self.details_table.setHorizontalHeaderLabels([
            "كود الحساب", "اسم الحساب", "الوصف", "مدين", "دائن", "مركز التكلفة"
        ])
        
        # تنسيق الجدول
        header = self.details_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        header.setSectionResizeMode(4, QHeaderView.Fixed)
        header.setSectionResizeMode(5, QHeaderView.Fixed)
        
        self.details_table.setColumnWidth(0, 100)
        self.details_table.setColumnWidth(3, 100)
        self.details_table.setColumnWidth(4, 100)
        self.details_table.setColumnWidth(5, 120)
        
        details_layout.addWidget(self.details_table)
        
        main_layout.addWidget(details_group)

    def setup_attachments_section(self, main_layout):
        """إعداد قسم المرفقات"""
        attachments_group = QGroupBox("المرفقات")
        attachments_layout = QVBoxLayout(attachments_group)

        # أزرار إدارة المرفقات
        buttons_layout = QHBoxLayout()

        self.add_attachment_btn = QPushButton("إضافة مرفق")
        self.add_attachment_btn.setIcon(QIcon("icons/add.png") if os.path.exists("icons/add.png") else QIcon())
        buttons_layout.addWidget(self.add_attachment_btn)

        self.remove_attachment_btn = QPushButton("حذف المرفق")
        self.remove_attachment_btn.setIcon(QIcon("icons/remove.png") if os.path.exists("icons/remove.png") else QIcon())
        self.remove_attachment_btn.setEnabled(False)
        buttons_layout.addWidget(self.remove_attachment_btn)

        self.view_attachment_btn = QPushButton("عرض المرفق")
        self.view_attachment_btn.setIcon(QIcon("icons/view.png") if os.path.exists("icons/view.png") else QIcon())
        self.view_attachment_btn.setEnabled(False)
        buttons_layout.addWidget(self.view_attachment_btn)

        buttons_layout.addStretch()

        attachments_layout.addLayout(buttons_layout)

        # قائمة المرفقات
        self.attachments_list = QListWidget()
        self.attachments_list.setMaximumHeight(100)
        attachments_layout.addWidget(self.attachments_list)

        # قائمة لحفظ مسارات المرفقات
        self.attachment_paths = []

        main_layout.addWidget(attachments_group)

    def setup_buttons_section(self, main_layout):
        """إعداد قسم الأزرار"""
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_btn = QPushButton("حفظ القيد")
        self.save_btn.setIcon(QIcon("icons/save.png") if os.path.exists("icons/save.png") else QIcon())
        self.save_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        buttons_layout.addWidget(self.save_btn)
        
        # زر الحفظ والاعتماد
        self.save_approve_btn = QPushButton("حفظ واعتماد")
        self.save_approve_btn.setIcon(QIcon("icons/approve.png") if os.path.exists("icons/approve.png") else QIcon())
        self.save_approve_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        buttons_layout.addWidget(self.save_approve_btn)
        
        buttons_layout.addStretch()
        
        # زر الإلغاء
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setIcon(QIcon("icons/cancel.png") if os.path.exists("icons/cancel.png") else QIcon())
        buttons_layout.addWidget(self.cancel_btn)
        
        main_layout.addLayout(buttons_layout)
    
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.add_detail_btn.clicked.connect(self.add_detail_row)
        self.remove_detail_btn.clicked.connect(self.remove_detail_row)
        self.save_btn.clicked.connect(lambda: self.save_entry(False))
        self.save_approve_btn.clicked.connect(lambda: self.save_entry(True))
        self.cancel_btn.clicked.connect(self.reject)
        
        # ربط تحديث الإجماليات عند تغيير القيم
        self.details_table.itemChanged.connect(self.update_totals)

        # ربط أزرار المرفقات
        self.add_attachment_btn.clicked.connect(self.add_attachment)
        self.remove_attachment_btn.clicked.connect(self.remove_attachment)
        self.view_attachment_btn.clicked.connect(self.view_attachment)
        self.attachments_list.itemSelectionChanged.connect(self.on_attachment_selection_changed)
    
    def add_detail_row(self):
        """إضافة سطر جديد لتفاصيل القيد"""
        try:
            from account_selector import AccountSelectorDialog
            
            # فتح نافذة اختيار الحساب
            selector = AccountSelectorDialog(self)
            if selector.exec() == QDialog.Accepted:
                selected_account = selector.get_selected_account()
                if selected_account:
                    self.add_account_to_table(selected_account)
        except ImportError:
            # في حالة عدم توفر نافذة اختيار الحساب، إضافة سطر فارغ
            self.add_empty_row()
    
    def add_account_to_table(self, account_data):
        """إضافة حساب محدد إلى الجدول"""
        row = self.details_table.rowCount()
        self.details_table.insertRow(row)
        
        # كود الحساب
        code_item = QTableWidgetItem(account_data.get('كود_الحساب', ''))
        self.details_table.setItem(row, 0, code_item)
        
        # اسم الحساب
        name_item = QTableWidgetItem(account_data.get('اسم_الحساب', ''))
        self.details_table.setItem(row, 1, name_item)
        
        # الوصف
        desc_item = QTableWidgetItem("")
        self.details_table.setItem(row, 2, desc_item)
        
        # مدين
        debit_item = QTableWidgetItem("0.00")
        debit_item.setTextAlignment(Qt.AlignCenter)
        self.details_table.setItem(row, 3, debit_item)
        
        # دائن
        credit_item = QTableWidgetItem("0.00")
        credit_item.setTextAlignment(Qt.AlignCenter)
        self.details_table.setItem(row, 4, credit_item)
        
        # مركز التكلفة
        cost_center_item = QTableWidgetItem("")
        self.details_table.setItem(row, 5, cost_center_item)
    
    def add_empty_row(self):
        """إضافة سطر فارغ"""
        row = self.details_table.rowCount()
        self.details_table.insertRow(row)
        
        for col in range(6):
            item = QTableWidgetItem("")
            if col in [3, 4]:  # أعمدة المدين والدائن
                item.setText("0.00")
                item.setTextAlignment(Qt.AlignCenter)
            self.details_table.setItem(row, col, item)
    
    def remove_detail_row(self):
        """حذف السطر المحدد"""
        current_row = self.details_table.currentRow()
        if current_row >= 0:
            self.details_table.removeRow(current_row)
            self.update_totals()
    
    def update_totals(self):
        """تحديث الإجماليات"""
        total_debit = 0.0
        total_credit = 0.0
        
        for row in range(self.details_table.rowCount()):
            # المدين
            debit_item = self.details_table.item(row, 3)
            if debit_item and debit_item.text():
                try:
                    total_debit += float(debit_item.text())
                except ValueError:
                    pass
            
            # الدائن
            credit_item = self.details_table.item(row, 4)
            if credit_item and credit_item.text():
                try:
                    total_credit += float(credit_item.text())
                except ValueError:
                    pass
        
        balance = total_debit - total_credit
        
        # تحديث الملصقات
        self.total_debit_label.setText(f"إجمالي المدين: {total_debit:,.2f}")
        self.total_credit_label.setText(f"إجمالي الدائن: {total_credit:,.2f}")
        self.balance_label.setText(f"الرصيد: {balance:,.2f}")
        
        # تلوين ملصق الرصيد
        if abs(balance) < 0.01:  # متوازن
            self.balance_label.setStyleSheet("color: green; font-weight: bold;")
        else:  # غير متوازن
            self.balance_label.setStyleSheet("color: red; font-weight: bold;")

    def add_attachment(self):
        """إضافة مرفق جديد"""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("جميع الملفات (*.*)")

        if file_dialog.exec():
            selected_files = file_dialog.selectedFiles()
            for file_path in selected_files:
                if file_path not in self.attachment_paths:
                    self.attachment_paths.append(file_path)
                    file_name = os.path.basename(file_path)

                    item = QListWidgetItem(file_name)
                    item.setData(Qt.UserRole, file_path)
                    self.attachments_list.addItem(item)

    def remove_attachment(self):
        """حذف المرفق المحدد"""
        current_item = self.attachments_list.currentItem()
        if current_item:
            file_path = current_item.data(Qt.UserRole)
            if file_path in self.attachment_paths:
                self.attachment_paths.remove(file_path)

            row = self.attachments_list.row(current_item)
            self.attachments_list.takeItem(row)

    def view_attachment(self):
        """عرض المرفق المحدد"""
        current_item = self.attachments_list.currentItem()
        if current_item:
            file_path = current_item.data(Qt.UserRole)
            if os.path.exists(file_path):
                QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))
            else:
                QMessageBox.warning(self, "خطأ", "الملف غير موجود")

    def on_attachment_selection_changed(self):
        """معالجة تغيير تحديد المرفق"""
        has_selection = self.attachments_list.currentItem() is not None
        self.remove_attachment_btn.setEnabled(has_selection)
        self.view_attachment_btn.setEnabled(has_selection)

    def validate_entry(self):
        """التحقق من صحة بيانات القيد"""
        # التحقق من وجود وصف
        if not self.description_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يجب إدخال وصف للقيد")
            return False

        # التحقق من وجود تفاصيل
        if self.details_table.rowCount() == 0:
            QMessageBox.warning(self, "خطأ", "يجب إضافة تفاصيل للقيد")
            return False

        # التحقق من توازن القيد
        total_debit = 0.0
        total_credit = 0.0

        for row in range(self.details_table.rowCount()):
            # التحقق من وجود كود الحساب
            code_item = self.details_table.item(row, 0)
            if not code_item or not code_item.text().strip():
                QMessageBox.warning(self, "خطأ", f"يجب إدخال كود الحساب في السطر {row + 1}")
                return False

            # حساب الإجماليات
            debit_item = self.details_table.item(row, 3)
            credit_item = self.details_table.item(row, 4)

            try:
                debit = float(debit_item.text()) if debit_item and debit_item.text() else 0.0
                credit = float(credit_item.text()) if credit_item and credit_item.text() else 0.0

                total_debit += debit
                total_credit += credit

                # التحقق من أن السطر يحتوي على قيمة في المدين أو الدائن
                if debit == 0 and credit == 0:
                    QMessageBox.warning(self, "خطأ", f"يجب إدخال قيمة في المدين أو الدائن في السطر {row + 1}")
                    return False

                # التحقق من عدم وجود قيم في المدين والدائن معاً
                if debit > 0 and credit > 0:
                    QMessageBox.warning(self, "خطأ", f"لا يمكن إدخال قيم في المدين والدائن معاً في السطر {row + 1}")
                    return False

            except ValueError:
                QMessageBox.warning(self, "خطأ", f"قيم غير صحيحة في السطر {row + 1}")
                return False

        # التحقق من توازن القيد
        if abs(total_debit - total_credit) > 0.01:
            QMessageBox.warning(self, "خطأ", "القيد غير متوازن. يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن")
            return False

        return True

    def save_entry(self, approve=False):
        """حفظ القيد"""
        if not self.validate_entry():
            return

        try:
            # إعداد بيانات القيد
            entry_data = {
                'تاريخ_القيد': self.entry_date_edit.date().toString("yyyy-MM-dd"),
                'وصف_القيد': self.description_edit.text().strip(),
                'نوع_القيد': self.entry_type_combo.currentText(),
                'المرجع_الخارجي': self.reference_edit.text().strip(),
                'مركز_التكلفة': self.cost_center_combo.currentText(),
                'ملاحظات': self.notes_edit.toPlainText().strip(),
                'حالة_القيد': 'معتمد' if approve else 'مسودة',
                'المستخدم': getattr(self, 'current_user', 'admin'),
                'السنة': self.entry_date_edit.date().year()
            }

            # إعداد تفاصيل القيد
            details_list = []
            for row in range(self.details_table.rowCount()):
                detail = {
                    'كود_الحساب': self.details_table.item(row, 0).text(),
                    'اسم_الحساب': self.details_table.item(row, 1).text(),
                    'وصف_التفصيل': self.details_table.item(row, 2).text(),
                    'مدين': float(self.details_table.item(row, 3).text() or 0),
                    'دائن': float(self.details_table.item(row, 4).text() or 0),
                    'مركز_التكلفة': self.details_table.item(row, 5).text(),
                    'ترتيب_السطر': row + 1
                }
                details_list.append(detail)

            # حفظ القيد
            journal_manager = JournalEntryManager()
            success, message = journal_manager.create_journal_entry(
                entry_data, details_list, auto_approve=approve
            )

            if success:
                # حفظ المرفقات إذا كان هناك أي منها
                if self.attachment_paths:
                    self.save_attachments(message.split()[-1])  # استخراج رقم القيد من الرسالة

                QMessageBox.information(self, "نجح", "تم حفظ القيد بنجاح")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", f"فشل في حفظ القيد:\n{message}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ القيد:\n{str(e)}")

    def save_attachments(self, entry_number):
        """حفظ المرفقات المرتبطة بالقيد"""
        try:
            # إنشاء مجلد المرفقات إذا لم يكن موجود
            attachments_dir = os.path.join("attachments", "journal_entries")
            if not os.path.exists(attachments_dir):
                os.makedirs(attachments_dir)

            # إنشاء مجلد خاص بالقيد
            entry_attachments_dir = os.path.join(attachments_dir, entry_number)
            if not os.path.exists(entry_attachments_dir):
                os.makedirs(entry_attachments_dir)

            conn, cursor = con_db()
            if not conn:
                return

            # نسخ الملفات وحفظ معلوماتها في قاعدة البيانات
            for file_path in self.attachment_paths:
                if os.path.exists(file_path):
                    file_name = os.path.basename(file_path)
                    file_extension = os.path.splitext(file_name)[1]

                    # إنشاء اسم ملف فريد
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    unique_filename = f"{timestamp}_{file_name}"
                    destination_path = os.path.join(entry_attachments_dir, unique_filename)

                    # نسخ الملف
                    shutil.copy2(file_path, destination_path)

                    # حفظ معلومات المرفق في قاعدة البيانات
                    cursor.execute("""
                        INSERT INTO مرفقات_القيود_المحاسبية
                        (رقم_القيد, اسم_الملف_الأصلي, اسم_الملف_المحفوظ, مسار_الملف,
                         نوع_الملف, حجم_الملف, تاريخ_الرفع, المستخدم)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        entry_number,
                        file_name,
                        unique_filename,
                        destination_path,
                        file_extension,
                        os.path.getsize(destination_path),
                        datetime.now(),
                        getattr(self, 'current_user', 'admin')
                    ))

            conn.commit()

        except Exception as e:
            print(f"خطأ في حفظ المرفقات: {e}")
        finally:
            if 'conn' in locals():
                conn.close()

    def load_entry_data(self):
        """تحميل بيانات القيد للتعديل"""
        if not self.entry_data:
            return

        # تحميل البيانات الأساسية
        self.entry_number_edit.setText(str(self.entry_data.get('رقم_القيد', '')))

        entry_date = self.entry_data.get('تاريخ_القيد')
        if entry_date:
            if isinstance(entry_date, str):
                date_obj = QDate.fromString(entry_date, "yyyy-MM-dd")
            else:
                date_obj = QDate(entry_date)
            self.entry_date_edit.setDate(date_obj)

        self.description_edit.setText(self.entry_data.get('وصف_القيد', ''))

        entry_type = self.entry_data.get('نوع_القيد', 'عام')
        index = self.entry_type_combo.findText(entry_type)
        if index >= 0:
            self.entry_type_combo.setCurrentIndex(index)

        self.reference_edit.setText(self.entry_data.get('المرجع_الخارجي', ''))
        self.cost_center_combo.setCurrentText(self.entry_data.get('مركز_التكلفة', ''))
        self.notes_edit.setPlainText(self.entry_data.get('ملاحظات', ''))

        # تحميل التفاصيل
        self.load_entry_details()

    def load_entry_details(self):
        """تحميل تفاصيل القيد"""
        if not self.entry_data:
            return

        try:
            conn, cursor = con_db()
            if not conn:
                return

            entry_id = self.entry_data.get('id')
            cursor.execute("""
                SELECT كود_الحساب, اسم_الحساب, وصف_التفصيل, مدين, دائن, مركز_التكلفة
                FROM تفاصيل_القيود_المحاسبية
                WHERE معرف_القيد = %s
                ORDER BY ترتيب_السطر
            """, (entry_id,))

            details = cursor.fetchall()

            for detail in details:
                row = self.details_table.rowCount()
                self.details_table.insertRow(row)

                self.details_table.setItem(row, 0, QTableWidgetItem(str(detail[0])))
                self.details_table.setItem(row, 1, QTableWidgetItem(str(detail[1])))
                self.details_table.setItem(row, 2, QTableWidgetItem(str(detail[2])))

                debit_item = QTableWidgetItem(f"{float(detail[3]):,.2f}")
                debit_item.setTextAlignment(Qt.AlignCenter)
                self.details_table.setItem(row, 3, debit_item)

                credit_item = QTableWidgetItem(f"{float(detail[4]):,.2f}")
                credit_item.setTextAlignment(Qt.AlignCenter)
                self.details_table.setItem(row, 4, credit_item)

                self.details_table.setItem(row, 5, QTableWidgetItem(str(detail[5] or '')))

            self.update_totals()

        except Exception as e:
            print(f"خطأ في تحميل تفاصيل القيد: {e}")
        finally:
            if 'conn' in locals():
                conn.close()


class JournalEntriesWindow(QMainWindow):
    """النافذة الرئيسية لإدارة القيود المحاسبية"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.current_user = getattr(parent, 'current_user', 'admin') if parent else 'admin'

        # التأكد من وجود الجداول المطلوبة
        ensure_accounting_tables()

        self.setup_ui()
        self.setup_connections()
        self.load_entries()

        # تطبيق الستايل
        if 'apply_stylesheet' in globals():
            apply_stylesheet(self)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة القيود المحاسبية")
        self.setGeometry(100, 100, 1400, 800)
        self.setLayoutDirection(Qt.RightToLeft)

        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)

        # شريط الأدوات العلوي
        self.setup_toolbar(main_layout)

        # منطقة المحتوى الرئيسي
        self.setup_content_area(main_layout)

        # شريط الحالة
        self.setup_status_bar()

    def setup_toolbar(self, main_layout):
        """إعداد شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_layout = QHBoxLayout(toolbar_frame)

        # أزرار الإجراءات الرئيسية
        self.add_entry_btn = QPushButton("إضافة قيد جديد")
        self.add_entry_btn.setIcon(QIcon("icons/add.png") if os.path.exists("icons/add.png") else QIcon())
        self.add_entry_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }")
        toolbar_layout.addWidget(self.add_entry_btn)

        self.edit_entry_btn = QPushButton("تعديل القيد")
        self.edit_entry_btn.setIcon(QIcon("icons/edit.png") if os.path.exists("icons/edit.png") else QIcon())
        self.edit_entry_btn.setEnabled(False)
        toolbar_layout.addWidget(self.edit_entry_btn)

        self.delete_entry_btn = QPushButton("حذف القيد")
        self.delete_entry_btn.setIcon(QIcon("icons/delete.png") if os.path.exists("icons/delete.png") else QIcon())
        self.delete_entry_btn.setEnabled(False)
        self.delete_entry_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        toolbar_layout.addWidget(self.delete_entry_btn)

        toolbar_layout.addWidget(QLabel("|"))  # فاصل

        # أزرار الحالة
        self.approve_entry_btn = QPushButton("اعتماد القيد")
        self.approve_entry_btn.setIcon(QIcon("icons/approve.png") if os.path.exists("icons/approve.png") else QIcon())
        self.approve_entry_btn.setEnabled(False)
        self.approve_entry_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; }")
        toolbar_layout.addWidget(self.approve_entry_btn)

        self.cancel_entry_btn = QPushButton("إلغاء القيد")
        self.cancel_entry_btn.setIcon(QIcon("icons/cancel.png") if os.path.exists("icons/cancel.png") else QIcon())
        self.cancel_entry_btn.setEnabled(False)
        self.cancel_entry_btn.setStyleSheet("QPushButton { background-color: #FF9800; color: white; }")
        toolbar_layout.addWidget(self.cancel_entry_btn)

        toolbar_layout.addStretch()

        # أزرار التقارير والطباعة
        self.print_entry_btn = QPushButton("طباعة القيد")
        self.print_entry_btn.setIcon(QIcon("icons/print.png") if os.path.exists("icons/print.png") else QIcon())
        self.print_entry_btn.setEnabled(False)
        toolbar_layout.addWidget(self.print_entry_btn)

        self.export_btn = QPushButton("تصدير")
        self.export_btn.setIcon(QIcon("icons/export.png") if os.path.exists("icons/export.png") else QIcon())
        toolbar_layout.addWidget(self.export_btn)

        main_layout.addWidget(toolbar_frame)

    def setup_content_area(self, main_layout):
        """إعداد منطقة المحتوى الرئيسي"""
        # إنشاء تبويبات
        self.tab_widget = QTabWidget()

        # تبويب القيود المحاسبية
        self.entries_tab = QWidget()
        self.setup_entries_tab()
        self.tab_widget.addTab(self.entries_tab, "القيود المحاسبية")

        # تبويب دفتر اليومية
        self.journal_tab = QWidget()
        self.setup_journal_tab()
        self.tab_widget.addTab(self.journal_tab, "دفتر اليومية")

        # تبويب التقارير
        self.reports_tab = QWidget()
        self.setup_reports_tab()
        self.tab_widget.addTab(self.reports_tab, "التقارير")

        main_layout.addWidget(self.tab_widget)

    def setup_entries_tab(self):
        """إعداد تبويب القيود المحاسبية"""
        layout = QVBoxLayout(self.entries_tab)

        # منطقة البحث والتصفية
        search_frame = QFrame()
        search_frame.setFrameStyle(QFrame.StyledPanel)
        search_layout = QHBoxLayout(search_frame)

        # البحث بالرقم
        search_layout.addWidget(QLabel("رقم القيد:"))
        self.search_number_edit = QLineEdit()
        self.search_number_edit.setPlaceholderText("البحث برقم القيد")
        search_layout.addWidget(self.search_number_edit)

        # البحث بالوصف
        search_layout.addWidget(QLabel("الوصف:"))
        self.search_description_edit = QLineEdit()
        self.search_description_edit.setPlaceholderText("البحث بالوصف")
        search_layout.addWidget(self.search_description_edit)

        # تصفية بالتاريخ
        search_layout.addWidget(QLabel("من تاريخ:"))
        self.from_date_edit = QDateEdit()
        self.from_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.from_date_edit.setCalendarPopup(True)
        search_layout.addWidget(self.from_date_edit)

        search_layout.addWidget(QLabel("إلى تاريخ:"))
        self.to_date_edit = QDateEdit()
        self.to_date_edit.setDate(QDate.currentDate())
        self.to_date_edit.setCalendarPopup(True)
        search_layout.addWidget(self.to_date_edit)

        # تصفية بالحالة
        search_layout.addWidget(QLabel("الحالة:"))
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.addItems(["الكل", "مسودة", "معتمد", "ملغي"])
        search_layout.addWidget(self.status_filter_combo)

        # زر البحث
        self.search_btn = QPushButton("بحث")
        self.search_btn.setIcon(QIcon("icons/search.png") if os.path.exists("icons/search.png") else QIcon())
        search_layout.addWidget(self.search_btn)

        # زر إعادة تعيين
        self.reset_search_btn = QPushButton("إعادة تعيين")
        search_layout.addWidget(self.reset_search_btn)

        layout.addWidget(search_frame)

        # جدول القيود
        self.entries_table = QTableWidget()
        self.entries_table.setColumnCount(9)
        self.entries_table.setHorizontalHeaderLabels([
            "رقم القيد", "تاريخ القيد", "وصف القيد", "إجمالي مدين",
            "إجمالي دائن", "الحالة", "نوع القيد", "المستخدم", "تاريخ الإنشاء"
        ])

        # تنسيق الجدول
        header = self.entries_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        header.setSectionResizeMode(4, QHeaderView.Fixed)
        header.setSectionResizeMode(5, QHeaderView.Fixed)
        header.setSectionResizeMode(6, QHeaderView.Fixed)
        header.setSectionResizeMode(7, QHeaderView.Fixed)
        header.setSectionResizeMode(8, QHeaderView.Fixed)

        self.entries_table.setColumnWidth(0, 100)
        self.entries_table.setColumnWidth(1, 100)
        self.entries_table.setColumnWidth(3, 100)
        self.entries_table.setColumnWidth(4, 100)
        self.entries_table.setColumnWidth(5, 80)
        self.entries_table.setColumnWidth(6, 80)
        self.entries_table.setColumnWidth(7, 100)
        self.entries_table.setColumnWidth(8, 120)

        self.entries_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.entries_table.setAlternatingRowColors(True)

        layout.addWidget(self.entries_table)

    def setup_journal_tab(self):
        """إعداد تبويب دفتر اليومية"""
        layout = QVBoxLayout(self.journal_tab)

        # منطقة التصفية
        filter_frame = QFrame()
        filter_frame.setFrameStyle(QFrame.StyledPanel)
        filter_layout = QHBoxLayout(filter_frame)

        filter_layout.addWidget(QLabel("من تاريخ:"))
        self.journal_from_date = QDateEdit()
        self.journal_from_date.setDate(QDate.currentDate().addDays(-30))
        self.journal_from_date.setCalendarPopup(True)
        filter_layout.addWidget(self.journal_from_date)

        filter_layout.addWidget(QLabel("إلى تاريخ:"))
        self.journal_to_date = QDateEdit()
        self.journal_to_date.setDate(QDate.currentDate())
        self.journal_to_date.setCalendarPopup(True)
        filter_layout.addWidget(self.journal_to_date)

        filter_layout.addWidget(QLabel("رقم القيد:"))
        self.journal_entry_number = QLineEdit()
        filter_layout.addWidget(self.journal_entry_number)

        self.load_journal_btn = QPushButton("تحميل دفتر اليومية")
        filter_layout.addWidget(self.load_journal_btn)

        filter_layout.addStretch()

        layout.addWidget(filter_frame)

        # جدول دفتر اليومية
        self.journal_table = QTableWidget()
        self.journal_table.setColumnCount(7)
        self.journal_table.setHorizontalHeaderLabels([
            "التاريخ", "رقم القيد", "اسم الحساب", "وصف الحركة", "مدين", "دائن", "المرجع"
        ])

        # تنسيق جدول دفتر اليومية
        journal_header = self.journal_table.horizontalHeader()
        journal_header.setSectionResizeMode(2, QHeaderView.Stretch)
        journal_header.setSectionResizeMode(3, QHeaderView.Stretch)

        layout.addWidget(self.journal_table)

    def setup_reports_tab(self):
        """إعداد تبويب التقارير"""
        layout = QVBoxLayout(self.reports_tab)

        # قائمة التقارير المتاحة
        reports_group = QGroupBox("التقارير المتاحة")
        reports_layout = QVBoxLayout(reports_group)

        self.trial_balance_btn = QPushButton("ميزان المراجعة")
        self.trial_balance_btn.setIcon(QIcon("icons/report.png") if os.path.exists("icons/report.png") else QIcon())
        reports_layout.addWidget(self.trial_balance_btn)

        self.income_statement_btn = QPushButton("قائمة الدخل")
        self.income_statement_btn.setIcon(QIcon("icons/report.png") if os.path.exists("icons/report.png") else QIcon())
        reports_layout.addWidget(self.income_statement_btn)

        self.balance_sheet_btn = QPushButton("الميزانية العمومية")
        self.balance_sheet_btn.setIcon(QIcon("icons/report.png") if os.path.exists("icons/report.png") else QIcon())
        reports_layout.addWidget(self.balance_sheet_btn)

        self.account_statement_btn = QPushButton("كشف حساب")
        self.account_statement_btn.setIcon(QIcon("icons/report.png") if os.path.exists("icons/report.png") else QIcon())
        reports_layout.addWidget(self.account_statement_btn)

        reports_layout.addStretch()

        # أزرار طباعة وتصدير التقارير
        report_actions_layout = QHBoxLayout()

        self.print_report_btn = QPushButton("طباعة التقرير")
        self.print_report_btn.setIcon(QIcon("icons/print.png") if os.path.exists("icons/print.png") else QIcon())
        report_actions_layout.addWidget(self.print_report_btn)

        self.export_report_btn = QPushButton("تصدير التقرير")
        self.export_report_btn.setIcon(QIcon("icons/export.png") if os.path.exists("icons/export.png") else QIcon())
        report_actions_layout.addWidget(self.export_report_btn)

        reports_layout.addLayout(report_actions_layout)

        layout.addWidget(reports_group)

        # منطقة عرض التقرير
        self.report_display = QTextEdit()
        self.report_display.setReadOnly(True)
        layout.addWidget(self.report_display)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("جاهز")

        # إضافة معلومات إضافية
        self.entries_count_label = QLabel("عدد القيود: 0")
        self.status_bar.addPermanentWidget(self.entries_count_label)

        self.total_balance_label = QLabel("الرصيد الإجمالي: 0.00")
        self.status_bar.addPermanentWidget(self.total_balance_label)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # أزرار شريط الأدوات
        self.add_entry_btn.clicked.connect(self.add_new_entry)
        self.edit_entry_btn.clicked.connect(self.edit_selected_entry)
        self.delete_entry_btn.clicked.connect(self.delete_selected_entry)
        self.approve_entry_btn.clicked.connect(self.approve_selected_entry)
        self.cancel_entry_btn.clicked.connect(self.cancel_selected_entry)
        self.print_entry_btn.clicked.connect(self.print_selected_entry)
        self.export_btn.clicked.connect(self.export_entries)

        # أزرار البحث والتصفية
        self.search_btn.clicked.connect(self.search_entries)
        self.reset_search_btn.clicked.connect(self.reset_search)
        self.status_filter_combo.currentTextChanged.connect(self.load_entries)

        # جدول القيود
        self.entries_table.itemSelectionChanged.connect(self.on_entry_selection_changed)
        self.entries_table.itemDoubleClicked.connect(self.edit_selected_entry)

        # تبويب دفتر اليومية
        self.load_journal_btn.clicked.connect(self.load_journal)

        # أزرار التقارير
        self.trial_balance_btn.clicked.connect(self.generate_trial_balance)
        self.income_statement_btn.clicked.connect(self.generate_income_statement)
        self.balance_sheet_btn.clicked.connect(self.generate_balance_sheet)
        self.account_statement_btn.clicked.connect(self.generate_account_statement)

        # أزرار طباعة وتصدير التقارير
        self.print_report_btn.clicked.connect(self.print_current_report)
        self.export_report_btn.clicked.connect(self.export_current_report)

    def load_entries(self):
        """تحميل القيود المحاسبية"""
        try:
            conn, cursor = con_db()
            if not conn:
                return

            # بناء استعلام البحث
            query = """
                SELECT رقم_القيد, تاريخ_القيد, وصف_القيد, إجمالي_مدين, إجمالي_دائن,
                       حالة_القيد, نوع_القيد, المستخدم, تاريخ_الإنشاء
                FROM القيود_المحاسبية
                WHERE 1=1
            """
            params = []

            # تصفية بالحالة
            if hasattr(self, 'status_filter_combo') and self.status_filter_combo.currentText() != "الكل":
                query += " AND حالة_القيد = %s"
                params.append(self.status_filter_combo.currentText())

            # ترتيب النتائج
            query += " ORDER BY تاريخ_القيد DESC, رقم_القيد DESC"

            cursor.execute(query, params)
            entries = cursor.fetchall()

            # تحديث الجدول
            self.entries_table.setRowCount(len(entries))

            for row, entry in enumerate(entries):
                for col, value in enumerate(entry):
                    item = QTableWidgetItem(str(value) if value is not None else "")

                    # تنسيق خاص للأعمدة المالية
                    if col in [3, 4]:  # إجمالي مدين ودائن
                        try:
                            amount = float(value) if value else 0.0
                            item.setText(f"{amount:,.2f}")
                            item.setTextAlignment(Qt.AlignCenter)
                        except (ValueError, TypeError):
                            item.setText("0.00")

                    # تلوين حسب الحالة
                    if col == 5:  # عمود الحالة
                        if value == "معتمد":
                            item.setBackground(QColor(200, 255, 200))  # أخضر فاتح
                        elif value == "مسودة":
                            item.setBackground(QColor(255, 255, 200))  # أصفر فاتح
                        elif value == "ملغي":
                            item.setBackground(QColor(255, 200, 200))  # أحمر فاتح

                    self.entries_table.setItem(row, col, item)

            # تحديث شريط الحالة
            self.entries_count_label.setText(f"عدد القيود: {len(entries)}")

            # حساب الرصيد الإجمالي
            total_debit = sum(float(entry[3]) if entry[3] else 0 for entry in entries)
            total_credit = sum(float(entry[4]) if entry[4] else 0 for entry in entries)
            balance = total_debit - total_credit
            self.total_balance_label.setText(f"الرصيد الإجمالي: {balance:,.2f}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل القيود:\n{str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def on_entry_selection_changed(self):
        """معالجة تغيير التحديد في جدول القيود"""
        selected_rows = self.entries_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0

        # تفعيل/تعطيل الأزرار حسب التحديد
        self.edit_entry_btn.setEnabled(has_selection)
        self.delete_entry_btn.setEnabled(has_selection)
        self.print_entry_btn.setEnabled(has_selection)

        if has_selection:
            # التحقق من حالة القيد لتفعيل أزرار الحالة
            row = selected_rows[0].row()
            status_item = self.entries_table.item(row, 5)
            if status_item:
                status = status_item.text()
                self.approve_entry_btn.setEnabled(status == "مسودة")
                self.cancel_entry_btn.setEnabled(status == "معتمد")
        else:
            self.approve_entry_btn.setEnabled(False)
            self.cancel_entry_btn.setEnabled(False)

    def add_new_entry(self):
        """إضافة قيد جديد"""
        dialog = JournalEntryDialog(self)
        if dialog.exec() == QDialog.Accepted:
            self.load_entries()
            self.status_bar.showMessage("تم إضافة القيد بنجاح", 3000)

    def edit_selected_entry(self):
        """تعديل القيد المحدد"""
        selected_rows = self.entries_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد قيد للتعديل")
            return

        row = selected_rows[0].row()
        entry_number = self.entries_table.item(row, 0).text()

        # جلب بيانات القيد من قاعدة البيانات
        try:
            conn, cursor = con_db()
            if not conn:
                return

            cursor.execute("""
                SELECT * FROM القيود_المحاسبية WHERE رقم_القيد = %s
            """, (entry_number,))

            entry_data = cursor.fetchone()
            if entry_data:
                # تحويل النتيجة إلى قاموس
                columns = [desc[0] for desc in cursor.description]
                entry_dict = dict(zip(columns, entry_data))

                dialog = JournalEntryDialog(self, entry_dict)
                if dialog.exec() == QDialog.Accepted:
                    self.load_entries()
                    self.status_bar.showMessage("تم تعديل القيد بنجاح", 3000)
            else:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على القيد")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات القيد:\n{str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def delete_selected_entry(self):
        """حذف القيد المحدد"""
        selected_rows = self.entries_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد قيد للحذف")
            return

        row = selected_rows[0].row()
        entry_number = self.entries_table.item(row, 0).text()
        status = self.entries_table.item(row, 5).text()

        # التحقق من إمكانية الحذف
        if status == "معتمد":
            QMessageBox.warning(self, "تحذير", "لا يمكن حذف قيد معتمد")
            return

        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف القيد رقم {entry_number}؟",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn, cursor = con_db()
                if not conn:
                    return

                # حذف القيد وتفاصيله (سيتم حذف التفاصيل تلقائياً بسبب CASCADE)
                cursor.execute("DELETE FROM القيود_المحاسبية WHERE رقم_القيد = %s", (entry_number,))
                conn.commit()

                self.load_entries()
                self.status_bar.showMessage("تم حذف القيد بنجاح", 3000)

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف القيد:\n{str(e)}")
            finally:
                if 'conn' in locals():
                    conn.close()

    def approve_selected_entry(self):
        """اعتماد القيد المحدد"""
        self.change_entry_status("معتمد")

    def cancel_selected_entry(self):
        """إلغاء القيد المحدد"""
        self.change_entry_status("ملغي")

    def change_entry_status(self, new_status):
        """تغيير حالة القيد"""
        selected_rows = self.entries_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        row = selected_rows[0].row()
        entry_number = self.entries_table.item(row, 0).text()

        try:
            conn, cursor = con_db()
            if not conn:
                return

            cursor.execute("""
                UPDATE القيود_المحاسبية
                SET حالة_القيد = %s, تاريخ_التعديل = NOW()
                WHERE رقم_القيد = %s
            """, (new_status, entry_number))

            conn.commit()

            self.load_entries()
            self.status_bar.showMessage(f"تم تغيير حالة القيد إلى {new_status}", 3000)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تغيير حالة القيد:\n{str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def search_entries(self):
        """البحث في القيود"""
        try:
            conn, cursor = con_db()
            if not conn:
                return

            # بناء استعلام البحث
            query = """
                SELECT رقم_القيد, تاريخ_القيد, وصف_القيد, إجمالي_مدين, إجمالي_دائن,
                       حالة_القيد, نوع_القيد, المستخدم, تاريخ_الإنشاء
                FROM القيود_المحاسبية
                WHERE 1=1
            """
            params = []

            # البحث برقم القيد
            if self.search_number_edit.text().strip():
                query += " AND رقم_القيد LIKE %s"
                params.append(f"%{self.search_number_edit.text().strip()}%")

            # البحث بالوصف
            if self.search_description_edit.text().strip():
                query += " AND وصف_القيد LIKE %s"
                params.append(f"%{self.search_description_edit.text().strip()}%")

            # تصفية بالتاريخ
            from_date = self.from_date_edit.date().toString("yyyy-MM-dd")
            to_date = self.to_date_edit.date().toString("yyyy-MM-dd")
            query += " AND تاريخ_القيد BETWEEN %s AND %s"
            params.extend([from_date, to_date])

            # تصفية بالحالة
            if self.status_filter_combo.currentText() != "الكل":
                query += " AND حالة_القيد = %s"
                params.append(self.status_filter_combo.currentText())

            query += " ORDER BY تاريخ_القيد DESC, رقم_القيد DESC"

            cursor.execute(query, params)
            entries = cursor.fetchall()

            # تحديث الجدول
            self.entries_table.setRowCount(len(entries))

            for row, entry in enumerate(entries):
                for col, value in enumerate(entry):
                    item = QTableWidgetItem(str(value) if value is not None else "")

                    # تنسيق خاص للأعمدة المالية
                    if col in [3, 4]:  # إجمالي مدين ودائن
                        try:
                            amount = float(value) if value else 0.0
                            item.setText(f"{amount:,.2f}")
                            item.setTextAlignment(Qt.AlignCenter)
                        except (ValueError, TypeError):
                            item.setText("0.00")

                    # تلوين حسب الحالة
                    if col == 5:  # عمود الحالة
                        if value == "معتمد":
                            item.setBackground(QColor(200, 255, 200))  # أخضر فاتح
                        elif value == "مسودة":
                            item.setBackground(QColor(255, 255, 200))  # أصفر فاتح
                        elif value == "ملغي":
                            item.setBackground(QColor(255, 200, 200))  # أحمر فاتح

                    self.entries_table.setItem(row, col, item)

            # تحديث شريط الحالة
            self.entries_count_label.setText(f"عدد القيود: {len(entries)}")
            self.status_bar.showMessage(f"تم العثور على {len(entries)} قيد", 3000)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في البحث:\n{str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def reset_search(self):
        """إعادة تعيين البحث"""
        self.search_number_edit.clear()
        self.search_description_edit.clear()
        self.from_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.to_date_edit.setDate(QDate.currentDate())
        self.status_filter_combo.setCurrentText("الكل")
        self.load_entries()

    def load_journal(self):
        """تحميل دفتر اليومية"""
        try:
            conn, cursor = con_db()
            if not conn:
                return

            from_date = self.journal_from_date.date().toString("yyyy-MM-dd")
            to_date = self.journal_to_date.date().toString("yyyy-MM-dd")
            entry_no = self.journal_entry_number.text().strip()

            # بناء استعلام دفتر اليومية
            query = """
                SELECT ح.تاريخ_القيد, ح.رقم_القيد, ش.اسم_الحساب, ح.وصف_الحركة, ح.مدين, ح.دائن, ح.المرجع
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                WHERE ح.تاريخ_القيد BETWEEN %s AND %s
            """
            params = [from_date, to_date]

            if entry_no:
                query += " AND ح.رقم_القيد = %s"
                params.append(entry_no)

            query += " ORDER BY ح.تاريخ_القيد, ح.رقم_القيد, ح.id"

            cursor.execute(query, params)
            movements = cursor.fetchall()

            # تحديث جدول دفتر اليومية
            self.journal_table.setRowCount(len(movements))

            for row, movement in enumerate(movements):
                for col, value in enumerate(movement):
                    item = QTableWidgetItem(str(value) if value is not None else "")

                    # تنسيق خاص للأعمدة المالية
                    if col in [4, 5]:  # مدين ودائن
                        try:
                            amount = float(value) if value else 0.0
                            item.setText(f"{amount:,.2f}")
                            item.setTextAlignment(Qt.AlignCenter)
                        except (ValueError, TypeError):
                            item.setText("0.00")

                    self.journal_table.setItem(row, col, item)

            self.status_bar.showMessage(f"تم تحميل {len(movements)} حركة في دفتر اليومية", 3000)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل دفتر اليومية:\n{str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def print_selected_entry(self):
        """طباعة القيد المحدد"""
        selected_rows = self.entries_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد قيد للطباعة")
            return

        row = selected_rows[0].row()
        entry_number = self.entries_table.item(row, 0).text()

        try:
            # إنشاء HTML للطباعة
            html_content = self.generate_entry_print_html(entry_number)

            # إنشاء نافذة معاينة الطباعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setOrientation(QPrinter.Portrait)

            preview_dialog = QPrintPreviewDialog(printer, self)
            preview_dialog.setWindowTitle(f"معاينة طباعة القيد رقم {entry_number}")

            def print_document(printer):
                document = QTextBrowser()
                document.setHtml(html_content)
                document.print_(printer)

            preview_dialog.paintRequested.connect(print_document)
            preview_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة القيد:\n{str(e)}")

    def generate_entry_print_html(self, entry_number):
        """إنشاء HTML لطباعة القيد"""
        try:
            conn, cursor = con_db()
            if not conn:
                return ""

            # الحصول على بيانات القيد
            cursor.execute("""
                SELECT رقم_القيد, تاريخ_القيد, وصف_القيد, إجمالي_مدين, إجمالي_دائن,
                       حالة_القيد, نوع_القيد, المرجع_الخارجي, مركز_التكلفة, ملاحظات, المستخدم
                FROM القيود_المحاسبية
                WHERE رقم_القيد = %s
            """, (entry_number,))

            entry = cursor.fetchone()
            if not entry:
                return ""

            # الحصول على تفاصيل القيد
            cursor.execute("""
                SELECT كود_الحساب, اسم_الحساب, وصف_التفصيل, مدين, دائن, مركز_التكلفة
                FROM تفاصيل_القيود_المحاسبية
                WHERE رقم_القيد = %s
                ORDER BY ترتيب_السطر
            """, (entry_number,))

            details = cursor.fetchall()

            # إنشاء HTML
            html = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ text-align: center; margin-bottom: 30px; }}
                    .company-name {{ font-size: 24px; font-weight: bold; color: #2c3e50; }}
                    .document-title {{ font-size: 18px; color: #34495e; margin-top: 10px; }}
                    .entry-info {{ margin: 20px 0; }}
                    .info-table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                    .info-table td {{ padding: 8px; border: 1px solid #ddd; }}
                    .info-label {{ background-color: #ecf0f1; font-weight: bold; width: 150px; }}
                    .details-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                    .details-table th, .details-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                    .details-table th {{ background-color: #3498db; color: white; }}
                    .total-row {{ background-color: #ecf0f1; font-weight: bold; }}
                    .footer {{ margin-top: 50px; text-align: center; font-size: 12px; color: #7f8c8d; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="company-name">شركة المهندس لإدارة المشاريع</div>
                    <div class="document-title">قيد محاسبي رقم {entry[0]}</div>
                </div>

                <div class="entry-info">
                    <table class="info-table">
                        <tr>
                            <td class="info-label">رقم القيد:</td>
                            <td>{entry[0]}</td>
                            <td class="info-label">تاريخ القيد:</td>
                            <td>{entry[1]}</td>
                        </tr>
                        <tr>
                            <td class="info-label">وصف القيد:</td>
                            <td colspan="3">{entry[2]}</td>
                        </tr>
                        <tr>
                            <td class="info-label">نوع القيد:</td>
                            <td>{entry[6]}</td>
                            <td class="info-label">الحالة:</td>
                            <td>{entry[5]}</td>
                        </tr>
                        <tr>
                            <td class="info-label">المرجع الخارجي:</td>
                            <td>{entry[7] or ''}</td>
                            <td class="info-label">مركز التكلفة:</td>
                            <td>{entry[8] or ''}</td>
                        </tr>
                        <tr>
                            <td class="info-label">المستخدم:</td>
                            <td>{entry[10]}</td>
                            <td class="info-label">تاريخ الطباعة:</td>
                            <td>{datetime.now().strftime('%Y-%m-%d %H:%M')}</td>
                        </tr>
                    </table>
                </div>

                <table class="details-table">
                    <thead>
                        <tr>
                            <th>كود الحساب</th>
                            <th>اسم الحساب</th>
                            <th>الوصف</th>
                            <th>مدين</th>
                            <th>دائن</th>
                            <th>مركز التكلفة</th>
                        </tr>
                    </thead>
                    <tbody>
            """

            for detail in details:
                html += f"""
                        <tr>
                            <td>{detail[0]}</td>
                            <td>{detail[1]}</td>
                            <td>{detail[2] or ''}</td>
                            <td>{float(detail[3]):,.2f}</td>
                            <td>{float(detail[4]):,.2f}</td>
                            <td>{detail[5] or ''}</td>
                        </tr>
                """

            html += f"""
                        <tr class="total-row">
                            <td colspan="3">الإجمالي</td>
                            <td>{float(entry[3]):,.2f}</td>
                            <td>{float(entry[4]):,.2f}</td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>

                <div class="footer">
                    <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المشاريع - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
            </body>
            </html>
            """

            return html

        except Exception as e:
            print(f"خطأ في إنشاء HTML للطباعة: {e}")
            return ""
        finally:
            if 'conn' in locals():
                conn.close()

    def export_entries(self):
        """تصدير القيود"""
        # إنشاء حوار اختيار نوع التصدير
        export_dialog = QDialog(self)
        export_dialog.setWindowTitle("تصدير القيود المحاسبية")
        export_dialog.setModal(True)
        export_dialog.resize(400, 300)
        export_dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(export_dialog)

        # اختيار نوع التصدير
        export_type_group = QGroupBox("نوع التصدير")
        export_type_layout = QVBoxLayout(export_type_group)

        self.export_excel_radio = QCheckBox("تصدير إلى Excel")
        self.export_excel_radio.setChecked(True)
        export_type_layout.addWidget(self.export_excel_radio)

        self.export_pdf_radio = QCheckBox("تصدير إلى PDF")
        export_type_layout.addWidget(self.export_pdf_radio)

        self.export_csv_radio = QCheckBox("تصدير إلى CSV")
        export_type_layout.addWidget(self.export_csv_radio)

        layout.addWidget(export_type_group)

        # اختيار البيانات المراد تصديرها
        data_group = QGroupBox("البيانات المراد تصديرها")
        data_layout = QVBoxLayout(data_group)

        self.export_all_entries = QCheckBox("جميع القيود")
        self.export_all_entries.setChecked(True)
        data_layout.addWidget(self.export_all_entries)

        self.export_selected_entry = QCheckBox("القيد المحدد فقط")
        data_layout.addWidget(self.export_selected_entry)

        self.export_date_range = QCheckBox("فترة زمنية محددة")
        data_layout.addWidget(self.export_date_range)

        # تواريخ الفترة
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("من:"))
        self.export_from_date = QDateEdit()
        self.export_from_date.setDate(QDate.currentDate().addDays(-30))
        self.export_from_date.setCalendarPopup(True)
        self.export_from_date.setEnabled(False)
        date_layout.addWidget(self.export_from_date)

        date_layout.addWidget(QLabel("إلى:"))
        self.export_to_date = QDateEdit()
        self.export_to_date.setDate(QDate.currentDate())
        self.export_to_date.setCalendarPopup(True)
        self.export_to_date.setEnabled(False)
        date_layout.addWidget(self.export_to_date)

        data_layout.addLayout(date_layout)
        layout.addWidget(data_group)

        # أزرار الحوار
        buttons_layout = QHBoxLayout()

        export_btn = QPushButton("تصدير")
        export_btn.clicked.connect(lambda: self.perform_export(export_dialog))
        buttons_layout.addWidget(export_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(export_dialog.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        # ربط الأحداث
        self.export_date_range.toggled.connect(lambda checked: (
            self.export_from_date.setEnabled(checked),
            self.export_to_date.setEnabled(checked)
        ))

        export_dialog.exec()

    def perform_export(self, dialog):
        """تنفيذ عملية التصدير"""
        try:
            # التحقق من اختيار نوع التصدير
            export_types = []
            if self.export_excel_radio.isChecked():
                export_types.append('excel')
            if self.export_pdf_radio.isChecked():
                export_types.append('pdf')
            if self.export_csv_radio.isChecked():
                export_types.append('csv')

            if not export_types:
                QMessageBox.warning(dialog, "تحذير", "يرجى اختيار نوع التصدير")
                return

            # الحصول على البيانات
            data = self.get_export_data()
            if not data:
                QMessageBox.warning(dialog, "تحذير", "لا توجد بيانات للتصدير")
                return

            # اختيار مجلد الحفظ
            save_dir = QFileDialog.getExistingDirectory(dialog, "اختيار مجلد الحفظ")
            if not save_dir:
                return

            # تنفيذ التصدير
            success_count = 0
            for export_type in export_types:
                if export_type == 'excel':
                    if self.export_to_excel(data, save_dir):
                        success_count += 1
                elif export_type == 'pdf':
                    if self.export_to_pdf(data, save_dir):
                        success_count += 1
                elif export_type == 'csv':
                    if self.export_to_csv(data, save_dir):
                        success_count += 1

            if success_count > 0:
                QMessageBox.information(dialog, "نجح", f"تم تصدير {success_count} ملف بنجاح")
                dialog.accept()
            else:
                QMessageBox.critical(dialog, "خطأ", "فشل في تصدير الملفات")

        except Exception as e:
            QMessageBox.critical(dialog, "خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")

    def get_export_data(self):
        """الحصول على البيانات المراد تصديرها"""
        try:
            conn, cursor = con_db()
            if not conn:
                return None

            # بناء الاستعلام حسب الاختيار
            if self.export_selected_entry.isChecked():
                # القيد المحدد فقط
                selected_rows = self.entries_table.selectionModel().selectedRows()
                if not selected_rows:
                    return None

                row = selected_rows[0].row()
                entry_number = self.entries_table.item(row, 0).text()

                cursor.execute("""
                    SELECT ق.رقم_القيد, ق.تاريخ_القيد, ق.وصف_القيد, ق.إجمالي_مدين, ق.إجمالي_دائن,
                           ق.حالة_القيد, ق.نوع_القيد, ق.المرجع_الخارجي, ق.مركز_التكلفة, ق.المستخدم,
                           ت.كود_الحساب, ت.اسم_الحساب, ت.وصف_التفصيل, ت.مدين, ت.دائن
                    FROM القيود_المحاسبية ق
                    LEFT JOIN تفاصيل_القيود_المحاسبية ت ON ق.رقم_القيد = ت.رقم_القيد
                    WHERE ق.رقم_القيد = %s
                    ORDER BY ق.تاريخ_القيد, ت.ترتيب_السطر
                """, (entry_number,))

            elif self.export_date_range.isChecked():
                # فترة زمنية محددة
                from_date = self.export_from_date.date().toString("yyyy-MM-dd")
                to_date = self.export_to_date.date().toString("yyyy-MM-dd")

                cursor.execute("""
                    SELECT ق.رقم_القيد, ق.تاريخ_القيد, ق.وصف_القيد, ق.إجمالي_مدين, ق.إجمالي_دائن,
                           ق.حالة_القيد, ق.نوع_القيد, ق.المرجع_الخارجي, ق.مركز_التكلفة, ق.المستخدم,
                           ت.كود_الحساب, ت.اسم_الحساب, ت.وصف_التفصيل, ت.مدين, ت.دائن
                    FROM القيود_المحاسبية ق
                    LEFT JOIN تفاصيل_القيود_المحاسبية ت ON ق.رقم_القيد = ت.رقم_القيد
                    WHERE ق.تاريخ_القيد BETWEEN %s AND %s
                    ORDER BY ق.تاريخ_القيد, ت.ترتيب_السطر
                """, (from_date, to_date))

            else:
                # جميع القيود
                cursor.execute("""
                    SELECT ق.رقم_القيد, ق.تاريخ_القيد, ق.وصف_القيد, ق.إجمالي_مدين, ق.إجمالي_دائن,
                           ق.حالة_القيد, ق.نوع_القيد, ق.المرجع_الخارجي, ق.مركز_التكلفة, ق.المستخدم,
                           ت.كود_الحساب, ت.اسم_الحساب, ت.وصف_التفصيل, ت.مدين, ت.دائن
                    FROM القيود_المحاسبية ق
                    LEFT JOIN تفاصيل_القيود_المحاسبية ت ON ق.رقم_القيد = ت.رقم_القيد
                    ORDER BY ق.تاريخ_القيد, ت.ترتيب_السطر
                """)

            return cursor.fetchall()

        except Exception as e:
            print(f"خطأ في الحصول على بيانات التصدير: {e}")
            return None
        finally:
            if 'conn' in locals():
                conn.close()

    def export_to_excel(self, data, save_dir):
        """تصدير البيانات إلى Excel"""
        try:
            # إنشاء DataFrame
            df_data = []
            for row in data:
                df_data.append({
                    'رقم القيد': row[0],
                    'تاريخ القيد': row[1],
                    'وصف القيد': row[2],
                    'إجمالي مدين': row[3],
                    'إجمالي دائن': row[4],
                    'الحالة': row[5],
                    'نوع القيد': row[6],
                    'المرجع الخارجي': row[7],
                    'مركز التكلفة': row[8],
                    'المستخدم': row[9],
                    'كود الحساب': row[10],
                    'اسم الحساب': row[11],
                    'وصف التفصيل': row[12],
                    'مدين': row[13],
                    'دائن': row[14]
                })

            df = pd.DataFrame(df_data)

            # إنشاء ملف Excel
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"القيود_المحاسبية_{timestamp}.xlsx"
            filepath = os.path.join(save_dir, filename)

            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='القيود المحاسبية', index=False)

                # تنسيق الملف
                workbook = writer.book
                worksheet = writer.sheets['القيود المحاسبية']

                # تنسيق العناوين
                header_font = Font(bold=True, color="FFFFFF")
                header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

                for cell in worksheet[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal="center")

                # تنسيق الأعمدة
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            return True

        except Exception as e:
            print(f"خطأ في تصدير Excel: {e}")
            return False

    def export_to_pdf(self, data, save_dir):
        """تصدير البيانات إلى PDF"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"القيود_المحاسبية_{timestamp}.pdf"
            filepath = os.path.join(save_dir, filename)

            # إنشاء مستند PDF
            doc = SimpleDocTemplate(filepath, pagesize=A4)
            story = []

            # إضافة خط عربي إذا كان متوفر
            try:
                font_path = "fonts/NotoNaskhArabic-Regular.ttf"
                if os.path.exists(font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', font_path))
                    arabic_font = 'Arabic'
                else:
                    arabic_font = 'Helvetica'
            except:
                arabic_font = 'Helvetica'

            # إنشاء الأنماط
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontName=arabic_font,
                fontSize=16,
                alignment=1,  # وسط
                spaceAfter=30
            )

            # العنوان
            title = Paragraph("تقرير القيود المحاسبية", title_style)
            story.append(title)
            story.append(Spacer(1, 12))

            # إعداد بيانات الجدول
            table_data = [
                ['رقم القيد', 'التاريخ', 'الوصف', 'مدين', 'دائن', 'الحالة']
            ]

            # تجميع البيانات حسب القيد
            entries_dict = {}
            for row in data:
                entry_num = row[0]
                if entry_num not in entries_dict:
                    entries_dict[entry_num] = {
                        'date': row[1],
                        'description': row[2],
                        'total_debit': row[3],
                        'total_credit': row[4],
                        'status': row[5]
                    }

            # إضافة البيانات للجدول
            for entry_num, entry_data in entries_dict.items():
                table_data.append([
                    str(entry_num),
                    str(entry_data['date']),
                    str(entry_data['description'])[:30] + '...' if len(str(entry_data['description'])) > 30 else str(entry_data['description']),
                    f"{float(entry_data['total_debit']):,.2f}",
                    f"{float(entry_data['total_credit']):,.2f}",
                    str(entry_data['status'])
                ])

            # إنشاء الجدول
            table = Table(table_data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRid', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(table)

            # إنشاء المستند
            doc.build(story)
            return True

        except Exception as e:
            print(f"خطأ في تصدير PDF: {e}")
            return False

    def export_to_csv(self, data, save_dir):
        """تصدير البيانات إلى CSV"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"القيود_المحاسبية_{timestamp}.csv"
            filepath = os.path.join(save_dir, filename)

            # إنشاء DataFrame
            df_data = []
            for row in data:
                df_data.append({
                    'رقم القيد': row[0],
                    'تاريخ القيد': row[1],
                    'وصف القيد': row[2],
                    'إجمالي مدين': row[3],
                    'إجمالي دائن': row[4],
                    'الحالة': row[5],
                    'نوع القيد': row[6],
                    'المرجع الخارجي': row[7],
                    'مركز التكلفة': row[8],
                    'المستخدم': row[9],
                    'كود الحساب': row[10],
                    'اسم الحساب': row[11],
                    'وصف التفصيل': row[12],
                    'مدين': row[13],
                    'دائن': row[14]
                })

            df = pd.DataFrame(df_data)
            df.to_csv(filepath, index=False, encoding='utf-8-sig')

            return True

        except Exception as e:
            print(f"خطأ في تصدير CSV: {e}")
            return False

    def generate_trial_balance(self):
        """إنشاء ميزان المراجعة"""
        try:
            conn, cursor = con_db()
            if not conn:
                return

            # الحصول على أرصدة الحسابات
            cursor.execute("""
                SELECT ش.كود_الحساب, ش.اسم_الحساب, ش.نوع_الحساب,
                       COALESCE(SUM(ح.مدين), 0) as إجمالي_مدين,
                       COALESCE(SUM(ح.دائن), 0) as إجمالي_دائن,
                       CASE
                           WHEN ش.طبيعة_الحساب = 'مدين' THEN COALESCE(SUM(ح.مدين), 0) - COALESCE(SUM(ح.دائن), 0)
                           ELSE COALESCE(SUM(ح.دائن), 0) - COALESCE(SUM(ح.مدين), 0)
                       END as الرصيد
                FROM شجرة_الحسابات ش
                LEFT JOIN حركات_الحسابات ح ON ش.كود_الحساب = ح.كود_الحساب
                WHERE ش.حساب_نهائي = TRUE
                GROUP BY ش.كود_الحساب, ش.اسم_الحساب, ش.نوع_الحساب, ش.طبيعة_الحساب
                ORDER BY ش.كود_الحساب
            """)

            accounts = cursor.fetchall()

            # إنشاء HTML للتقرير
            html = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ text-align: center; color: #2c3e50; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                    th {{ background-color: #3498db; color: white; }}
                    .total-row {{ background-color: #ecf0f1; font-weight: bold; }}
                    .debit {{ color: #e74c3c; }}
                    .credit {{ color: #27ae60; }}
                </style>
            </head>
            <body>
                <h1>ميزان المراجعة</h1>
                <p style="text-align: center;">التاريخ: {datetime.now().strftime('%Y-%m-%d')}</p>

                <table>
                    <thead>
                        <tr>
                            <th>كود الحساب</th>
                            <th>اسم الحساب</th>
                            <th>نوع الحساب</th>
                            <th>إجمالي مدين</th>
                            <th>إجمالي دائن</th>
                            <th>الرصيد</th>
                        </tr>
                    </thead>
                    <tbody>
            """

            total_debit = 0
            total_credit = 0

            for account in accounts:
                code, name, acc_type, debit, credit, balance = account
                total_debit += float(debit)
                total_credit += float(credit)

                balance_class = "debit" if balance > 0 else "credit"

                html += f"""
                        <tr>
                            <td>{code}</td>
                            <td>{name}</td>
                            <td>{acc_type}</td>
                            <td>{debit:,.2f}</td>
                            <td>{credit:,.2f}</td>
                            <td class="{balance_class}">{balance:,.2f}</td>
                        </tr>
                """

            html += f"""
                    </tbody>
                    <tfoot>
                        <tr class="total-row">
                            <td colspan="3">الإجمالي</td>
                            <td>{total_debit:,.2f}</td>
                            <td>{total_credit:,.2f}</td>
                            <td>{total_debit - total_credit:,.2f}</td>
                        </tr>
                    </tfoot>
                </table>
            </body>
            </html>
            """

            self.report_display.setHtml(html)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء ميزان المراجعة:\n{str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def generate_income_statement(self):
        """إنشاء قائمة الدخل"""
        try:
            conn, cursor = con_db()
            if not conn:
                return

            # الحصول على الإيرادات
            cursor.execute("""
                SELECT ش.اسم_الحساب, COALESCE(SUM(ح.دائن - ح.مدين), 0) as المبلغ
                FROM شجرة_الحسابات ش
                LEFT JOIN حركات_الحسابات ح ON ش.كود_الحساب = ح.كود_الحساب
                WHERE ش.نوع_الحساب = 'إيرادات' AND ش.حساب_نهائي = TRUE
                GROUP BY ش.كود_الحساب, ش.اسم_الحساب
                HAVING المبلغ != 0
                ORDER BY ش.كود_الحساب
            """)
            revenues = cursor.fetchall()

            # الحصول على المصروفات
            cursor.execute("""
                SELECT ش.اسم_الحساب, COALESCE(SUM(ح.مدين - ح.دائن), 0) as المبلغ
                FROM شجرة_الحسابات ش
                LEFT JOIN حركات_الحسابات ح ON ش.كود_الحساب = ح.كود_الحساب
                WHERE ش.نوع_الحساب = 'مصروفات' AND ش.حساب_نهائي = TRUE
                GROUP BY ش.كود_الحساب, ش.اسم_الحساب
                HAVING المبلغ != 0
                ORDER BY ش.كود_الحساب
            """)
            expenses = cursor.fetchall()

            # حساب الإجماليات
            total_revenues = sum(float(rev[1]) for rev in revenues)
            total_expenses = sum(float(exp[1]) for exp in expenses)
            net_income = total_revenues - total_expenses

            # إنشاء HTML للتقرير
            html = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ text-align: center; color: #2c3e50; }}
                    h2 {{ color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; }}
                    th {{ background-color: #3498db; color: white; text-align: center; }}
                    .revenue {{ background-color: #d5f4e6; }}
                    .expense {{ background-color: #fadbd8; }}
                    .total {{ background-color: #ecf0f1; font-weight: bold; }}
                    .net-income {{ background-color: #aed6f1; font-weight: bold; font-size: 1.1em; }}
                    .positive {{ color: #27ae60; }}
                    .negative {{ color: #e74c3c; }}
                </style>
            </head>
            <body>
                <h1>قائمة الدخل</h1>
                <p style="text-align: center;">للفترة المنتهية في: {datetime.now().strftime('%Y-%m-%d')}</p>

                <h2>الإيرادات</h2>
                <table>
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
            """

            # إضافة الإيرادات
            for revenue in revenues:
                html += f"""
                        <tr class="revenue">
                            <td>{revenue[0]}</td>
                            <td>{float(revenue[1]):,.2f}</td>
                        </tr>
                """

            html += f"""
                        <tr class="total">
                            <td>إجمالي الإيرادات</td>
                            <td>{total_revenues:,.2f}</td>
                        </tr>
                    </tbody>
                </table>

                <h2>المصروفات</h2>
                <table>
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
            """

            # إضافة المصروفات
            for expense in expenses:
                html += f"""
                        <tr class="expense">
                            <td>{expense[0]}</td>
                            <td>{float(expense[1]):,.2f}</td>
                        </tr>
                """

            net_class = "positive" if net_income >= 0 else "negative"
            net_text = "صافي الربح" if net_income >= 0 else "صافي الخسارة"

            html += f"""
                        <tr class="total">
                            <td>إجمالي المصروفات</td>
                            <td>{total_expenses:,.2f}</td>
                        </tr>
                    </tbody>
                </table>

                <table>
                    <tbody>
                        <tr class="net-income">
                            <td>{net_text}</td>
                            <td class="{net_class}">{abs(net_income):,.2f}</td>
                        </tr>
                    </tbody>
                </table>
            </body>
            </html>
            """

            self.report_display.setHtml(html)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء قائمة الدخل:\n{str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def generate_balance_sheet(self):
        """إنشاء الميزانية العمومية"""
        self.report_display.setHtml("""
        <h2 style='text-align: center;'>الميزانية العمومية</h2>
        <p style='text-align: center;'>هذا التقرير قيد التطوير</p>
        <p>سيتم عرض:</p>
        <ul>
            <li>الأصول</li>
            <li>الخصوم</li>
            <li>حقوق الملكية</li>
        </ul>
        """)

    def generate_account_statement(self):
        """إنشاء كشف حساب"""
        self.report_display.setHtml("""
        <h2 style='text-align: center;'>كشف حساب</h2>
        <p style='text-align: center;'>هذا التقرير قيد التطوير</p>
        <p>سيتم عرض:</p>
        <ul>
            <li>حركات الحساب المحدد</li>
            <li>الرصيد الجاري</li>
            <li>إجمالي المدين والدائن</li>
        </ul>
        """)

    def print_current_report(self):
        """طباعة التقرير الحالي"""
        try:
            html_content = self.report_display.toHtml()
            if not html_content or html_content.strip() == "":
                QMessageBox.warning(self, "تحذير", "لا يوجد تقرير لطباعته")
                return

            # إنشاء نافذة معاينة الطباعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setOrientation(QPrinter.Portrait)

            preview_dialog = QPrintPreviewDialog(printer, self)
            preview_dialog.setWindowTitle("معاينة طباعة التقرير")

            def print_document(printer):
                document = QTextBrowser()
                document.setHtml(html_content)
                document.print_(printer)

            preview_dialog.paintRequested.connect(print_document)
            preview_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التقرير:\n{str(e)}")

    def export_current_report(self):
        """تصدير التقرير الحالي"""
        try:
            html_content = self.report_display.toHtml()
            if not html_content or html_content.strip() == "":
                QMessageBox.warning(self, "تحذير", "لا يوجد تقرير لتصديره")
                return

            # حوار اختيار نوع التصدير
            export_dialog = QDialog(self)
            export_dialog.setWindowTitle("تصدير التقرير")
            export_dialog.setModal(True)
            export_dialog.resize(300, 200)
            export_dialog.setLayoutDirection(Qt.RightToLeft)

            layout = QVBoxLayout(export_dialog)

            # اختيار نوع التصدير
            export_type_group = QGroupBox("نوع التصدير")
            export_type_layout = QVBoxLayout(export_type_group)

            html_radio = QCheckBox("HTML")
            html_radio.setChecked(True)
            export_type_layout.addWidget(html_radio)

            pdf_radio = QCheckBox("PDF")
            export_type_layout.addWidget(pdf_radio)

            layout.addWidget(export_type_group)

            # أزرار الحوار
            buttons_layout = QHBoxLayout()

            export_btn = QPushButton("تصدير")
            export_btn.clicked.connect(lambda: self.perform_report_export(
                export_dialog, html_radio.isChecked(), pdf_radio.isChecked(), html_content
            ))
            buttons_layout.addWidget(export_btn)

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.clicked.connect(export_dialog.reject)
            buttons_layout.addWidget(cancel_btn)

            layout.addLayout(buttons_layout)

            export_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")

    def perform_report_export(self, dialog, export_html, export_pdf, html_content):
        """تنفيذ تصدير التقرير"""
        try:
            if not export_html and not export_pdf:
                QMessageBox.warning(dialog, "تحذير", "يرجى اختيار نوع التصدير")
                return

            # اختيار مجلد الحفظ
            save_dir = QFileDialog.getExistingDirectory(dialog, "اختيار مجلد الحفظ")
            if not save_dir:
                return

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            success_count = 0

            # تصدير HTML
            if export_html:
                try:
                    filename = f"تقرير_محاسبي_{timestamp}.html"
                    filepath = os.path.join(save_dir, filename)

                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(html_content)

                    success_count += 1
                except Exception as e:
                    print(f"خطأ في تصدير HTML: {e}")

            # تصدير PDF
            if export_pdf:
                try:
                    filename = f"تقرير_محاسبي_{timestamp}.pdf"
                    filepath = os.path.join(save_dir, filename)

                    # إنشاء مستند PDF من HTML
                    printer = QPrinter(QPrinter.HighResolution)
                    printer.setOutputFormat(QPrinter.PdfFormat)
                    printer.setOutputFileName(filepath)
                    printer.setPageSize(QPrinter.A4)

                    document = QTextBrowser()
                    document.setHtml(html_content)
                    document.print_(printer)

                    success_count += 1
                except Exception as e:
                    print(f"خطأ في تصدير PDF: {e}")

            if success_count > 0:
                QMessageBox.information(dialog, "نجح", f"تم تصدير {success_count} ملف بنجاح")
                dialog.accept()
            else:
                QMessageBox.critical(dialog, "خطأ", "فشل في تصدير الملفات")

        except Exception as e:
            QMessageBox.critical(dialog, "خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")


# دالة لفتح نافذة إدارة القيود المحاسبية
def open_journal_entries_window(parent=None):
    """فتح نافذة إدارة القيود المحاسبية"""
    try:
        window = JournalEntriesWindow(parent)
        window.show()
        return window
    except Exception as e:
        print(f"خطأ في فتح نافذة القيود المحاسبية: {e}")
        if parent:
            QMessageBox.critical(parent, "خطأ", f"فشل في فتح نافذة القيود المحاسبية:\n{str(e)}")
        return None


# للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # اختبار النافذة
    window = open_journal_entries_window()

    sys.exit(app.exec_())
