#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نافذة مراحل المشروع والحوار - مع اختبار الدوال المفقودة
"""

import sys
from PySide6.QtWidgets import QApplication
from مراحل_المشروع import ProjectPhasesWindow, PhaseDialog

def test_missing_methods():
    """اختبار الدوال المفقودة التي تم إضافتها"""
    app = QApplication(sys.argv)

    # بيانات مشروع تجريبية
    project_data = {
        'id': 1,
        'اسم_المشروع': 'مشروع مقاولات تجريبي',
        'معرف_العميل': 1,
        'معرف_المهندس': 1,
        'المبلغ': 50000.0,
        'المدفوع': 25000.0,
        'تاريخ_الإستلام': '2024-01-01',
        'تاريخ_التسليم': '2024-12-31',
        'الحالة': 'قيد الإنجاز',
        'وصف_المشروع': 'مشروع مقاولات تجريبي للاختبار',
        'ملاحظات': 'ملاحظات تجريبية'
    }

    try:
        # إنشاء نافذة مراحل المشروع للمقاولات
        window = ProjectPhasesWindow(None, project_data, "مقاولات")

        print("✅ تم إنشاء نافذة مراحل المشروع للمقاولات بنجاح")

        # اختبار وجود الدوال المطلوبة
        methods_to_test = [
            'insert_contractor_balance',
            'insert_all_contractor_balances',
            'filter_contractors_by_name',
            'insert_worker_balance',
            'insert_all_worker_balances',
            'filter_workers_by_name',
            'insert_supplier_balance',
            'insert_all_supplier_balances',
            'filter_suppliers_by_name',
            'manage_timeline_status',
            'filter_timeline_by_status',
            'filter_expenses_by_custody',
            'transfer_custody',
            'close_custody',
            'filter_custody_by_number',
            'load_filter_data'
        ]

        missing_methods = []
        for method_name in methods_to_test:
            if hasattr(window, method_name):
                print(f"✅ الدالة {method_name} موجودة")
            else:
                missing_methods.append(method_name)
                print(f"❌ الدالة {method_name} مفقودة")

        if missing_methods:
            print(f"\n❌ الدوال المفقودة: {missing_methods}")
            return False
        else:
            print("\n✅ جميع الدوال المطلوبة موجودة!")

        # اختبار فتح النافذة
        window.show()
        print("✅ تم فتح نافذة مراحل المشروع بنجاح")

        # إغلاق النافذة بعد ثانية واحدة للاختبار
        from PySide6.QtCore import QTimer
        QTimer.singleShot(1000, window.close)
        QTimer.singleShot(1500, app.quit)

        app.exec()
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_project_phases_window():
    """اختبار نافذة مراحل المشروع"""
    app = QApplication(sys.argv)

    # بيانات مشروع تجريبية
    project_data = {
        'id': 1,
        'اسم_المشروع': 'مشروع تجريبي',
        'معرف_العميل': 1,
        'معرف_المهندس': 1,
        'المبلغ': 10000.0,
        'المدفوع': 5000.0,
        'تاريخ_الإستلام': '2024-01-01',
        'تاريخ_التسليم': '2024-12-31',
        'الحالة': 'قيد الإنجاز',
        'وصف_المشروع': 'مشروع تجريبي للاختبار',
        'ملاحظات': 'ملاحظات تجريبية'
    }

    try:
        # إنشاء نافذة مراحل المشروع
        window = ProjectPhasesWindow(None, project_data, "تصميم")
        window.show()

        print("تم فتح نافذة مراحل المشروع بنجاح")

        # تشغيل التطبيق
        sys.exit(app.exec())

    except Exception as e:
        print(f"خطأ في فتح النافذة: {e}")
        import traceback
        traceback.print_exc()

def test_phase_dialog():
    """اختبار حوار المراحل"""
    app = QApplication(sys.argv)

    try:
        # اختبار إضافة مرحلة جديدة
        dialog = PhaseDialog(project_id=1)  # معرف مشروع تجريبي

        # عرض الحوار
        result = dialog.exec()

        if result == dialog.Accepted:
            print("تم حفظ المرحلة بنجاح")
        else:
            print("تم إلغاء العملية")

    except Exception as e:
        print(f"خطأ في فتح الحوار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 بدء اختبار الدوال المفقودة...")
    success = test_missing_methods()

    if success:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    else:
        print("\n💥 فشل في بعض الاختبارات. يرجى مراجعة الأخطاء أعلاه.")

    # اختبار النافذة الرئيسية (اختياري)
    # test_project_phases_window()

    # أو اختبار الحوار فقط (اختياري)
    # test_phase_dialog()
