# نافذة مراحل المشروع الجديدة

## نظرة عامة

تم إنشاء نافذة شاملة جديدة لإدارة مراحل المشروع تحل محل النافذة القديمة وتوفر واجهة متطورة ومتكاملة لإدارة جميع جوانب المشروع.

## الملفات المضافة/المحدثة

### الملفات الجديدة:
- `مراحل_المشروع.py` - النافذة الرئيسية الجديدة
- `test_project_phases.py` - ملف اختبار النافذة
- `README_مراحل_المشروع.md` - هذا الملف

### الملفات المحدثة:
- `منظومة_المهندس.py` - تحديث لاستخدام النافذة الجديدة
- `نظام_البطاقات.py` - تحديث لاستخدام النافذة الجديدة

## هيكل النافذة الجديدة

### التابات الأساسية (لجميع أنواع المشاريع):

#### 1. تاب معلومات المشروع (افتراضي)

**التخطيط الأفقي الجديد:**
- الحاويات الثلاث الأولى تظهر بجانب بعضها البعض في صف واحد أفقياً
- كل حاوية تأخذ عرضاً متساوياً لاستغلال أفضل للمساحة

- **أ. حاوية المعلومات الأساسية:**
  - اسم المشروع
  - اسم المالك/العميل
  - المهندس المسؤول

- **ب. حاوية المعلومات المالية:**
  - إجمالي قيمة المشروع
  - إجمالي المبلغ المدفوع (بلون أخضر #27ae60)
  - إجمالي المبلغ المتبقي (بلون أحمر #e74c3c)
  - زر "إضافة دفعة جديدة"
  - زر "عرض الدفعات"

- **ج. حاوية معلومات التوقيت والحالة:**
  - تاريخ الاستلام
  - تاريخ التسليم المتوقع
  - الوقت المتبقي (محسوب تلقائياً)
  - نسبة الإنجاز (محسوبة حسب عدد المراحل المكتملة)
  - حالة المشروع الحالية
  - زر "تحرير حالة المشروع"

- **د. حاوية الوصف والملاحظات:**
  - حقل عريض لوصف المشروع
  - حقل عريض للملاحظات

- **هـ. حاوية الإحصائيات المالية:**
  - إجمالي عدد المراحل
  - إجمالي حسابات المهندسين
  - صافي ربح الشركة

#### 2. تاب مراحل المشروع
- شريط بحث في أعلى النافذة
- جدول المراحل مع الحقول: وصف المرحلة، الوحدة، الكمية، السعر، الإجمالي، حالة المبلغ، ملاحظات
- أزرار العمليات: إضافة، تعديل، حذف

#### 3. تاب مهام المهندسين
- شريط بحث في أعلى النافذة
- جدول مهام المهندسين مع الحقول: المهندس، وصف المرحلة، % النسبة، مبلغ المهندس، حالة المبلغ
- أزرار العمليات: إضافة، تعديل، حذف

#### 4. تاب الجدول الزمني
- شريط بحث في أعلى النافذة
- جدول الجدول الزمني مع الحقول: وصف المرحلة، المهندس المسؤول، تاريخ البدء، تاريخ الانتهاء، الوقت المتبقي، الحالة
- أزرار العمليات: إضافة، تعديل، حذف

#### 5. تاب التقارير الشاملة
- أزرار لإنتاج التقارير المختلفة:
  - تقرير المراحل
  - تقرير المهندسين
  - تقرير الجدول الزمني
  - تقرير مالي شامل

#### 6. تاب الملفات والمرفقات (جديد - متاح لجميع أنواع المشاريع)
- شريط بحث في أعلى النافذة
- جدول الملفات والمرفقات مع الأعمدة:
  - اسم الملف
  - نوع الملف (صورة، مستند، جدول بيانات، عرض تقديمي، ملف CAD، ملف عام)
  - الوصف
  - تاريخ الإضافة
  - حجم الملف
- أزرار العمليات مع ألوان مميزة:
  - **إضافة ملف** (أخضر): رفع ملفات جديدة مع إمكانية إضافة وصف
  - **عرض ملف** (أزرق): فتح الملف بالتطبيق الافتراضي
  - **تحميل ملف** (برتقالي): حفظ نسخة من الملف في مكان آخر
  - **حذف ملف** (أحمر): حذف الملف نهائياً مع تأكيد
- مميزات إضافية:
  - تصنيف تلقائي لأنواع الملفات حسب الامتداد
  - عرض حجم الملف بتنسيق قابل للقراءة (B, KB, MB, GB)
  - حفظ الملفات في مجلد منظم حسب رقم المشروع
  - حماية من تضارب أسماء الملفات
  - تصفية وبحث في جميع معلومات الملفات

### التابات الإضافية للمقاولات:

#### 6. تاب المصروفات
- شريط بحث
- جدول المصروفات مع جميع التفاصيل المالية
- أزرار العمليات

#### 7. تاب العهد المالية
- شريط بحث
- جدول العهد المالية مع تفاصيل العهد
- أزرار العمليات

#### 8. تاب دفعات العهد
- شريط بحث
- جدول دفعات العهد
- أزرار العمليات

#### 9. تاب المقاولين
- شريط بحث
- جدول المقاولين مع معلومات الاتصال والتقييم
- أزرار العمليات

#### 10. تاب العمال
- شريط بحث
- جدول العمال مع الرواتب والحسابات
- أزرار العمليات

#### 11. تاب الموردين
- شريط بحث
- جدول الموردين مع معلومات التوريد
- أزرار العمليات

## المميزات الجديدة

### 1. التصميم المحسن والمتجاوب
- **تخطيط أفقي جديد**: الحاويات الثلاث الأولى في تاب معلومات المشروع تظهر جنباً إلى جنب
- واجهة RTL متوافقة مع اللغة العربية
- استخدام نظام الألوان المعتمد في التطبيق
- تصميم حديث ومتسق مع باقي أجزاء التطبيق
- استغلال أفضل للمساحة المتاحة

### 2. التكامل مع قاعدة البيانات
- ربط مباشر مع الجداول الجديدة:
  - `المشاريع_المراحل`
  - `مشاريع_مراحل_مهندسين`
  - `المشاريع_الجدول_الزمني`
  - `المقاولات_العهد`
  - `المقاولات_دفعات_العهد`
  - `المقاولات_مصروفات_العهد`

### 3. الحسابات التلقائية
- حساب الوقت المتبقي تلقائياً
- حساب نسبة الإنجاز بناءً على المراحل المكتملة
- حساب صافي ربح الشركة
- تحديث البيانات في الوقت الفعلي

### 4. نظام البحث والتصفية
- شريط بحث في كل تاب (باستثناء معلومات المشروع)
- تصفية فورية للبيانات
- بحث في جميع الحقول

### 5. نظام إدارة الملفات والمرفقات (جديد)
- **رفع وإدارة الملفات**: إمكانية رفع أي نوع من الملفات مع وصف مخصص
- **تصنيف تلقائي**: تصنيف الملفات حسب النوع (صور، مستندات، ملفات CAD، إلخ)
- **عرض وتحميل**: فتح الملفات بالتطبيق الافتراضي أو تحميل نسخ منها
- **تنظيم هيكلي**: حفظ الملفات في مجلدات منظمة حسب رقم المشروع
- **بحث وتصفية**: بحث سريع في أسماء وأوصاف الملفات
- **إدارة آمنة**: حماية من تضارب الأسماء وتأكيد قبل الحذف

### 6. إدارة شاملة للمقاولات
- تابات مخصصة للمقاولات
- إدارة العهد المالية ودفعاتها
- إدارة المقاولين والعمال والموردين
- تتبع المصروفات والتكاليف

## كيفية الاستخدام

### فتح النافذة الجديدة:
```python
from مراحل_المشروع import open_project_phases_window

# بيانات المشروع
project_data = {
    'id': 1,
    'اسم_المشروع': 'اسم المشروع',
    'نوع_المشروع': 'تصميم',  # أو 'مقاولات'
    # ... باقي البيانات
}

# فتح النافذة
window = open_project_phases_window(parent, project_data, "تصميم")
```

### اختبار النافذة:
```bash
python test_project_phases.py
```

## التكامل مع النظام الحالي

تم تحديث الملفات التالية لاستخدام النافذة الجديدة:

1. **منظومة_المهندس.py**: تحديث معالجة زر "عرض" في أقسام المشاريع والمقاولات
2. **نظام_البطاقات.py**: تحديث أزرار العرض والإدارة في البطاقات

## الحالة الحالية

### مكتمل:
- ✅ هيكل النافذة الأساسي
- ✅ جميع التابات المطلوبة
- ✅ **تخطيط أفقي محسن** للحاويات في تاب معلومات المشروع
- ✅ **تاب الملفات والمرفقات الجديد** مع إمكانيات كاملة
- ✅ واجهة المستخدم والتصميم
- ✅ التكامل مع النظام الحالي
- ✅ تحميل البيانات الأساسية
- ✅ الحسابات التلقائية
- ✅ نظام إدارة الملفات المتكامل

### قيد التطوير:
- 🔄 تنفيذ دوال CRUD للجداول
- 🔄 نوافذ الإضافة والتعديل
- 🔄 إنتاج التقارير
- 🔄 طباعة البيانات

### مخطط للمستقبل:
- 📋 تحسين الأداء
- 📋 إضافة المزيد من التقارير
- 📋 تحسين واجهة المستخدم
- 📋 إضافة ميزات متقدمة

## ملاحظات مهمة

1. النافذة الجديدة تحل محل النافذة القديمة تدريجياً
2. في حالة فشل تحميل النافذة الجديدة، يتم الرجوع للنافذة القديمة تلقائياً
3. جميع البيانات متوافقة مع هيكل قاعدة البيانات الجديد
4. التصميم متسق مع نمط التطبيق الحالي

## المتطلبات

- Python 3.8+
- PySide6
- MySQL Connector
- QtAwesome (للأيقونات)
- جميع مكتبات المشروع الحالية
