#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار قوائم المراحل المختلفة حسب نوع المشروع
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QDialog

# إضافة المسار الحالي لـ Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_phase_dialog_types():
    """اختبار قوائم المراحل المختلفة"""
    try:
        # إنشاء QApplication أولاً
        app = QApplication(sys.argv)

        from مراحل_المشروع import PhaseDialog

        print("🧪 اختبار قوائم المراحل حسب نوع المشروع...")

        # اختبار مراحل التصميم
        print("\n📐 اختبار مراحل التصميم:")
        design_dialog = PhaseDialog(project_id=1, project_type="تصميم")
        design_phases = design_dialog.get_phases_list_by_type()
        print(f"عدد مراحل التصميم: {len(design_phases)}")
        for i, phase in enumerate(design_phases[:5], 1):  # عرض أول 5 مراحل
            print(f"  {i}. {phase}")
        if len(design_phases) > 5:
            print(f"  ... و {len(design_phases) - 5} مراحل أخرى")
        
        # اختبار مراحل المقاولات
        print("\n🏗️ اختبار مراحل المقاولات:")
        contracting_dialog = PhaseDialog(project_id=1, project_type="مقاولات")
        contracting_phases = contracting_dialog.get_phases_list_by_type()
        print(f"عدد مراحل المقاولات: {len(contracting_phases)}")
        for i, phase in enumerate(contracting_phases[:5], 1):  # عرض أول 5 مراحل
            print(f"  {i}. {phase}")
        if len(contracting_phases) > 5:
            print(f"  ... و {len(contracting_phases) - 5} مراحل أخرى")
        
        # التحقق من أن القوائم مختلفة
        if design_phases != contracting_phases:
            print("\n✅ القوائم مختلفة بنجاح حسب نوع المشروع")
        else:
            print("\n❌ القوائم متطابقة - هناك مشكلة!")
            return False
        
        # التحقق من وجود مراحل مقاولات محددة
        contracting_specific_phases = [
            "حفر الأساسات", "صب الخرسانة المسلحة", "بناء الجدران", 
            "أعمال البلاط", "أعمال الدهان"
        ]
        
        missing_phases = []
        for phase in contracting_specific_phases:
            if phase not in contracting_phases:
                missing_phases.append(phase)
        
        if missing_phases:
            print(f"\n❌ مراحل مقاولات مفقودة: {missing_phases}")
            return False
        else:
            print("\n✅ جميع مراحل المقاولات المطلوبة موجودة")
        
        # التحقق من وجود مراحل تصميم محددة
        design_specific_phases = [
            "الرفع المساحي", "تصميم 2D", "تصميم 3D", 
            "تصميم إنشائي", "خرائط كهربائية"
        ]
        
        missing_design_phases = []
        for phase in design_specific_phases:
            if phase not in design_phases:
                missing_design_phases.append(phase)
        
        if missing_design_phases:
            print(f"\n❌ مراحل تصميم مفقودة: {missing_design_phases}")
            return False
        else:
            print("\n✅ جميع مراحل التصميم المطلوبة موجودة")
        
        print("\n🎉 جميع الاختبارات نجحت!")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_phase_dialog_creation():
    """اختبار إنشاء حوار المراحل مع أنواع مختلفة"""
    try:
        from مراحل_المشروع import PhaseDialog
        
        print("\n🧪 اختبار إنشاء حوار المراحل...")
        
        # اختبار إنشاء حوار للتصميم
        print("📐 إنشاء حوار للتصميم...")
        design_dialog = PhaseDialog(project_id=1, project_type="تصميم")
        print("✅ تم إنشاء حوار التصميم بنجاح")
        
        # اختبار إنشاء حوار للمقاولات
        print("🏗️ إنشاء حوار للمقاولات...")
        contracting_dialog = PhaseDialog(project_id=1, project_type="مقاولات")
        print("✅ تم إنشاء حوار المقاولات بنجاح")
        
        # التحقق من أن ComboBox يحتوي على القوائم الصحيحة
        design_combo_count = design_dialog.phase_name_combo.count()
        contracting_combo_count = contracting_dialog.phase_name_combo.count()
        
        print(f"📊 عدد عناصر ComboBox للتصميم: {design_combo_count}")
        print(f"📊 عدد عناصر ComboBox للمقاولات: {contracting_combo_count}")
        
        if design_combo_count > 0 and contracting_combo_count > 0:
            print("✅ ComboBox يحتوي على عناصر في كلا النوعين")
        else:
            print("❌ ComboBox فارغ في أحد الأنواع أو كليهما")
            return False
        
        # عرض بعض العناصر من كل ComboBox
        print("\n📋 عناصر ComboBox للتصميم (أول 3):")
        for i in range(min(3, design_combo_count)):
            item_text = design_dialog.phase_name_combo.itemText(i)
            print(f"  {i+1}. {item_text}")
        
        print("\n📋 عناصر ComboBox للمقاولات (أول 3):")
        for i in range(min(3, contracting_combo_count)):
            item_text = contracting_dialog.phase_name_combo.itemText(i)
            print(f"  {i+1}. {item_text}")
        
        print("\n🎉 جميع اختبارات إنشاء الحوار نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء الحوار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار قوائم المراحل المختلفة...")
    
    # اختبار الدوال
    functions_success = test_phase_dialog_types()
    
    # اختبار إنشاء الحوار
    creation_success = test_phase_dialog_creation()
    
    if functions_success and creation_success:
        print("\n🎊 جميع الاختبارات نجحت!")
        print("✅ قوائم المراحل تعمل بشكل صحيح حسب نوع المشروع")
        print("✅ حوار إضافة المراحل جاهز للاستخدام")
    else:
        print("\n💥 فشل في بعض الاختبارات")
        sys.exit(1)
