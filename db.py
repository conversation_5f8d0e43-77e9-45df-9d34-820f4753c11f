# cSpell:disable
from ui_boton import*
from ستايل import*
from for_all import*

#-------------------------قاعدة البيانات الرئيسية- ----------------------
#قاعدة البيانات///////////////////////////////////////////////////////
def create_database_if_not_exists(self, year):
        db_name = f"project_manager_V2"
        conn = None
        cursor = None
        try:
            conn = self.get_root_connection()
            if conn is None:
                return False

            cursor = conn.cursor()

            cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;")
            cursor.execute(f"USE `{db_name}`")

            def create_index_if_not_exists(cursor, index_name, table_name, column_name):
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                if not cursor.fetchone():
                    print(f"Table {table_name} does not exist. Skipping index creation.")
                    return # الجدول غير موجود، لا يمكن إنشاء الفهرس

                # Check if index exists using information_schema
                try:
                    cursor.execute("""
                        SELECT COUNT(*)
                        FROM INFORMATION_SCHEMA.STATISTICS
                        WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND INDEX_NAME = %s
                    """, (db_name, table_name, index_name))
                    if cursor.fetchone()[0] == 0:
                        try:
                            cursor.execute(f"CREATE INDEX `{index_name}` ON `{table_name}`(`{column_name}`)")
                            print(f"Index {index_name} created on {table_name}({column_name}).")
                        except mysql.connector.Error as e:
                             print(f"Warning: Could not create index {index_name} on {table_name}({column_name}): {e}")
                    # else:
                    #      print(f"Index {index_name} already exists on {table_name}. Skipping.")
                except mysql.connector.Error as e:
                     print(f"Warning: Could not check for index {index_name} existence: {e}")

            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `التصنيفات` (
                `id` INT PRIMARY KEY AUTO_INCREMENT,
                `اسم_القسم` VARCHAR(100) NOT NULL,
                `اسم_التصنيف` VARCHAR(100) NOT NULL,
                `لون_التصنيف` VARCHAR(20) DEFAULT '#3498db',
                `وصف_التصنيف` TEXT, 
                `تاريخ_الإنشاء` DATETIME DEFAULT CURRENT_TIMESTAMP,
                `المستخدم` VARCHAR(50),
                `حالة_التصنيف` ENUM('نشط', 'غير نشط') DEFAULT 'نشط',
                UNIQUE KEY `unique_section_category` (`اسم_القسم`, `اسم_التصنيف`),
                INDEX `idx_اسم_القسم` (`اسم_القسم`),
                INDEX `idx_اسم_التصنيف` (`اسم_التصنيف`)
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            # العملاء------------------------------------------
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `العملاء` (
                    `id` INTEGER PRIMARY KEY AUTO_INCREMENT,
                    `التصنيف` VARCHAR(255),
                    `اسم_العميل` VARCHAR(255),
                    `العنوان` VARCHAR(255),
                    `رقم_الهاتف` VARCHAR(255),
                    `الإيميل` VARCHAR(255),
                    `تاريخ_الإنشاء` DATE,
                    `ملاحظات` VARCHAR(255),
                           
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `السنة` INT GENERATED ALWAYS AS (YEAR(`تاريخ_الإنشاء`)) STORED,
                    INDEX `idx_اسم_العميل` (`اسم_العميل`),
                    INDEX `idx_المستخدم` (`المستخدم`),
                    INDEX `idx_السنة` (`السنة`)
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            # الموظفين------------------------------------------          
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `الموظفين` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `التصنيف` VARCHAR(255),
                    `اسم_الموظف` VARCHAR(255),
                    `الوظيفة` VARCHAR(255),
                    `الهاتف` VARCHAR(255),
                    `العنوان` VARCHAR(255),
                    `تاريخ_التوظيف` DATE,
                    `المرتب` DECIMAL(10,2),
                    `النسبة` DECIMAL(5,2),
                    `الرصيد` DECIMAL(10,2) DEFAULT 0,
                    `الحالة` ENUM('مستقيل', 'تم فصله','إجازة', 'غير نشط', 'نشط') NOT NULL DEFAULT 'نشط',
                    `ملاحظات` TEXT,
                           
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `السنة` INT GENERATED ALWAYS AS (YEAR(`تاريخ_التوظيف`)) STORED,
                    INDEX `idx_المستخدم` (`المستخدم`),
                    INDEX `idx_السنة` (`السنة`)

                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            # معاملات مالية الموظفين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `الموظفين_معاملات_مالية` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `معرف_الموظف` INT,
                           
                    `نوع_العملية` ENUM(
                        'إيداع', 
                        'سحب',
                        'خصم' 
                    ) NOT NULL,
                           
                    `نوع_المعاملة` ENUM(
                        'إيداع مرتب',
                        'إيداع مبلغ محدد',
                        'إيداع نسبة%',
                        'سحب مبلغ محدد',
                        'خصم مباشر',
                        'خصم نسبة%'
                    ) NOT NULL,
                           
                    `المبلغ` DECIMAL(10,2) NOT NULL,
                    `التاريخ` DATE NOT NULL,
                    `الوصف` VARCHAR(255),

                    `معرف_الجدولة` INT NULL COMMENT 'id الجدولة التلقائية إن وجدت',
                    `معاملة_تلقائية` BOOLEAN DEFAULT FALSE,
                           
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `السنة` INT GENERATED ALWAYS AS (YEAR(`التاريخ`)) STORED,
                    
                    INDEX `idx_معرف_الموظف` (`معرف_الموظف`),
                    INDEX `idx_نوع_العملية` (`نوع_العملية`),
                    INDEX `idx_نوع_المعاملة` (`نوع_المعاملة`),
                    INDEX `idx_التاريخ` (`التاريخ`),
                    INDEX `idx_المستخدم` (`المستخدم`),
                    INDEX `idx_السنة` (`السنة`),
                           
                    CONSTRAINT `fk_الموظفين_معاملات_مالية_معرف_الموظف`
                    FOREIGN KEY (`معرف_الموظف`)
                    REFERENCES `الموظفين`(`id`)
                    ON DELETE CASCADE
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            # جدولة مرتبات الموظفين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `الموظفين_جدولة_المرتبات` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `معرف_الموظف` INT NOT NULL,
                    `مبلغ_المرتب` DECIMAL(10,2) NOT NULL,
                    `نوع_الجدولة` ENUM('إدراج شهري تلقائي', 'يدوي') NOT NULL,
                    `تاريخ_البداية` DATE NOT NULL,
                    `تاريخ_آخر_إيداع` DATE NULL,
                    `تاريخ_الإيداع_التالي` DATE NULL,
                    `نشط` BOOLEAN DEFAULT TRUE,
                    `عدد_الإيداعات_المنجزة` INT DEFAULT 0,
                    `ملاحظات` TEXT,
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإنشاء` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `تاريخ_التحديث` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX `idx_معرف_الموظف` (`معرف_الموظف`),
                    INDEX `idx_نوع_الجدولة` (`نوع_الجدولة`),
                    INDEX `idx_تاريخ_الإيداع_التالي` (`تاريخ_الإيداع_التالي`),
                    INDEX `idx_نشط` (`نشط`),
                    INDEX `idx_المستخدم` (`المستخدم`),
                    CONSTRAINT `fk_جدولة_المرتبات_معرف_الموظف`
                    FOREIGN KEY (`معرف_الموظف`)
                    REFERENCES `الموظفين`(`id`)
                    ON DELETE CASCADE
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            # تقييم الموظفين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `الموظفين_التقييم` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `معرف_الموظف` INT,
                    `حالة_التسليم` ENUM('تسليم متأخر', 'لم يتم التسليم','قبل الموعد', 'في الموعد') NOT NULL,
                    `النقاط` INT,

                    CONSTRAINT `fk_تقييم_المهندسين_معرف_المهندس`
                    FOREIGN KEY (`معرف_الموظف`)
                    REFERENCES `الموظفين`(`id`)
                    ON DELETE CASCADE
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            # جدول حضور وانصراف الموظفين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `الموظفين_الحضور_والانصراف` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `معرف_الموظف` INT NOT NULL,
                    `التاريخ` DATE NOT NULL,
                    `وقت_الحضور` TIME,
                    `وقت_الانصراف` TIME,
                    
                    `حضور_مبكر` BOOLEAN DEFAULT FALSE,
                    `حضور_متأخر` BOOLEAN DEFAULT FALSE,
                       
                    `انصراف_مبكر` BOOLEAN DEFAULT FALSE,
                    `إنصراف_متأخر` BOOLEAN DEFAULT FALSE,
                           
                    `ملاحظات` TEXT,
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `تاريخ_التحديث` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    INDEX `idx_معرف_الموظف` (`معرف_الموظف`),
                    INDEX `idx_التاريخ` (`التاريخ`),
                    
                    CONSTRAINT `fk_الحضور_والانصراف_الموظف`
                    FOREIGN KEY (`معرف_الموظف`)
                    REFERENCES `الموظفين`(`id`)
                    ON DELETE CASCADE
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            # جدول تسجيل مهام الموظفين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `الموظفين_المهام` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `معرف_الموظف` INT NOT NULL,
                    `عنوان_المهمة` VARCHAR(255) NOT NULL,
                    `وصف_المهمة` TEXT,
                    `تاريخ_البداية` DATE,
                    `تاريخ_الانتهاء` DATE,
                    `الحالة` ENUM('قيد التنفيذ', 'مكتملة', 'ملغاة', 'متأخرة') DEFAULT 'قيد التنفيذ',
                    `ملاحظات` TEXT,
                           
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `تاريخ_التحديث` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                    INDEX `idx_معرف_الموظف` (`معرف_الموظف`),
                    INDEX `idx_الحالة` (`الحالة`),
                    INDEX `idx_تاريخ_البداية` (`تاريخ_البداية`),

                    CONSTRAINT `fk_المهام_الموظف`
                    FOREIGN KEY (`معرف_الموظف`)
                    REFERENCES `الموظفين`(`id`)
                    ON DELETE CASCADE
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)


            # المشاريع------------------------------------------
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `المشاريع` (
                    `id` INTEGER PRIMARY KEY AUTO_INCREMENT,
                    `نوع_المشروع` ENUM('تصميم', 'مقاولات') DEFAULT 'تصميم',
                    `التصنيف` VARCHAR(255),
                    `معرف_العميل` INT,
                    `معرف_المهندس` INT,
                           
                    `اسم_المشروع` VARCHAR(255),
                    `وصف_المشروع` TEXT,
                    
                    `المبلغ` DECIMAL(10,2),
                    `المدفوع` DECIMAL(10,2) DEFAULT 0,
                    `الباقي` DECIMAL(10,2) GENERATED ALWAYS AS (`المبلغ` - `المدفوع`) STORED,
                           
                    `تاريخ_الإستلام` DATE,
                    `تاريخ_التسليم` DATE,  
                    `الحالة` ENUM('معلق ', 'منتهي', 'تم التسليم', 'متوقف', 'تأكيد التسليم', 'قيد الإنجاز') NOT NULL DEFAULT 'قيد الإنجاز',
                    `ملاحظات` TEXT,

                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `السنة` INT GENERATED ALWAYS AS (YEAR(`تاريخ_التسليم`)) STORED,
                           
                    INDEX `idx_المستخدم` (`المستخدم`),
                    INDEX `idx_السنة` (`السنة`),
                           
                    CONSTRAINT fk_العميل FOREIGN KEY (`معرف_العميل`) REFERENCES `العملاء`(`id`) ON DELETE CASCADE

                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)
            
            # دفعات المشاريع
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `المشاريع_المدفوعات` (
                    `id` INTEGER PRIMARY KEY AUTO_INCREMENT,
                    `معرف_العميل` INT,
                    `معرف_المشروع` INT,
                           
                    `المبلغ_المدفوع` DECIMAL(10,2),    
                    `وصف_المدفوع` VARCHAR(255),
                    `تاريخ_الدفع` DATE,
                    `طريقة_الدفع` VARCHAR(255),
                    `خصم` DECIMAL(10,2), 
                    `المستلم` VARCHAR(255),
                           
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `السنة` INT GENERATED ALWAYS AS (YEAR(`تاريخ_الدفع`)) STORED,
                           
                    INDEX `idx_المستخدم` (`المستخدم`),
                    INDEX `idx_السنة` (`السنة`),

                    CONSTRAINT `fk_دفعات_المشاريع_معرف_المشروع`
                    FOREIGN KEY (`معرف_المشروع`) REFERENCES `المشاريع`(`id`) ON DELETE CASCADE
                           
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)
            
            # مراحل المشروع
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `المشاريع_المراحل` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `معرف_العميل` INT,
                    `معرف_المشروع` INT,
                    `اسم_المرحلة` VARCHAR(255),
                    `وصف_المرحلة` VARCHAR(255),
                    `الوحدة` VARCHAR(255),
                    `الكمية` INT,
                    `السعر` DECIMAL(10,2),
                    `الإجمالي` DECIMAL(10,2) GENERATED ALWAYS AS (`الكمية` * `السعر`) STORED,
                    `حالة_المبلغ` ENUM('غير مدرج', 'تم الإدراج') NOT NULL DEFAULT 'غير مدرج',
                    `ملاحظات` TEXT,                  
                    
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `السنة` INT,

                    INDEX `idx_المستخدم` (`المستخدم`),
                    INDEX `idx_السنة` (`السنة`),
                    INDEX `idx_معرف_العميل` (`معرف_العميل`),

                    CONSTRAINT `fk_مرحلة_معرف_العميل`
                    FOREIGN KEY (`معرف_العميل`)
                    REFERENCES `المشاريع`(`id`)
                    ON DELETE CASCADE
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            # ========================
            # جدول الربط مشاريع_مراحل_مهندسين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `المشاريع_مهام_المهندسين` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `معرف_المرحلة` INT NOT NULL,
                    `معرف_المهندس` INT NOT NULL,
                    `نسبة_المهندس` INT DEFAULT NULL,
                    `مبلغ_المهندس` DECIMAL(10,2) DEFAULT NULL,
                    `حالة_مبلغ_المهندس` ENUM('غير مدرج', 'تم الإدراج') NOT NULL DEFAULT 'غير مدرج',
                    `ملاحظات` TEXT, 
                    
                    `تاريخ_البداية` DATE,
                    `تاريخ_النهاية` DATE,
                    `الحالة` ENUM('لم يبدأ', 'قيد التنفيذ', 'منتهي', 'متوقف') NOT NULL DEFAULT 'لم يبدأ',
                    
                    
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    
                    INDEX `idx_معرف_المرحلة` (`معرف_المرحلة`),
                    INDEX `idx_معرف_المهندس` (`معرف_المهندس`),
                           
                    UNIQUE KEY `unq_مرحلة_مهندس` (`معرف_المرحلة`, `معرف_المهندس`),

                    CONSTRAINT `fk_رابط_مرحلة`
                    FOREIGN KEY (`معرف_المرحلة`)
                    REFERENCES `المشاريع_المراحل`(`id`)
                    ON DELETE CASCADE,

                    CONSTRAINT `fk_رابط_مهندس`
                    FOREIGN KEY (`معرف_المهندس`)
                    REFERENCES `الموظفين`(`id`)
                    ON DELETE CASCADE
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)
            
            # العهد المالية المقاولات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `المقاولات_العهد` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `معرف_العميل` INT NOT NULL,
                    `معرف_المشروع` INT NOT NULL, 
                              
                    `رقم_العهدة` VARCHAR(50),       
                    `وصف_العهدة` VARCHAR(50),
                    `مبلغ_العهدة` DECIMAL(15,2) NOT NULL,
                    `نسبة_المكتب` DECIMAL(5,2) DEFAULT 0,
                    `تاريخ_الاستلام` DATE NOT NULL,
                    `حالة_العهدة` ENUM('مفتوحة', 'مغلقة', 'مرحلة') DEFAULT 'مفتوحة',
                    `ملاحظات` TEXT,
                                               
                    `مبلغ_نسبة_المكتب` DECIMAL(15,2) GENERATED ALWAYS AS (`مبلغ_العهدة` * `نسبة_المكتب` / 100) STORED,
                    `المبلغ_الصافي` DECIMAL(15,2) GENERATED ALWAYS AS (`مبلغ_العهدة` - `مبلغ_نسبة_المكتب`) STORED,
                 
                    `المصروف` DECIMAL(15,2) DEFAULT 0,
                    `مبلغ_المكتب_من_المصروف` DECIMAL(15,2) GENERATED ALWAYS AS (`المصروف` * `نسبة_المكتب` / 100) STORED,
                    `المتبقي` DECIMAL(15,2) GENERATED ALWAYS AS (`مبلغ_العهدة` - `مبلغ_المكتب_من_المصروف`-`المصروف`) STORED,
                    
                           
                    `تاريخ_الإغلاق` DATE NULL,                           
                    `معرف_العهدة_السابقة` INT NULL,
                    `مبلغ_مرحل_من_السابقة` DECIMAL(15,2) DEFAULT 0,
                    
                           
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `السنة` INT,

                    INDEX `idx_رقم_العهدة` (`رقم_العهدة`),
                    INDEX `idx_معرف_المشروع` (`معرف_المشروع`),
                    INDEX `idx_حالة_العهدة` (`حالة_العهدة`),
                    INDEX `idx_المستخدم` (`المستخدم`),
                    INDEX `idx_السنة` (`السنة`),

                    CONSTRAINT `fk_العهد_المالية_معرف_المقاولات`
                    FOREIGN KEY (`معرف_المشروع`)
                    REFERENCES `المشاريع`(`id`)
                    ON DELETE CASCADE,

                    CONSTRAINT `fk_العهد_المالية_معرف_العهدة_السابقة`
                    FOREIGN KEY (`معرف_العهدة_السابقة`)
                    REFERENCES `المقاولات_العهد`(`id`)
                    ON DELETE SET NULL
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            # دفعات العهد المالية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `المقاولات_دفعات_العهد` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `معرف_العهدة` INT NOT NULL,
                    `رقم_العهدة` VARCHAR(50),
                    `وصف_الدفعة` VARCHAR(255) NOT NULL,
                    `المبلغ` DECIMAL(15,2) NOT NULL,
                    `تاريخ_الدفعة` DATE NOT NULL,
                    `نوع_الدفعة` ENUM('دفعة_أولى', 'دفعة_إضافية', 'مبلغ_مرحل') DEFAULT 'دفعة_إضافية',
                    `معرف_العهدة_المصدر` INT NULL COMMENT 'id العهدة المصدر في حالة الترحيل',
                    `طريقة_الدفع` VARCHAR(100),
                    `المستلم` VARCHAR(255),
                    `ملاحظات` TEXT,
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `السنة` INT,

                    INDEX `idx_معرف_العهدة` (`معرف_العهدة`),
                    INDEX `idx_رقم_العهدة` (`رقم_العهدة`),
                    INDEX `idx_تاريخ_الدفعة` (`تاريخ_الدفعة`),
                    INDEX `idx_نوع_الدفعة` (`نوع_الدفعة`),
                    INDEX `idx_المستخدم` (`المستخدم`),
                    INDEX `idx_السنة` (`السنة`),

                    CONSTRAINT `fk_دفعات_العهد_معرف_العهدة`
                    FOREIGN KEY (`معرف_العهدة`)
                    REFERENCES `المقاولات_العهد`(`id`)
                    ON DELETE CASCADE,

                    CONSTRAINT `fk_دفعات_العهد_معرف_العهدة_المصدر`
                    FOREIGN KEY (`معرف_العهدة_المصدر`)
                    REFERENCES `المقاولات_العهد`(`id`)
                    ON DELETE SET NULL
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)
            
            #تحويلات العهد
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `المقاولات_تحويلات_العهد` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `معرف_العهدة_المصدر` INT NOT NULL,
                    `معرف_العهدة_الهدف` INT NOT NULL,
                    `المبلغ_المحول` DECIMAL(15,2) NOT NULL,
                    `تاريخ_التحويل` DATE NOT NULL,
                    `سبب_التحويل` VARCHAR(255),
                    `ملاحظات` TEXT,
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,

                    INDEX `idx_معرف_العهدة_المصدر` (`معرف_العهدة_المصدر`),
                    INDEX `idx_معرف_العهدة_الهدف` (`معرف_العهدة_الهدف`),
                    INDEX `idx_تاريخ_التحويل` (`تاريخ_التحويل`),
                    INDEX `idx_المستخدم` (`المستخدم`),

                    CONSTRAINT `fk_تحويلات_العهد_المصدر`
                    FOREIGN KEY (`معرف_العهدة_المصدر`)
                    REFERENCES `المقاولات_العهد`(`id`)
                    ON DELETE CASCADE,

                    CONSTRAINT `fk_تحويلات_العهد_الهدف`
                    FOREIGN KEY (`معرف_العهدة_الهدف`)
                    REFERENCES `المقاولات_العهد`(`id`)
                    ON DELETE CASCADE
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            # جدول مصروفات المشروع 
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `المقاولات_مصروفات_العهد` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `معرف_المشروع` INT NOT NULL,
                    `نوع_المصروف` ENUM('مرتبط_بعهدة', 'غير_مرتبط_بعهدة') NOT NULL DEFAULT 'غير_مرتبط_بعهدة',
                    `معرف_العهدة` INT NULL,
                    `رقم_العهدة` VARCHAR(50) NULL,
                    `وصف_المصروف` VARCHAR(255) NOT NULL,
                    `المبلغ` DECIMAL(15,2) NOT NULL,
                    `تاريخ_المصروف` DATE NOT NULL,
                    `المستلم` VARCHAR(255),
                    `طريقة_الدفع` VARCHAR(100),
                    `رقم_الفاتورة` VARCHAR(100),
                    `المورد` VARCHAR(255),
                    `فئة_المصروف` VARCHAR(100),
                    `ملاحظات` TEXT,
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `السنة` INT,
                    `حالة_المصروف` ENUM('معتمد', 'معلق', 'مرفوض') DEFAULT 'معتمد',
                    
                    INDEX `idx_معرف_المشروع` (`معرف_المشروع`),
                    INDEX `idx_نوع_المصروف` (`نوع_المصروف`),
                    INDEX `idx_معرف_العهدة` (`معرف_العهدة`),
                    INDEX `idx_تاريخ_المصروف` (`تاريخ_المصروف`),
                    INDEX `idx_فئة_المصروف` (`فئة_المصروف`),
                    INDEX `idx_حالة_المصروف` (`حالة_المصروف`),
                    INDEX `idx_المستخدم` (`المستخدم`),
                    INDEX `idx_السنة` (`السنة`),
                    
                    CONSTRAINT `fk_مصروفات_المشروع_معرف_المشروع`
                    FOREIGN KEY (`معرف_المشروع`)
                    REFERENCES `المشاريع`(`id`)
                    ON DELETE CASCADE,
                    
                    CONSTRAINT `fk_مصروفات_المشروع_معرف_العهدة`
                    FOREIGN KEY (`معرف_العهدة`)
                    REFERENCES `المقاولات_العهد`(`id`)
                    ON DELETE SET NULL
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            # جدول مرفقات وملفات المشروع 
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `المشاريع_مرفقات` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `معرف_المشروع` INT NOT NULL,
                    `اسم_الملف` VARCHAR(50) NULL,
                    ` نوع_الملف` VARCHAR(255) NOT NULL,
                    `الوصف` DECIMAL(15,2) NOT NULL,
                    `المسار` DECIMAL(15,2) NOT NULL,
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
               
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            
            # الحسابات------------------------------------------
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `الحسابات` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `التصنيف` VARCHAR(255),
                    `المصروف` VARCHAR(255),
                    `المبلغ` DECIMAL(10,2),
                    `تاريخ_المصروف` DATE,
                    `المستلم` VARCHAR(255),
                    `رقم_الهاتف` VARCHAR(255),
                    `رقم_الفاتورة` VARCHAR(255),
                    `ملاحظات` TEXT,

                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `السنة` INT GENERATED ALWAYS AS (YEAR(`تاريخ_المصروف`)) STORED,
                    INDEX `idx_المستخدم` (`المستخدم`),
                    INDEX `idx_السنة` (`السنة`)

                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            # # الموردين
            # cursor.execute("""
            #     CREATE TABLE IF NOT EXISTS `الحسابات_الموردين` (
            #         `id` INT PRIMARY KEY AUTO_INCREMENT,
            #         `التصنيف` VARCHAR(255),
            #         `اسم_المورد` VARCHAR(255),
            #         `رقم_الهاتف` VARCHAR(255),
            #         `العنوان` VARCHAR(255),
            #         `ملاحظات` TEXT,

            #         `المستخدم` VARCHAR(50),
            #         `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
            #         `السنة` INT,
            #         INDEX `idx_المستخدم` (`المستخدم`),
            #         INDEX `idx_السنة` (`السنة`)
            #     ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            # """)

            # # سجل الديون
            # cursor.execute("""
            #     CREATE TABLE IF NOT EXISTS `المصروفات_سجل _الديون` (
            #         `id` INT PRIMARY KEY AUTO_INCREMENT,
            #         `معرف_الحساب` INT,
            #         `اسم_الحساب` VARCHAR(255),
            #         `نوع_الحساب` VARCHAR(255),
            #         `الوصف` VARCHAR(255),
            #         `المبلغ` DECIMAL(10,2),
            #         `تاريخ_الدين` DATE,
            #         `تاريخ_السداد` DATE,
            #         `ملاحظات` TEXT
            #     ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            # """)


            #العقارات-------------------------------------
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `العقارات` (
                    `id` INTEGER PRIMARY KEY AUTO_INCREMENT,
                    `التصنيف` VARCHAR(255),
                    `وصف_العقار` VARCHAR(255),
                    `المالك` VARCHAR(255),
                    `الموقع` VARCHAR(255),
                    `نوع_العقد` VARCHAR(255),
                    `السعر_المطلوب` DECIMAL(10,2),
                    `التاريخ` DATE,
                    `الحالة` ENUM('متاح', 'محجوز', 'مباع', 'مؤجر', 'قيد التجهيز', 'غير متاح') NOT NULL DEFAULT 'متاح',
                    `ملاحظات` TEXT,
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `السنة` INT GENERATED ALWAYS AS (YEAR(`التاريخ`)) STORED,
                    INDEX `idx_المستخدم` (`المستخدم`),
                    INDEX `idx_السنة` (`السنة`)
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            # cursor.execute("""
            #     CREATE TABLE IF NOT EXISTS `العقارات_بيانات_المالك` (
            #         `id` INTEGER PRIMARY KEY AUTO_INCREMENT,
            #         `معرف_العقار` INT,
            #         `الاسم` VARCHAR(255),
            #         `رقم_الهاتف` VARCHAR(255),
            #         `العنوان` VARCHAR(255),
            #         `ملاحظات` TEXT,

            #         CONSTRAINT `fk_بيانات_المالك_معرف_العقار`
            #         FOREIGN KEY (`معرف_العقار`)
            #         REFERENCES `العقارات`(`id`)
            #         ON DELETE CASCADE
            #     ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            # """)

            # cursor.execute("""
            #     CREATE TABLE IF NOT EXISTS `العقارات_بيانات_المشتري` (
            #         `id` INTEGER PRIMARY KEY AUTO_INCREMENT,
            #         `معرف_العقار` INT,
            #         `الاسم` VARCHAR(255),
            #         `رقم_الهاتف` VARCHAR(255),
            #         `العنوان` VARCHAR(255),
            #         `ملاحظات` TEXT,

            #         CONSTRAINT `fk_بيانات_المشتري_معرف_العقار`
            #         FOREIGN KEY (`معرف_العقار`)
            #         REFERENCES `العقارات`(`id`)
            #         ON DELETE CASCADE
            #     ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            # """)

            # cursor.execute("""
            #     CREATE TABLE IF NOT EXISTS `العقارات_معاملات_مالية` (
            #         `id` INTEGER PRIMARY KEY AUTO_INCREMENT,
            #         `معرف_العقار` INT,
            #         `المبلغ_المدفوع` DECIMAL(10,2),
            #         `تاريخ_الدفع` DATE,
            #         `طريقة_الدفع` VARCHAR(255),
            #         `المستلم` VARCHAR(255),

            #         CONSTRAINT `fk_العقار_المدفوعات_معرف_العقار`
            #         FOREIGN KEY (`معرف_العقار`)
            #         REFERENCES `العقارات`(`id`)
            #         ON DELETE CASCADE
            #     ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            # """)

            # cursor.execute("""
            #     CREATE TABLE IF NOT EXISTS `العقارات_الطلبات` (
            #         `id` INTEGER PRIMARY KEY AUTO_INCREMENT,
            #         `العقار` VARCHAR(255),
            #         `الاسم` VARCHAR(255),
            #         `رقم_الهاتف` VARCHAR(255),
            #         `العنوان` VARCHAR(255),
            #         `ملاحظات` TEXT
            #     ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            # """)

            #التدريب-------------------------------------
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `التدريب` (
                    `id` INT PRIMARY KEY AUTO_INCREMENT,
                    `التصنيف` VARCHAR(255),
                    `عنوان_الدورة` VARCHAR(255),
                    `المدرب` VARCHAR(255),
                    `التكلفة` DECIMAL(10,2),
                    `إجمالي_المبلغ` DECIMAL(10,2),
                    `عدد_المشاركين` INT,
                    `عدد_المجموعات` INT,
                    `تاريخ_البدء` DATE,
                    `تاريخ_الإنتهاء` DATE,
                    `الحالة` ENUM('معلق ', 'ملغاه', 'منتهية','جارية', 'قيد التسجيل') NOT NULL DEFAULT 'قيد التسجيل',
                    `ملاحظات` TEXT,
                    `المستخدم` VARCHAR(50),
                    `تاريخ_الإضافة` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `السنة` INT GENERATED ALWAYS AS (YEAR(`تاريخ_الإنتهاء`)) STORED,
                    INDEX `idx_المستخدم` (`المستخدم`),
                    INDEX `idx_السنة` (`السنة`)
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            """)

            # # المدربين
            # cursor.execute("""
            #     CREATE TABLE IF NOT EXISTS `التدريب_المدربين` (
            #         `id` INT PRIMARY KEY AUTO_INCREMENT,
            #         `الاسم` VARCHAR(255),
            #         `رقم_الهاتف` VARCHAR(255),
            #         `العنوان` VARCHAR(255),
            #         `التخصص` VARCHAR(255),
            #         `الراتب` DECIMAL(10,2),

            #         `الحالة` VARCHAR(255),
            #         `ملاحظات` TEXT
            #     ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            # """)

            # # المجموعات
            # cursor.execute("""
            #     CREATE TABLE IF NOT EXISTS `التدريب_المجموعات` (
            #         `id` INT PRIMARY KEY AUTO_INCREMENT,
            #         `معرف_الدورة` VARCHAR(255),
            #         `اسم المجموعه` VARCHAR(255),
            #         `الوصف` VARCHAR(255),
            #         `الدورة` VARCHAR(255),
            #         `الحالة` VARCHAR(255),
            #         `ملاحظات` TEXT
            #     ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            # """)

            # # الطلاب
            # cursor.execute("""
            #     CREATE TABLE IF NOT EXISTS `التدريب_الطلاب` (
            #         `id` INT PRIMARY KEY AUTO_INCREMENT,
            #         `معرف_الدورة` VARCHAR(255),
            #         `معرف_المجموعة` VARCHAR(255),
            #         `الاسم` VARCHAR(255),
            #         `رقم_الهاتف` VARCHAR(255),
            #         `المجموعة` VARCHAR(255),
            #         `المبلغ` DECIMAL(10,2),
            #         `المدفوع` DECIMAL(10,2) DEFAULT 0,
            #         `الباقي` DECIMAL(10,2) GENERATED ALWAYS AS (`المبلغ` - `المدفوع`) STORED,

            #         `الحالة` ENUM('لم يدفع', 'دفع جزئي', 'خالص','معفى') NOT NULL DEFAULT 'لم يدفع',
                           
            #         `تاريخ_التسجيل` DATE,
            #         `ملاحظات` TEXT
            #     ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            # """)
            
            # # دفعات الطلاب
            # cursor.execute("""
            #     CREATE TABLE IF NOT EXISTS `التدريب_دفعات_الطلاب` (
            #         `id` INT PRIMARY KEY AUTO_INCREMENT,
            #         `معرف_الدورة` VARCHAR(255),
            #         `معرف_المجموعة` VARCHAR(255),
            #         `معرف_الطالب` VARCHAR(255),
            #         `الاسم` VARCHAR(255),
            #         `المبلغ_المدفوع` DECIMAL(10,2),
            #         `تاريخ_الدفع` DATE,
            #         `طريقة_الدفع` VARCHAR(255),
            #         `المستلم` VARCHAR(255)
            #     ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            # """)

            # # مصروفات الدورات
            # cursor.execute("""
            #     CREATE TABLE IF NOT EXISTS `التدريب_مصروفات` (
            #         `id` INT PRIMARY KEY AUTO_INCREMENT,
            #         `معرف_الدورة` VARCHAR(255),
            #         `نوع_المصروف` VARCHAR(255),
            #         `وصف_المصروف` VARCHAR(255),
            #         `المبلغ` DECIMAL(10,2),
            #         `تاريخ_المصروف` DATE,
            #         `رقم_الفاتورة` VARCHAR(255),
            #         `المستلم` VARCHAR(255)
            #     ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            # """)

            
            # إضافة عمود وصف_المشروع إذا لم يكن موجوداً
            try:
                cursor.execute("SHOW COLUMNS FROM `المشاريع` LIKE 'وصف_المشروع'")
                if not cursor.fetchone():
                    cursor.execute("ALTER TABLE `المشاريع` ADD COLUMN `وصف_المشروع` TEXT AFTER `اسم_المشروع`")
                    print("تم إضافة عمود وصف_المشروع إلى جدول المشاريع")
            except Exception as e:
                print(f"خطأ في إضافة عمود وصف_المشروع: {e}")

            # إنشاء الفهارس (بعد إنشاء الجداول)
            create_index_if_not_exists(cursor, 'idx_المشاريع_التصنيف', 'المشاريع', 'التصنيف')

            # إدراج بعض التصنيفات الافتراضية
            default_categories = [
                # تصنيفات المشاريع
                ("المشاريع", "تصميم معماري", "#3498db", "تصميم المباني والمنشآت المعمارية"),
                ("المشاريع", "تصميم داخلي", "#9b59b6", "تصميم الديكور والتصميم الداخلي"),
                ("المشاريع", "مقاولات عامة", "#8b4513", "أعمال المقاولات والتنفيذ"),
                ("المشاريع", "إشراف هندسي", "#f39c12", "الإشراف على تنفيذ المشاريع"),
                ("المشاريع", "إعداد مقايسات", "#e67e22", "إعداد المقايسات والتكاليف"),

                # تصنيفات العملاء
                ("العملاء", "مواطن", "#27ae60", "عملاء أفراد"),
                ("العملاء", "شركة", "#2980b9", "شركات خاصة"),
                ("العملاء", "مؤسسة حكومية", "#8e44ad", "مؤسسات حكومية"),
                ("العملاء", "مكتب هندسي", "#16a085", "مكاتب هندسية"),

                # تصنيفات الحسابات
                ("الحسابات", "مصاريف إدارية", "#e74c3c", "المصاريف الإدارية العامة"),
                ("الحسابات", "مصاريف تشغيلية", "#f39c12", "مصاريف التشغيل اليومي"),
                ("الحسابات", "مصاريف مشاريع", "#3498db", "مصاريف خاصة بالمشاريع"),
                ("الحسابات", "مصاريف تسويق", "#9b59b6", "مصاريف التسويق والإعلان"),

                # تصنيفات العقارات
                ("العقارات", "شقة", "#34495e", "شقق سكنية"),
                ("العقارات", "فيلا", "#2c3e50", "فلل سكنية"),
                ("العقارات", "محل تجاري", "#f39c12", "محلات تجارية"),
                ("العقارات", "أرض", "#27ae60", "أراضي للبيع"),

                # تصنيفات التدريب
                ("التدريب", "دورة هندسية", "#3498db", "دورات تدريبية هندسية"),
                ("التدريب", "دورة تقنية", "#e67e22", "دورات تقنية متخصصة"),
                ("التدريب", "ورشة عمل", "#9b59b6", "ورش عمل تطبيقية"),

                # تصنيفات المقاولات
                ("المقاولات", "تنفيذ", "#8b4513", "أعمال التنفيذ والبناء العامة"),
                ("المقاولات", "بناء عظم", "#a0522d", "أعمال البناء والهيكل الأساسي"),
                ("المقاولات", "تشطيب", "#cd853f", "أعمال التشطيب والديكور"),
                ("المقاولات", "إشراف هندسي", "#f39c12", "الإشراف الهندسي على المقاولات"),
                ("المقاولات", "مقاولات عامة", "#8b4513", "مقاولات عامة متنوعة"),
                ("المقاولات", "مقاولات متخصصة", "#d2691e", "مقاولات متخصصة في مجال معين"),
                ("المقاولات", "صيانة وترميم", "#bc8f8f", "أعمال الصيانة والترميم"),
            ]

            for section, category, color, description in default_categories:
                try:
                    cursor.execute("""
                        INSERT IGNORE INTO التصنيفات
                        (اسم_القسم, اسم_التصنيف, لون_التصنيف, وصف_التصنيف, المستخدم)
                        VALUES (%s, %s, %s, %s, %s)
                    """, (section, category, color, description, "النظام"))
                except Exception as e:
                    print(f"تحذير: لم يتم إدراج التصنيف {category}: {e}")
            # Note: اسم_العميل is not a column in المشاريع table, it's joined from العملاء table

            create_index_if_not_exists(cursor, 'idx_المشاريع_المدفوعات_معرف_المشروع', 'المشاريع_المدفوعات', 'معرف_المشروع')
            create_index_if_not_exists(cursor, 'idx_المشاريع_المدفوعات_تاريخ_الدفع', 'المشاريع_المدفوعات', 'تاريخ_الدفع')

            create_index_if_not_exists(cursor, 'idx_العملاء_اسم_العميل', 'العملاء', 'اسم_العميل')

            create_index_if_not_exists(cursor, 'idx_المصروفات_التاريخ', 'الحسابات', 'تاريخ_المصروف')
            create_index_if_not_exists(cursor, 'idx_المصروفات_رقم_الفاتورة', 'الحسابات', 'رقم_الفاتورة')

            create_index_if_not_exists(cursor, 'idx_الموظفين_التصنيف', 'الموظفين', 'التصنيف')
            create_index_if_not_exists(cursor, 'idx_الموظفين_اسم_الموظف', 'الموظفين', 'اسم_الموظف')
            create_index_if_not_exists(cursor, 'idxالموظفين_معاملات_مالية_معرف_الموظف', 'الموظفين_معاملات_مالية', 'معرف_الموظف')
            create_index_if_not_exists(cursor, 'idxالموظفين_معاملات_مالية_التاريخ', 'الموظفين_معاملات_مالية', 'التاريخ')

            create_index_if_not_exists(cursor, 'idx_التصميم_معرف_العميل', 'المشاريع_المراحل', 'معرف_العميل')
            create_index_if_not_exists(cursor, 'idx_التصميم_معرف_المهندس', 'المشاريع_المراحل', 'معرف_المهندس')
            create_index_if_not_exists(cursor, 'idx_التصميم_تاريخ_البدء', 'المشاريع_المراحل', 'تاريخ_البدء')

            create_index_if_not_exists(cursor, 'idx_تقييم_المهندسين_معرف_المهندس', 'تقييم_المهندسين', 'معرف_الموظف')

            create_index_if_not_exists(cursor, 'idx_دورات_تدريبية_التصنيف', 'التدريب', 'التصنيف')
            create_index_if_not_exists(cursor, 'idx_دورات_تدريبية_تاريخ_البدء', 'التدريب', 'تاريخ_البدء')
            create_index_if_not_exists(cursor, 'idx_العقارات_التصنيف', 'العقارات', 'التصنيف')
            create_index_if_not_exists(cursor, 'idx_مصروفات_المشروع_معرف_المشروع', 'مصروفات_المشروع', 'معرف_المشروع')
            create_index_if_not_exists(cursor, 'idx_مصروفات_المشروع_نوع_المصروف', 'مصروفات_المشروع', 'نوع_المصروف')
            create_index_if_not_exists(cursor, 'idx_مصروفات_المشروع_تاريخ_المصروف', 'مصروفات_المشروع', 'تاريخ_المصروف')
            create_index_if_not_exists(cursor, 'idx_مصروفات_مباشرة_المشروع_معرف_المشروع', 'مصروفات_مباشرة_المشروع', 'معرف_المشروع')
            create_index_if_not_exists(cursor, 'idx_مصروفات_مباشرة_المشروع_تاريخ_المصروف', 'مصروفات_مباشرة_المشروع', 'تاريخ_المصروف')



            # إنشاء التريغرات
            # Note: MySQL triggers are schema-specific. These will be created in project_manager2_YYYY database
            triggers = [
                ("update_project_paid_insert", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_project_paid_insert`
                    AFTER INSERT ON `المشاريع_المدفوعات`
                    FOR EACH ROW
                    BEGIN
                        UPDATE `المشاريع`
                        SET `المدفوع` = `المدفوع` + NEW.`المبلغ_المدفوع`
                        WHERE `id` = NEW.`معرف_المشروع`;
                    END;
                """),
                ("update_project_paid_update", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_project_paid_update`
                    AFTER UPDATE ON `المشاريع_المدفوعات`
                    FOR EACH ROW
                    BEGIN
                        UPDATE `المشاريع`
                        SET `المدفوع` = `المدفوع` - OLD.`المبلغ_المدفوع` + NEW.`المبلغ_المدفوع`
                        WHERE `id` = NEW.`معرف_المشروع`;
                    END;
                """),
                ("update_project_paid_delete", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_project_paid_delete`
                    AFTER DELETE ON `المشاريع_المدفوعات`
                    FOR EACH ROW
                    BEGIN
                        UPDATE `المشاريع`
                        SET `المدفوع` = `المدفوع` - OLD.`المبلغ_المدفوع`
                        WHERE `id` = OLD.`معرف_المشروع`;
                    END;
                """),

                ("update_employee_balance_insert", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_employee_balance_insert`
                    AFTER INSERT ON `الموظفين_معاملات_مالية`
                    FOR EACH ROW
                    BEGIN
                        UPDATE `الموظفين`
                        SET `الرصيد` = `الرصيد` + NEW.`المبلغ`
                        WHERE `id` = NEW.`معرف_الموظف`;
                    END;
                """),
                 ("update_employee_balance_update", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_employee_balance_update`
                    AFTER UPDATE ON `الموظفين_معاملات_مالية`
                    FOR EACH ROW
                    BEGIN
                        UPDATE `الموظفين`
                        SET `الرصيد` = `الرصيد` - OLD.`المبلغ` + NEW.`المبلغ`
                        WHERE `id` = NEW.`معرف_الموظف`;
                    END;
                """),
                 ("update_employee_balance_delete", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_employee_balance_delete`
                    AFTER DELETE ON `الموظفين_معاملات_مالية`
                    FOR EACH ROW
                    BEGIN
                        UPDATE `الموظفين`
                        SET `الرصيد` = `الرصيد` - OLD.`المبلغ`
                        WHERE `id` = OLD.`معرف_الموظف`;
                    END;
                """),

                ("تحديث_الموظفين_معاملات_مالية", f"""
                    CREATE TRIGGER IF NOT EXISTS `تحديث_الموظفين_معاملات_مالية`
                    AFTER UPDATE ON `الموظفين`
                    FOR EACH ROW
                    BEGIN
                        UPDATE `الموظفين_معاملات_مالية`
                        SET
                            `اسم_الموظف` = NEW.`اسم_الموظف`,
                            `الوظيفة` = NEW.`الوظيفة`
                        WHERE `معرف_الموظف` = NEW.`id`;
                    END;
                """),
                ("تحديث_التصميم", f"""
                    CREATE TRIGGER IF NOT EXISTS `تحديث_التصميم`
                    AFTER UPDATE ON `المشاريع`
                    FOR EACH ROW
                    BEGIN
                        DECLARE client_name VARCHAR(255);

                        -- Get client name from the clients table
                        SELECT `اسم_العميل` INTO client_name
                        FROM `العملاء`
                        WHERE `id` = NEW.`معرف_العميل`;

                        UPDATE `المشاريع_المراحل`
                        SET
                            `اسم_العميل` = client_name,
                            `اسم_المشروع` = NEW.`اسم_المشروع`
                        WHERE `معرف_العميل` = NEW.`id`;
                    END;
                """),
                ("تحديث_التصميم_للموظف", f"""
                    CREATE TRIGGER IF NOT EXISTS `تحديث_التصميم_للموظف`
                    AFTER UPDATE ON `الموظفين`
                    FOR EACH ROW
                    BEGIN
                        UPDATE `المشاريع_المراحل`
                        SET
                            `المهندس` = NEW.`اسم_الموظف`
                        WHERE `معرف_الموظف` = NEW.`id`;
                    END;
                """),

                # تريغرات العهد المالية
                ("update_custody_expenses_insert", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_custody_expenses_insert`
                    AFTER INSERT ON `مصروفات_العهد`
                    FOR EACH ROW
                    BEGIN
                        UPDATE `المقاولات_العهد`
                        SET `المصروف` = `المصروف` + NEW.`المبلغ`
                        WHERE `id` = NEW.`معرف_العهدة`;
                    END;
                """),
                ("update_custody_expenses_update", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_custody_expenses_update`
                    AFTER UPDATE ON `مصروفات_العهد`
                    FOR EACH ROW
                    BEGIN
                        UPDATE `المقاولات_العهد`
                        SET `المصروف` = `المصروف` - OLD.`المبلغ` + NEW.`المبلغ`
                        WHERE `id` = NEW.`معرف_العهدة`;
                    END;
                """),
                ("update_custody_expenses_delete", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_custody_expenses_delete`
                    AFTER DELETE ON `مصروفات_العهد`
                    FOR EACH ROW
                    BEGIN
                        UPDATE `المقاولات_العهد`
                        SET `المصروف` = `المصروف` - OLD.`المبلغ`
                        WHERE `id` = OLD.`معرف_العهدة`;
                    END;
                """),
                ("update_custody_project_info", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_custody_project_info`
                    AFTER UPDATE ON `المشاريع`
                    FOR EACH ROW
                    BEGIN
                        DECLARE client_name VARCHAR(255);

                        SELECT `اسم_العميل` INTO client_name
                        FROM `العملاء`
                        WHERE `id` = NEW.`معرف_العميل`;

                        UPDATE `المقاولات_العهد`
                        SET
                            `اسم_المشروع` = NEW.`اسم_المشروع`,
                            `اسم_العميل` = client_name
                        WHERE `معرف_المشروع` = NEW.`id`;
                    END;
                """),

                # تريغرات دفعات العهد المالية لتحديث مبلغ_العهدة تلقائياً
                ("update_custody_amount_insert", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_custody_amount_insert`
                    AFTER INSERT ON `دفعات_العهد`
                    FOR EACH ROW
                    BEGIN
                        UPDATE `المقاولات_العهد`
                        SET `مبلغ_العهدة` = `مبلغ_العهدة` + NEW.`المبلغ`
                        WHERE `id` = NEW.`معرف_العهدة`;
                    END;
                """),
                ("update_custody_amount_update", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_custody_amount_update`
                    AFTER UPDATE ON `دفعات_العهد`
                    FOR EACH ROW
                    BEGIN
                        UPDATE `المقاولات_العهد`
                        SET `مبلغ_العهدة` = `مبلغ_العهدة` - OLD.`المبلغ` + NEW.`المبلغ`
                        WHERE `id` = NEW.`معرف_العهدة`;
                    END;
                """),
                ("update_custody_amount_delete", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_custody_amount_delete`
                    AFTER DELETE ON `دفعات_العهد`
                    FOR EACH ROW
                    BEGIN
                        UPDATE `المقاولات_العهد`
                        SET `مبلغ_العهدة` = `مبلغ_العهدة` - OLD.`المبلغ`
                        WHERE `id` = OLD.`معرف_العهدة`;
                    END;
                """),

                # تريغرات مصروفات المشروع
                ("update_project_expenses_insert", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_project_expenses_insert`
                    AFTER INSERT ON `مصروفات_المشروع`
                    FOR EACH ROW
                    BEGIN
                        -- إذا كان المصروف مرتبط بعهدة، قم بتحديث العهدة أيضاً
                        IF NEW.`نوع_المصروف` = 'مرتبط_بعهدة' AND NEW.`معرف_العهدة` IS NOT NULL THEN
                            UPDATE `المقاولات_العهد`
                            SET `المصروف` = `المصروف` + NEW.`المبلغ`
                            WHERE `id` = NEW.`معرف_العهدة`;
                        END IF;
                    END;
                """),
                ("update_project_expenses_update", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_project_expenses_update`
                    AFTER UPDATE ON `مصروفات_المشروع`
                    FOR EACH ROW
                    BEGIN
                        -- إذا كان المصروف القديم مرتبط بعهدة، قم بطرح المبلغ القديم
                        IF OLD.`نوع_المصروف` = 'مرتبط_بعهدة' AND OLD.`معرف_العهدة` IS NOT NULL THEN
                            UPDATE `المقاولات_العهد`
                            SET `المصروف` = `المصروف` - OLD.`المبلغ`
                            WHERE `id` = OLD.`معرف_العهدة`;
                        END IF;
                        
                        -- إذا كان المصروف الجديد مرتبط بعهدة، قم بإضافة المبلغ الجديد
                        IF NEW.`نوع_المصروف` = 'مرتبط_بعهدة' AND NEW.`معرف_العهدة` IS NOT NULL THEN
                            UPDATE `المقاولات_العهد`
                            SET `المصروف` = `المصروف` + NEW.`المبلغ`
                            WHERE `id` = NEW.`معرف_العهدة`;
                        END IF;
                    END;
                """),
                ("update_project_expenses_delete", f"""
                    CREATE TRIGGER IF NOT EXISTS `update_project_expenses_delete`
                    AFTER DELETE ON `مصروفات_المشروع`
                    FOR EACH ROW
                    BEGIN
                        -- إذا كان المصروف مرتبط بعهدة، قم بطرح المبلغ من العهدة
                        IF OLD.`نوع_المصروف` = 'مرتبط_بعهدة' AND OLD.`معرف_العهدة` IS NOT NULL THEN
                            UPDATE `المقاولات_العهد`
                            SET `المصروف` = `المصروف` - OLD.`المبلغ`
                            WHERE `id` = OLD.`معرف_العهدة`;
                        END IF;
                    END;
                """)
            ]

            for trigger_name, trigger_sql in triggers:
                try:
                    trigger_exists = False
                    try:
                        cursor.execute("""
                            SELECT COUNT(*)
                            FROM INFORMATION_SCHEMA.TRIGGERS
                            WHERE TRIGGER_SCHEMA = %s AND TRIGGER_NAME = %s
                        """, (db_name, trigger_name))
                        if cursor.fetchone()[0] > 0:
                            trigger_exists = True
                    except mysql.connector.Error as check_err:
                         print(f"Warning: Could not check for trigger {trigger_name} existence: {check_err}")

                    if not trigger_exists:
                        try:
                            cursor.execute(trigger_sql)
                            print(f"Trigger {trigger_name} ensured.")
                        except mysql.connector.Error as err:
                            if err.errno == 1359: # Trigger already exists error number (common)
                                print(f"Trigger {trigger_name} already exists. Skipping creation (from error code).")
                            else:
                                print(f"Error creating trigger {trigger_name}: {err}")
                                # Decide if this should be a fatal error or just a warning
                                # raise # Uncomment to make trigger creation errors fatal
                    # else:
                    #      print(f"Trigger {trigger_name} already exists. Skipping creation.")

                except Exception as e:
                     print(f"Unexpected error processing trigger {trigger_name}: {e}")

            conn.commit()
            return True

        except mysql.connector.Error as err:
            print(f"Database Creation/Setup Error: {err}")
            QMessageBox.critical(self, "خطأ في إعداد قاعدة البيانات",
                                 f"حدث خطأ أثناء إنشاء أو تحديث قاعدة البيانات للسنة {year}:\n{err}")
            return False
        except Exception as e:
            print(f"Unexpected Error during DB setup: {e}")
            QMessageBox.critical(self, "خطأ غير متوقع",
                                 f"حدث خطأ غير متوقع أثناء إعداد قاعدة البيانات:\n{e}")
            return False
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()




# متغير يخزن آخر مجلد تم فيه النسخ الاحتياطي---------------------------------------------------------------
last_backup_folder = None

#بروسس بار للنسخ الاحتياطي
class ProgressDialog(QDialog):
    def __init__(self, title, message, parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setFixedSize(450, 150)

        layout = QVBoxLayout()

        # تحسين شكل الرسالة
        self.label = QLabel(message)
        self.label.setAlignment(Qt.AlignCenter)
        self.label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(self.label)

        # تحسين شكل البروغرس بار
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p%")
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 10px;
                background-color: rgba(236, 240, 241, 0.3);
                text-align: center;
                color: #2c3e50;
                font-weight: bold;
                font-size: 14px;
                height: 25px;
                padding: 0px;
                margin: 10px 10px;
            }
            QProgressBar::chunk {
                background: qlineargradient(
                    spread:pad, x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:0.3 #2980b9,
                    stop:0.6 #16a085, stop:1 #2ecc71
                );
                border-radius: 10px;
                margin: 0px;
            }
        """)
        layout.addWidget(self.progress_bar)

        # إضافة هامش في الأسفل
        layout.addSpacing(10)

        self.setLayout(layout)

#==========# دالة النسخ الاحتياطي اليدوي============================================================================================
def Backup_DB(self):
    global last_backup_folder

    if self.license_type == "trial":
        reply = GEN_MSG_BOX("قيود النسخة التجريبية", "هذه الميزة متوفرة في النسخة المدفوعة فقط.", "license.png", "شراء", "إلغاء", "#dfcab4")
        if reply != QMessageBox.Ok:
            return
        else:
            self.changing_activation_dialog()
            return

    # اختيار مجلد الحفظ
    source_folder = QFileDialog.getExistingDirectory(None, "اختر مجلد حفظ النسخ الاحتياطية")
    if source_folder:
        # حفظ المسار
        with open(backup_info, 'w', encoding='utf-8') as file:
            file.write(source_folder)

        main_backup_folder = os.path.join(source_folder, "Backup folders")
        os.makedirs(main_backup_folder, exist_ok=True)

        today_date = datetime.now().strftime("%d-%m-%Y")
        backup_folder_name = f"Backup_DB V{CURRENT_VERSION} {today_date}"
        project_manager = os.path.join(main_backup_folder, backup_folder_name)

        try:
            # إنشاء شريط التقدم
            progress = QProgressDialog("جارٍ إنشاء النسخ الاحتياطية...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("النسخ الاحتياطي")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setValue(0)

            # حذف المجلد إذا كان موجودًا
            if os.path.exists(project_manager):
                shutil.rmtree(project_manager)

            # نسخ الملفات (50% من التقدم)
            total_files = sum(len(files) for _, _, files in os.walk(folder_path))
            if total_files == 0:
                progress.close()
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على ملفات للنسخ.")
                return

            copied_files = 0

            def copy_with_progress(src, dst):
                nonlocal copied_files
                os.makedirs(dst, exist_ok=True)
                for item in os.listdir(src):
                    if progress.wasCanceled():
                        return False
                    s = os.path.join(src, item)
                    d = os.path.join(dst, item)
                    if os.path.isdir(s):
                        copy_with_progress(s, d)
                    else:
                        shutil.copy2(s, d)
                        copied_files += 1
                        progress_value = int((copied_files / total_files) * 50)  # 50% للملفات
                        progress.setValue(progress_value)
                        progress.setLabelText(f"جارٍ نسخ الملفات: {copied_files}/{total_files}")
                        QApplication.processEvents()
                return True

            # نسخ الملفات
            if not copy_with_progress(folder_path, project_manager):
                progress.close()
                QMessageBox.information(self, "إلغاء", "تم إلغاء عملية النسخ الاحتياطي.")
                return

            # نسخ قواعد البيانات (50% المتبقية)
            progress.setLabelText("جارٍ نسخ قاعدة البيانات...")
            if not backup_databases(source_folder, project_manager, progress, start_progress=50, end_progress=100):
                progress.close()
                QMessageBox.information(self, "إلغاء", "تم إلغاء عملية النسخ الاحتياطي.")
                return

            # إكمال التقدم
            progress.setValue(100)
            progress.close()

            # حذف النسخ القديمة
            remove_old_backup_folders(self, main_backup_folder)

            QMessageBox.information(self, "النسخ الاحتياطية", "تم إنشاء النسخ الاحتياطية لجميع قواعد البيانات بنجاح.")
        except Exception as e:
            progress.close()
            QMessageBox.critical(self, "نسخ إحتياطي يدوي", f"فشل في إنشاء النسخة الاحتياطية: {e}")
    else:
        QMessageBox.warning(self, "تحذير", "لم يتم اختيار أي مجلد.")



# إنشاء ملف إعداد mysql مؤقت
def create_mysql_config_file():
    user_home = os.path.expanduser("~")
    config_path = os.path.join(user_home, "temp_mysql.cnf")

    config_content = """
[client]
user=root
password=kh123456
host=localhost
"""

    try:
        with open(config_path, 'w') as f:
            f.write(config_content.strip())

        # إخفاء الملف في ويندوز
        subprocess.run(["attrib", "+h", config_path], shell=True)
        return config_path
    except Exception as e:
        QMessageBox.critical(None, "خطأ", f"فشل في إنشاء ملف إعداد الاتصال: {e}")
        return None

# دالة تنفيذ النسخ الاحتياطي
def backup_databases(folder, project_manager, progress_dialog=None, start_progress=50, end_progress=100):
    if not folder:
        return

    if not os.path.exists(project_manager):
        os.makedirs(project_manager)

    mysql_path = r"C:\Program Files\MySQL\MySQL Server 8.4\bin\mysql.exe"
    mysqldump_path = r"C:\Program Files\MySQL\MySQL Server 8.4\bin\mysqldump.exe"

    # جلب قائمة قواعد البيانات
    list_command = [
        mysql_path,
        f"--host={host}",
        f"--user={user_r}",
        f"--password={password_r}",
        "--batch",
        "--skip-column-names",
        "-e",
        "SHOW DATABASES LIKE 'project_manager%';"
    ]
    result = subprocess.run(list_command, capture_output=True, text=True, check=True)
    db_list = result.stdout.splitlines()
    total_dbs = len(db_list)

    if total_dbs == 0:
        return

    # حساب التقدم لكل قاعدة بيانات
    progress_per_db = (end_progress - start_progress) / total_dbs if total_dbs > 0 else 0
    current_progress = start_progress

    for i, db_name in enumerate(db_list):
        if progress_dialog and progress_dialog.wasCanceled():
            return False  # إلغاء العملية

        backup_filename = f"{db_name}_backup.sql"
        backup_path = os.path.join(project_manager, backup_filename)
        dump_command = [
            mysqldump_path,
            f"--host={host}",
            f"--user={user_r}",
            f"--password={password_r}",
            db_name,
            "-r",
            backup_path
        ]
        subprocess.run(dump_command, check=True)

        # تحديث شريط التقدم
        if progress_dialog:
            current_progress += progress_per_db
            progress_dialog.setValue(int(current_progress))
            progress_dialog.setLabelText(f"جارٍ نسخ قاعدة البيانات: {i + 1}/{total_dbs}")
            QApplication.processEvents()

    return True

# دالة تنفيذ النسخ الاحتياطي
def backup_databases(folder, project_manager, progress_dialog=None, start_progress=50, end_progress=100):
    if not folder:
        return

    if not os.path.exists(project_manager):
        os.makedirs(project_manager)

    # إنشاء ملف إعداد الاتصال
    config_path = create_mysql_config_file()
    if not config_path:
        return False

    mysql_path = r"C:\Program Files\MySQL\MySQL Server 8.4\bin\mysql.exe"
    mysqldump_path = r"C:\Program Files\MySQL\MySQL Server 8.4\bin\mysqldump.exe"

    try:
        # جلب قائمة قواعد البيانات
        list_command = [
            mysql_path,
            f"--defaults-file={config_path}",
            "--batch",
            "--skip-column-names",
            "-e",
            "SHOW DATABASES LIKE 'project_manager%';"
        ]
        result = subprocess.run(list_command, capture_output=True, text=True, check=True)
        db_list = result.stdout.splitlines()
        total_dbs = len(db_list)

        if total_dbs == 0:
            return

        # حساب التقدم لكل قاعدة بيانات
        progress_per_db = (end_progress - start_progress) / total_dbs if total_dbs > 0 else 0
        current_progress = start_progress

        for i, db_name in enumerate(db_list):
            if progress_dialog and progress_dialog.wasCanceled():
                return False  # إلغاء العملية

            backup_filename = f"{db_name}_backup.sql"
            backup_path = os.path.join(project_manager, backup_filename)
            dump_command = [
                mysqldump_path,
                f"--defaults-file={config_path}",
                db_name,
                "-r",
                backup_path
            ]
            subprocess.run(dump_command, check=True)

            # تحديث شريط التقدم
            if progress_dialog:
                current_progress += progress_per_db
                progress_dialog.setValue(int(current_progress))
                progress_dialog.setLabelText(f"جارٍ نسخ قاعدة البيانات: {i + 1}/{total_dbs}")
                QApplication.processEvents()

    finally:
        # حذف ملف الإعداد المؤقت بعد الانتهاء
        try:
            if os.path.exists(config_path):
                os.remove(config_path)
        except Exception as e:
            print(f"تنبيه: لم يتم حذف ملف الاتصال المؤقت: {e}")

    return True

#=========#نسخ تلقائي للقاعدة=============================================================================================
def Auto_Backup_DB(self):
    global last_backup_folder
    try:
        with open(backup_info, 'r', encoding='utf-8') as file:
            dest_folder_path = file.read().strip()

        if os.path.exists(dest_folder_path):
            today_date = datetime.now().strftime("%d-%m-%Y")
            dest_folder_backup_path = os.path.join(dest_folder_path, "Backup folders", f"Backup_DB V{CURRENT_VERSION} {today_date}")

            # إنشاء شريط التقدم
            progress = QProgressDialog("جارٍ إنشاء النسخ الاحتياطية التلقائية...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("النسخ الاحتياطي التلقائي")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setValue(0)

            # حذف المجلد إذا كان موجودًا
            if os.path.exists(dest_folder_backup_path):
                shutil.rmtree(dest_folder_backup_path)

            # نسخ الملفات (50% من التقدم)
            total_files = sum(len(files) for _, _, files in os.walk(folder_path))
            if total_files == 0:
                progress.close()
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على ملفات للنسخ.")
                return

            copied_files = 0

            def copy_with_progress(src, dst):
                nonlocal copied_files
                os.makedirs(dst, exist_ok=True)
                for item in os.listdir(src):
                    if progress.wasCanceled():
                        return False
                    s = os.path.join(src, item)
                    d = os.path.join(dst, item)
                    if os.path.isdir(s):
                        copy_with_progress(s, d)
                    else:
                        shutil.copy2(s, d)
                        copied_files += 1
                        progress_value = int((copied_files / total_files) * 50)  # 50% للملفات
                        progress.setValue(progress_value)
                        progress.setLabelText(f"جارٍ نسخ الملفات: {copied_files}/{total_files}")
                        QApplication.processEvents()
                return True

            # نسخ الملفات
            if not copy_with_progress(folder_path, dest_folder_backup_path):
                progress.close()
                QMessageBox.information(self, "إلغاء", "تم إلغاء عملية النسخ الاحتياطي التلقائي.")
                return

            # نسخ قواعد البيانات (50% المتبقية)
            progress.setLabelText("جارٍ نسخ قاعدة البيانات...")
            if not backup_databases(dest_folder_path, dest_folder_backup_path, progress, start_progress=50, end_progress=100):
                progress.close()
                QMessageBox.information(self, "إلغاء", "تم إلغاء عملية النسخ الاحتياطي التلقائي.")
                return

            # إكمال التقدم
            progress.setValue(100)
            progress.close()

            # حذف النسخ القديمة
            remove_old_backup_folders(self, os.path.join(dest_folder_path, "Backup folders"))

            QMessageBox.information(self, "النسخ الاحتياطية", f"تم تحديث النسخة الإحتياطية بنجاح.\n{dest_folder_backup_path}")
        else:
            reply = GEN_MSG_BOX('النسخ الإحتياطي التلقائي', 'مسار النسخ الاحتياطي غير موجود، يرجى تحديد مسار جديد.', 'warning.png', 'مسار جديد', 'خروج', msg_box_color)
            if reply != QMessageBox.Ok:
                return
            Backup_DB(self)
    except Exception as e:
        progress.close()
        QMessageBox.critical(self, "نسخ تلقائي إحتياطي", f"فشل في إنشاء النسخة الاحتياطية: {e}")

# حذف بعد شهر
def remove_old_backup_folders(self, path):
    now = datetime.now()
    one_month_ago = now - timedelta(days=90)
    old_folders = []  # قائمة لتخزين أسماء النسخ الاحتياطية القديمة

    for folder_name in os.listdir(path):
        folder_path = os.path.join(path, folder_name)

        if os.path.isdir(folder_path) and folder_name.startswith("Backup_DB"):
            try:
                # استخراج التاريخ من اسم المجلد
                #date_part = folder_name.split(f"Backup_DB V{CURRENT_VERSION} ")[1]
                date_part = folder_name.split("Backup_DB V")[1].split()[-1]  # يأخذ الجزء الأخير بعد المسافة
                folder_date = datetime.strptime(date_part, "%d-%m-%Y")

                if folder_date < one_month_ago:
                    old_folders.append(folder_path)  # إضافة المجلد إلى القائمة

            except (IndexError, ValueError) as e:
                QMessageBox.critical(self, "خطأ في اسم المجلد",
                                     f"اسم المجلد: {folder_name}\nخطأ: {str(e)}")
                continue

    if old_folders:
        # إنشاء نص رسالة التأكيد
        folders_list = "\n".join(os.path.basename(folder) for folder in old_folders)
        reply = QMessageBox.question(self, "تأكيد الحذف",
                                     f"يوجد نسخ إحتياطية قديمة لأكثر من 3 أشهر !\nهل تريد حذف النسخ الاحتياطيةالتالية؟\n\n{folders_list}",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            for folder in old_folders:
                shutil.rmtree(folder)  # حذف المجلد
            QMessageBox.information(self, "تم الحذف", "تم حذف جميع النسخ الاحتياطية القديمة بنجاح.")

#استعادة النسخة الاحتياطية \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
def import_db(self):
    if self.license_type == "trial":
        reply = GEN_MSG_BOX("قيود النسخة التجريبية", "هذه الميزة متوفرة في النسخة المدفوعة فقط.", "license.png", "شراء", "إلغاء", "#dfcab4")
        if reply != QMessageBox.Ok:
            return
        else:
            self.changing_activation_dialog()
            return

    folder_path = QFileDialog.getExistingDirectory(None, "اختر المجلد الذي يحتوي على النسخ الاحتياطية")
    if folder_path:
        # جمع ملفات النسخ الاحتياطية
        backup_files = [f for f in os.listdir(folder_path) if f.startswith("project_manager") and f.endswith(".sql")]
        total_files = len(backup_files)

        if total_files == 0:
            QMessageBox.warning(self, "تحذير", "لم يتم العثور على ملفات نسخ احتياطية في المجلد.")
            return

        # إنشاء شريط التقدم
        progress = QProgressDialog("جارٍ استعادة النسخ الاحتياطية...", "إلغاء", 0, total_files, self)
        progress.setWindowTitle("استعادة النسخة الاحتياطية")
        progress.setWindowModality(Qt.WindowModal)
        progress.setMinimumDuration(0)
        progress.setValue(0)
        processed_files = 0

        for filename in backup_files:
            # التحقق من إلغاء العملية
            if progress.wasCanceled():
                QMessageBox.information(self, "إلغاء", "تم إلغاء عملية الاستعادة.")
                return

            backup_file = os.path.join(folder_path, filename)
            db_name = filename.split('_backup')[0]  # استخراج اسم قاعدة البيانات
            mysql_path = r"C:\Program Files\MySQL\MySQL Server 8.4\bin\mysql.exe"
            mysqladmin_path = r"C:\Program Files\MySQL\MySQL Server 8.4\bin\mysqladmin.exe"

            # إنشاء قاعدة البيانات إذا لم تكن موجودة
            create_db_command = [mysql_path, f"--host={host}", f"--user={user_r}", f"--password={password_r}", "-e", f"CREATE DATABASE IF NOT EXISTS {db_name}"]
            try:
                subprocess.run(create_db_command, check=True)
                print(f"تم التأكد من وجود قاعدة البيانات أو إنشاؤها: {db_name}")
            except subprocess.CalledProcessError as e:
                print(f"فشل في إنشاء قاعدة البيانات {db_name}: {e}")
                continue

            # فحص وجود الجدول License_Status
            check_table_command = [mysql_path, f"--host={host}", f"--user={user}", f"--password={password}", db_name, "-e", "SHOW TABLES LIKE 'License_Status';"]
            table_exists = False
            try:
                result = subprocess.run(check_table_command, capture_output=True, text=True, check=True)
                if 'License_Status' in result.stdout:
                    table_exists = True
                    print(f"تم العثور على الجدول 'License_Status' في قاعدة البيانات: {db_name}")
            except subprocess.CalledProcessError as e:
                print(f"فشل في التحقق من وجود الجدول 'License_Status' في قاعدة البيانات {db_name}: {e}")

            if table_exists:
                print(f"تم استبعاد الجدول 'License_Status' من الاسترداد.")
                continue  # تخطي استعادة النسخة الاحتياطية إذا كان الجدول موجودًا

            # استعادة النسخة الاحتياطية
            restore_command = [mysql_path, f"--host={host}", f"--user={user}", f"--password={password}", db_name]
            try:
                with open(backup_file, 'r') as file:
                    subprocess.run(restore_command, stdin=file, check=True)
                print(f"تم استعادة النسخة الاحتياطية بنجاح لقاعدة البيانات: {db_name}")
            except subprocess.CalledProcessError as e:
                print(f"فشل في استعادة النسخة الاحتياطية لقاعدة البيانات {db_name}: {e}")
            except FileNotFoundError:
                print(f"الملف {backup_file} غير موجود.")

            # تحديث شريط التقدم
            processed_files += 1
            progress.setValue(processed_files)
            progress.setLabelText(f"جارٍ استعادة النسخة الاحتياطية: {processed_files}/{total_files}")
            QApplication.processEvents()  # تحديث واجهة المستخدم

        # إغلاق شريط التقدم
        progress.setValue(total_files)
        QMessageBox.information(self, "استعادة النسخ الاحتياطية", "تم استعادة جميع قواعد البيانات.\nسيتم إعادة تشغيل التطبيق.")
        restart_application()
    else:
        QMessageBox.warning(self, "تحذير", "لم يتم اختيار أي مجلد.")


# استعادة النسخة الاحتياطية
def import_db(self):
    if self.license_type == "trial":
        reply = GEN_MSG_BOX("قيود النسخة التجريبية", "هذه الميزة متوفرة في النسخة المدفوعة فقط.", "license.png", "شراء", "إلغاء", "#dfcab4")
        if reply != QMessageBox.Ok:
            return
        else:
            self.changing_activation_dialog()
            return

    folder_path = QFileDialog.getExistingDirectory(None, "اختر المجلد الذي يحتوي على النسخ الاحتياطية")
    if not folder_path:
        QMessageBox.warning(self, "تحذير", "لم يتم اختيار أي مجلد.")
        return

    # إنشاء ملف إعداد mysql
    config_path = create_mysql_config_file()
    if not config_path:
        return

    try:
        backup_files = [f for f in os.listdir(folder_path) if f.startswith("project_manager") and f.endswith(".sql")]
        total_files = len(backup_files)

        if total_files == 0:
            QMessageBox.warning(self, "تحذير", "لم يتم العثور على ملفات نسخ احتياطية في المجلد.")
            return

         # تغيير الترميز إلى UTF-8 لتفادي مشاكل cp720
        os.system("chcp 65001 > NUL")

        mysql_path = r"C:\Program Files\MySQL\MySQL Server 8.4\bin\mysql.exe"


        progress = QProgressDialog("جارٍ استعادة النسخ الاحتياطية...", "إلغاء", 0, total_files, self)
        progress.setWindowTitle("استعادة النسخة الاحتياطية")
        progress.setWindowModality(Qt.WindowModal)
        progress.setMinimumDuration(0)
        progress.setValue(0)
        processed_files = 0


        for filename in backup_files:
            if progress.wasCanceled():
                QMessageBox.information(self, "إلغاء", "تم إلغاء عملية الاستعادة.")
                return

            backup_file = os.path.join(folder_path, filename)
            db_name = filename.split('_backup')[0]

            # إنشاء قاعدة البيانات
            create_db_command = [
                mysql_path, f"--defaults-file={config_path}", "-e",
                f"CREATE DATABASE IF NOT EXISTS {db_name}"
            ]
            subprocess.run(create_db_command, check=True)

            # التحقق من وجود جدول License_Status
            check_table_command = [
                mysql_path, f"--defaults-file={config_path}", db_name, "-e",
                "SHOW TABLES LIKE 'License_Status';"
            ]
            try:
                result = subprocess.run(check_table_command, capture_output=True, text=True, check=True)
                if 'License_Status' in result.stdout:
                    continue  # تخطي الاستعادة إذا كان الجدول موجودًا
            except:
                pass

            # تنفيذ الاستعادة
            restore_command = [mysql_path, f"--defaults-file={config_path}", db_name]
            try:
                with open(backup_file, 'r') as file:
                    subprocess.run(restore_command, stdin=file, check=True)
            except Exception as e:
                print(f"خطأ أثناء استعادة {db_name}: {e}")
                continue

            processed_files += 1
            progress.setValue(processed_files)
            progress.setLabelText(f"جارٍ استعادة النسخة الاحتياطية: {processed_files}/{total_files}")
            QApplication.processEvents()

        progress.setValue(total_files)
        QMessageBox.information(self, "استعادة النسخ الاحتياطية", "تم استعادة جميع قواعد البيانات.\nسيتم إعادة تشغيل التطبيق.")
        restart_application()

    finally:
        if os.path.exists(config_path):
            try:
                os.remove(config_path)
            except Exception as e:
                print(f"تنبيه: لم يتم حذف ملف الإعداد المؤقت: {e}")


#GoogleDrive///////////////////////////////////////////////////////////////////////////////////

#تثبيت قاعدة البيانات ////////////////////////////////////////////////
def instll_db():
    mysql_bin_path = r'C:\Program Files\MySQL\MySQL Server 8.4\bin'
    msi_path = os.path.join(application_path, "mysql8.4.msi")
    if not os.path.exists(mysql_bin_path):
        #QMessageBox.critical(self, ' قاعدة البيانات', f'يجب تثبيت قاعدة البيانات أولا... اتصل بالمطور لحل المشكلة')
        if os.path.exists(msi_path):
            os.startfile(msi_path)
        sys.exit()

#تغيير باسوورد الروت
def change_root_password(self,new_password):
    try:
        # الاتصال بقاعدة البيانات باستخدام كلمة المرور القديمة
        connection = mysql.connector.connect(host=host,user="root",password="123456")
        if connection.is_connected():
            cursor = connection.cursor()
            # تنفيذ استعلام تغيير كلمة المرور
            cursor.execute(f"ALTER USER 'root'@'{host}' idENTIFIED BY '{new_password}';")
            # التحقق مما إذا كان المستخدم 'pme' موجودًا
            cursor.execute(f"SELECT COUNT(*) FROM mysql.user WHERE user = 'pme' AND host = '{host}';")
            user_exists = cursor.fetchone()[0]  # جلب النتيجة
            if user_exists:  # إذا كان المستخدم موجودً
                cursor.execute(f"ALTER USER 'pme'@'{host}' idENTIFIED BY '{new_password}';")

            connection.commit()  # تأكيد التغييرات
            cursor.close()
            connection.close()
            QMessageBox.information(self, "قاعدة البيانات", f"تم تغيير كلمة المرور بنجاح!")
    except Error as e:
        pass

#انشاء مستخدم pme
def pme_user(self):
    add_registry_key(self)
    # استخدام الدالة مع كلمة المرور الجديدة
    change_root_password(self,"kh123456")
    # بيانات المستخدم الجديد
    new_user = 'pme'
    new_password = 'kh123456'
    new_host = host
    try:
        connection = mysql.connector.connect(host=host,user=user_r,password=password_r)
        cursor = connection.cursor()
        query_check_user = f"SELECT User FROM mysql.user WHERE User = '{new_user}' AND Host = '{new_host}';"
        cursor.execute(query_check_user)
        user_exists = cursor.fetchone()
        if not user_exists:
            query_create_user = f"CREATE USER '{new_user}'@'{new_host}' idENTIFIED BY '{new_password}';"
            query_grant_privileges = f"GRANT ALL PRIVILEGES ON *.* TO '{new_user}'@'{new_host}' WITH GRANT OPTION;"
            query_flush_privileges = "FLUSH PRIVILEGES;"
            cursor.execute(query_create_user)
            cursor.execute(query_grant_privileges)
            cursor.execute(query_flush_privileges)
            connection.commit()  # لحفظ التغييرات
        # إغلاق الاتصال
        cursor.close()
        connection.close()
    except mysql.connector.Error as err:
        if err.errno == errorcode.ER_ACCESS_DENIED_ERROR:
            QMessageBox.critical(self, ' قاعدة البيانات', f"خطأ: اسم المستخدم أو كلمة المرور غير صحيحة.")
            sys.exit()
        elif err.errno == errorcode.ER_BAD_DB_ERROR:
            QMessageBox.critical(self, ' قاعدة البيانات', f"خطأ: قاعدة البيانات غير موجودة.")
            sys.exit()
        else:
            QMessageBox.critical(self, ' قاعدة البيانات', f"خطأ غير متوقع: {err}")
            sys.exit()
    except Exception as e:
        QMessageBox.critical(self, ' قاعدة البيانات', f"حدث خطأ: هناك مشكلة في الوصول لقاعدة البيانات تحقق من كلمة المرور او اتصل بالمطور {e}")
        sys.exit()
    except Error as e:
        QMessageBox.critical(self, ' قاعدة البيانات', f'حدثت مشكلة اثناء الإتصال بقاعدة البيانات ... قم بإعادة تشغيل الكمبيوتر ... في حالة لم يتم حل المشكلة اتصل بالمطور')
        sys.exit()

#ايقونه الغاء التثبيت
def find_correct_subkey():
    try:
        # تحديد المسار الرئيسي
        root = reg.HKEY_LOCAL_MACHINE
        parent_key = r"Software\Microsoft\Windows\CurrentVersion\Uninstall"
        # فتح المفتاح الرئيسي
        with reg.OpenKey(root, parent_key, 0, reg.KEY_READ) as key:
            # تعداد المفاتيح الفرعية
            i = 0
            while True:
                try:
                    subkey_name = reg.EnumKey(key, i)
                    if "منظومة المهندس1" in subkey_name:
                        return f"{parent_key}\\{subkey_name}"
                    i += 1
                except OSError:
                    break
        return None
    except Exception as e:
        print(f"حدث خطأ أثناء البحث عن المفتاح: {e}")
        return None

#ايقونه الغاء التثبيت
def add_registry_key(self):
    try:
        # البحث عن المفتاح الفرعي الصحيح
        subkey = find_correct_subkey()
        if not subkey:
            print("لم يتم العثور على مفتاح مناسب.")
            return
        # فتح المفتاح أو إنشاؤه إذا لم يكن موجودًا
        root = reg.HKEY_LOCAL_MACHINE
        key = reg.CreateKeyEx(root, subkey, 0, reg.KEY_WRITE)

        # تحديد قيمة المفتاح
        value_name = "DisplayIcon"
        value_data = r"C:\\Program Files\\منظومة المهندس1\\icon_app.ico"
        # إضافة القيمة إلى الريجستري
        reg.SetValueEx(key, value_name, 0, reg.REG_SZ, value_data)
        # إغلاق المفتاح
        reg.CloseKey(key)
    except PermissionError:
        QMessageBox.critical(self, 'ايقونه الغاء التثبيت', "تحتاج إلى تشغيل السكربت كمسؤول.")
        sys.exit()
    except Exception as e:
        QMessageBox.critical(self, 'ايقونه الغاء التثبيت', f"حدث خطأ: {e}")




#اعدادات المستخدم///////////////////////////////////////////////////////
def ueser_database(self):
    try:
        #selected_year = self.year_combo.currentText()
        #db_name = f"project_manager_V2"
        db_name = f"project_manager2_user"
        conn = mysql.connector.connect(host=host, user=user, password=password)
        cursor = conn.cursor()

        # إنشاء قاعدة بيانات إذا لم تكن موجودة
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name}")
        cursor.execute(f"USE {db_name}")

        # تعريف الجداول والأعمدة المطلوبة
        tables = {
            "users": [
                ("id", "INT AUTO_INCREMENT PRIMARY KEY"),
                ("username", "TEXT"),
                ("password_hash", "TEXT"),
                ("user_permissions", "TEXT"),
                ("Security_question", "TEXT"),
                ("Permissions_details", "TEXT"),

            ],

            "company": [
                ("id", "INT AUTO_INCREMENT PRIMARY KEY"),
                ("company_name", "TEXT"),
                ("company_logo", "TEXT"),
                ("Currency_type", "TEXT"),
                ("phone_number", "TEXT"),     # رقم الهاتف
                ("address", "TEXT"),
                ("email", "TEXT")

            ],

            "License_Status": [
              ("id", "INT AUTO_INCREMENT PRIMARY KEY"),
                ("license_type" ,"TEXT"),
                ("start_date" ,"TEXT"),
                ("end_date" ,"TEXT"),
            ],
        }

        # التحقق من وجود الجداول وتحديث الأعمدة
        for table_name, columns in tables.items():
            cursor.execute(f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(f'{col} {dtype}' for col, dtype in columns)})")
            cursor.execute(f"SHOW COLUMNS FROM {table_name}")
            existing_columns = {row[0] for row in cursor.fetchall()}

            # إضافة أعمدة جديدة إذا لم تكن موجودة
            for column_name, column_type in columns:
                if column_name not in existing_columns:
                    cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}")

            # حذف الأعمدة غير المطلوبة
            for column in existing_columns - {col[0] for col in columns}:
                cursor.execute(f"ALTER TABLE {table_name} DROP COLUMN {column}")

            # تعديل ترتيب الأعمدة (يتطلب إعادة إنشاء الجدول)
            reordered_columns = ", ".join([f"{col[0]} {col[1]}" for col in columns])
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {table_name}_temp (
                    {reordered_columns}
                )
            """)
            cursor.execute(f"INSERT INTO {table_name}_temp SELECT * FROM {table_name}")
            cursor.execute(f"DROP TABLE {table_name}")
            cursor.execute(f"RENAME TABLE {table_name}_temp TO {table_name}")


            # التحقق من وجود الفهارس قبل إنشائها
        def create_index_if_not_exists(table, index_name, column, prefix_length=None):
            cursor.execute(f"SHOW INDEX FROM {table} WHERE Key_name = '{index_name}'")
            if not cursor.fetchone():
                if prefix_length:
                    cursor.execute(f"CREATE INDEX {index_name} ON {table} ({column}({prefix_length}))")
                else:
                    cursor.execute(f"CREATE INDEX {index_name} ON {table} ({column})")

        # إنشاء الفهارس
        # create_index_if_not_exists('المشاريع', 'idx_المشاريع_الحالة', 'الحالة', 255)
        

        conn.commit()
        conn.close()

    except mysql.connector.Error as e:
        QMessageBox.critical(self, 'المستخدم قاعدة البيانات', f'لم يتم الوصول لقاعدة البيانات ... اتصل بالمطور لحل المشكلة\n{str(e)}')
        sys.exit()

#سجل الديون db ///////////////////////////////////////////////////////
def debts_database(self):
    try:
        #selected_year = self.year_combo.currentText()
        #db_name = f"project_manager_V2"
        db_name = f"project_manager2_debts"
        conn = mysql.connector.connect(host=host, user=user, password=password)
        cursor = conn.cursor()

        # إنشاء قاعدة بيانات إذا لم تكن موجودة
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name}")
        cursor.execute(f"USE {db_name}")

        # تعريف الجداول والأعمدة المطلوبة
        tables = {
            "حسابات_الديون": [
                ("id", "INT AUTO_INCREMENT PRIMARY KEY"),
                ("اسم_الحساب", "TEXT"),
                ("رقم_الهاتف", "TEXT"),
                ("نوع_الحساب", "TEXT"),
                ("الوصف", "TEXT"),
                ("المبلغ", "INT DEFAULT 0" ),
                ("المدفوع", "INT DEFAULT 0" ),
                ("الباقي", "INT DEFAULT 0" ),
                ("التاريخ", "DATE"),
                ("ملاحظات", "TEXT"),

            ],

            "سجل_الديون": [
                ("id", "INT AUTO_INCREMENT PRIMARY KEY"),
                ("معرف_الحساب" ,"INT"),
                ("اسم_الحساب", "TEXT"),
                ("نوع_الحساب" ,"TEXT"),
                ("الوصف", "TEXT"),
                ("المبلغ", "INT DEFAULT 0"),
                ("الباقي", "INT DEFAULT 0"),
                ("تاريخ_الدين", "DATE"),
                ("تاريخ_السداد", "TEXT"),
                ("ملاحظات", "TEXT"),

            ],

            "دفعات_الديون": [
              ("id", "INT AUTO_INCREMENT PRIMARY KEY"),
                ("معرف_الحساب" ,"INT"),
                ("اسم_الحساب", "TEXT"),
                ("نوع_الحساب" ,"TEXT"),
                ("وصف_المدفوع" ,"TEXT"),
                ("المبلغ_المدفوع" ,"INT DEFAULT 0"),
                ("تاريخ_الدفع" ,"DATE"),
                ("رقم_الفاتورة", "TEXT"),
                ("ملاحظات", "TEXT"),
            ],
        }

        # التحقق من وجود الجداول وتحديث الأعمدة
        for table_name, columns in tables.items():
            cursor.execute(f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(f'{col} {dtype}' for col, dtype in columns)})")
            cursor.execute(f"SHOW COLUMNS FROM {table_name}")
            existing_columns = {row[0] for row in cursor.fetchall()}

            # إضافة أعمدة جديدة إذا لم تكن موجودة
            for column_name, column_type in columns:
                if column_name not in existing_columns:
                    cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}")

            # حذف الأعمدة غير المطلوبة
            for column in existing_columns - {col[0] for col in columns}:
                cursor.execute(f"ALTER TABLE {table_name} DROP COLUMN {column}")

            # تعديل ترتيب الأعمدة (يتطلب إعادة إنشاء الجدول)
            reordered_columns = ", ".join([f"{col[0]} {col[1]}" for col in columns])
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {table_name}_temp (
                    {reordered_columns}
                )
            """)
            cursor.execute(f"INSERT INTO {table_name}_temp SELECT * FROM {table_name}")
            cursor.execute(f"DROP TABLE {table_name}")
            cursor.execute(f"RENAME TABLE {table_name}_temp TO {table_name}")


            # التحقق من وجود الفهارس قبل إنشائها
        def create_index_if_not_exists(table, index_name, column, prefix_length=None):
            cursor.execute(f"SHOW INDEX FROM {table} WHERE Key_name = '{index_name}'")
            if not cursor.fetchone():
                if prefix_length:
                    cursor.execute(f"CREATE INDEX {index_name} ON {table} ({column}({prefix_length}))")
                else:
                    cursor.execute(f"CREATE INDEX {index_name} ON {table} ({column})")

        # إنشاء الفهارس
        # create_index_if_not_exists('المشاريع', 'idx_المشاريع_الحالة', 'الحالة', 255)
        

        conn.commit()
        conn.close()

        return

    except mysql.connector.Error as e:
        QMessageBox.critical(self, 'المستخدم قاعدة البيانات', f'لم يتم الوصول لقاعدة البيانات ... اتصل بالمطور لحل المشكلة\n{str(e)}')
        sys.exit()

# جدوالترخيص  ========================================================================================
def DB_license_status(self,license_type,end_date_input, start_date=None):
    change_root_password(self,"kh123456")
    try:
        db_name = "project_manager2_user"
        conn = mysql.connector.connect(host=host, user=user_r, password=password_r)
        cursor = conn.cursor()
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name}")
        cursor.execute(f"USE {db_name}")
        # تعريف جدول الترخيص
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS License_Status (
                id INT AUTO_INCREMENT PRIMARY KEY,
                license_type TEXT,
                start_date TEXT,
                end_date TEXT
            )
        """)
        # حساب تاريخ النهاية بناءً على نوع الترخيص
        if start_date is None:
            start_date = datetime.now()

        if license_type == "trial":
            end_date = end_date_input
        elif license_type == "annual":
            end_date = end_date_input
        elif license_type == "permanent":
            end_date = "permanent"
        else:
            raise ValueError("Invalid license type.")
        # تحويل التواريخ إلى نصوص
        start_date_str = start_date.strftime('%Y-%m-%d')
        # تشفير البيانات
        hashed_license_type = bcrypt.hashpw(license_type.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        hashed_start_date = bcrypt.hashpw(start_date_str.encode('utf-8'), bcrypt.gensalt()).decode('utf-8') #if end_date != "permanent" else "permanent"
        hashed_end_date = bcrypt.hashpw(end_date.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        # تحديث أو إدخال بيانات الترخيص
        cursor.execute("SELECT id FROM License_Status LIMIT 1")
        if cursor.fetchone():
            cursor.execute("""
                UPDATE License_Status
                SET license_type = %s, start_date = %s, end_date = %s
                WHERE id = 1
            """, (hashed_license_type, hashed_start_date, hashed_end_date))
        else:
            cursor.execute("""
                INSERT INTO License_Status (license_type, start_date, end_date)
                VALUES (%s, %s, %s)
            """, (hashed_license_type, hashed_start_date, hashed_end_date))

        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Error updating license status: {e}")


#------------استيراد ملفات اكسل ---------------------
def open_excel_to_db_dialog(self,parent=None):
    # Initialize dialog
    dialog = QDialog(self)
    dialog.setLayoutDirection(Qt.RightToLeft)
    dialog.setWindowTitle("Excel to Database Importer")
    dialog.setGeometry(100, 100, 1200, 800)
    selected_year = self.year_combo.currentText()
    db_name = f"project_manager_V2"

    # Initialize database connection
    conn = None
    cursor = None
    table_columns = {}

    try:
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        # Fetch table names
        cursor.execute("SHOW TABLES")
        tables = [row[0] for row in cursor.fetchall()]

        # Fetch columns for each table
        for table in tables:
            cursor.execute(f"DESCRIBE {table}")
            columns = cursor.fetchall()
            table_columns[table] = [(col[0], str(col[1], encoding="utf-8").split('(')[0].upper() if isinstance(col[1], bytes) else col[1].split('(')[0].upper()) for col in columns if col[0] != "id"]

    except mysql.connector.Error as err:
        QMessageBox.critical(dialog, "خطأ", f"فشل الاتصال بقاعدة البيانات: {err}")
        return

    # Store Excel data and mappings
    excel_df = None
    excel_columns = []
    column_mappings = {}
    selected_db_columns = set()

    # Main widget and layout
    main_widget = QWidget()
    main_layout = QVBoxLayout(main_widget)

    # Table selection
    table_selection_layout = QHBoxLayout()
    table_combo = QComboBox()
    table_combo.addItems(table_columns.keys())
    table_selection_layout.addWidget(QLabel("اختر الجدول:"))
    # table_combo.setStyleSheet("""
    #     QComboBox {
    #         font-size: 14px;
    #         padding: 5px;
    #     }
    #     QComboBox::drop-down {
    #         width: 20px;
    #     }
    # """)
    table_selection_layout.addWidget(table_combo)

    # Excel file upload
    upload_button = QPushButton("رفع ملف Excel")
    # upload_button.setStyleSheet("""
    #     QPushButton {
    #         font-size: 14px;
    #         padding: 5px;
    #     }
    # """)
    table_selection_layout.addWidget(upload_button)

    main_layout.addLayout(table_selection_layout)

    # Table columns display
    table_columns_table = QTableWidget()
    table_columns_table.setRowCount(1)
    table_columns_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
    main_layout.addWidget(QLabel("أعمدة الجدول في قاعدة البيانات:"))
    main_layout.addWidget(table_columns_table)

    # Excel columns and data preview (merged)
    mapping_table = QTableWidget()
    mapping_table.setRowCount(4)
    mapping_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
    main_layout.addWidget(QLabel("أعمدة ملف Excel ومعاينة البيانات (أول 4 صفوف):"))
    main_layout.addWidget(mapping_table)

    # Mapping combo boxes table
    combo_table = QTableWidget()
    combo_table.setRowCount(1)
    combo_table.setColumnCount(0)
    combo_table.setVerticalHeaderLabels(["عمود قاعدة البيانات"])
    combo_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
    main_layout.addWidget(QLabel("ربط أعمدة Excel بأعمدة قاعدة البيانات:"))
    main_layout.addWidget(combo_table)

    # Import button
    import_button = QPushButton("استيراد البيانات")
    import_button.setStyleSheet("""
        QPushButton {
            font-size: 14px;
            padding: 5px;
        }
    """)
    main_layout.addWidget(import_button)
    center_all_widgets(table_selection_layout)
    center_all_widgets(dialog)
    center_all_widgets(main_layout)
    center_all_widgets(main_widget)

    table_setting(combo_table)
    table_setting(mapping_table)
    #table_setting(table_columns_table)



    dialog.setLayout(main_layout)

    def display_table_columns(table_name):
        columns = table_columns.get(table_name, [])
        table_columns_table.setColumnCount(len(columns))
        table_columns_table.setRowCount(1)

        table_columns_table.setHorizontalHeaderLabels([col[0] for col in columns])

        for col_idx, (col_name, col_type) in enumerate(columns):
            #table_columns_table.setItem(0, col_idx, QTableWidgetItem(col_name))
            display_type = get_display_type(col_type)
            table_columns_table.setItem(0, col_idx, QTableWidgetItem(display_type))

        table_columns_table.resizeColumnsToContents()
        table_columns_table.hide()

    def get_display_type(col_type):
        if col_type in ["FLOAT", "INT", "INTEGER"]:
            return "رقم"
        elif col_type == "DATE":
            return "تاريخ"
        else:
            return "نص"

    def on_table_changed(table_name):
        display_table_columns(table_name)
        if excel_columns:
            selected_db_columns.clear()
            column_mappings.clear()
            display_excel_columns()

    def upload_excel():
        nonlocal excel_df, excel_columns
        file_path, _ = QFileDialog.getOpenFileName(dialog, "اختر ملف Excel", "", "Excel Files (*.xlsx *.xls)")
        if file_path:
            try:
                excel_df = pd.read_excel(file_path)
                excel_columns = excel_df.columns.tolist()
                selected_db_columns.clear()
                column_mappings.clear()
                display_excel_columns()
            except Exception as e:
                QMessageBox.critical(dialog, "خطأ", f"فشل تحميل ملف Excel: {e}")

    def display_excel_columns():
        mapping_table.setRowCount(3)
        mapping_table.setColumnCount(len(excel_columns))
        combo_table.setColumnCount(len(excel_columns))

        mapping_table.setHorizontalHeaderLabels(excel_columns)
        combo_table.setHorizontalHeaderLabels(excel_columns)

        selected_table = table_combo.currentText()
        db_columns = [""] + [col[0] for col in table_columns.get(selected_table, [])]

        for col_idx, excel_col in enumerate(excel_columns):
            #mapping_table.setItem(0, col_idx, QTableWidgetItem(excel_col))

            col_data = excel_df[excel_col]
            if pd.api.types.is_integer_dtype(col_data):
                data_type = "رقم"
            elif pd.api.types.is_float_dtype(col_data):
                data_type = "رقم"
            elif pd.api.types.is_datetime64_any_dtype(col_data):
                data_type = "تاريخ"
            else:
                data_type = "نص"
            mapping_table.setItem(0, col_idx, QTableWidgetItem(data_type))

            combo = QComboBox()
            available_columns = [""] + [col for col in db_columns[1:] if col not in selected_db_columns]
            combo.addItems(available_columns)
            combo.currentTextChanged.connect(lambda text, idx=col_idx: on_combo_changed(text, idx))
            combo_table.setCellWidget(0, col_idx, combo)

        preview_rows = min(3, len(excel_df))
        for row_idx in range(preview_rows):
            for col_idx, excel_col in enumerate(excel_columns):
                value = excel_df.iloc[row_idx][excel_col]
                value_str = str(value) if pd.notnull(value) else ""
                mapping_table.setItem(row_idx + 1, col_idx, QTableWidgetItem(value_str))

        for row_idx in range(preview_rows, 3):
            for col_idx in range(len(excel_columns)):
                mapping_table.setItem(row_idx + 1, col_idx, QTableWidgetItem(""))

        mapping_table.resizeColumnsToContents()
        combo_table.resizeColumnsToContents()

    def on_combo_changed(selected_db_col, col_idx):
        combo = combo_table.cellWidget(0, col_idx)
        previous_selection = combo.property("previous_selection") or ""

        if previous_selection and previous_selection in selected_db_columns:
            selected_db_columns.remove(previous_selection)
        if selected_db_col:
            selected_db_columns.add(selected_db_col)

        combo.setProperty("previous_selection", selected_db_col)

        selected_table = table_combo.currentText()
        db_columns = [""] + [col[0] for col in table_columns.get(selected_table, [])]

        for idx in range(len(excel_columns)):
            combo = combo_table.cellWidget(0, idx)
            current_selection = combo.currentText()
            combo.blockSignals(True)
            combo.clear()
            available_columns = [""] + [col for col in db_columns[1:] if col not in selected_db_columns or col == current_selection]
            combo.addItems(available_columns)
            combo.setCurrentText(current_selection)
            combo.blockSignals(False)

    def validate_data_types():
        selected_table = table_combo.currentText()
        db_columns = {col[0]: col[1] for col in table_columns.get(selected_table, [])}
        column_mappings.clear()

        for col_idx, excel_col in enumerate(excel_columns):
            combo = combo_table.cellWidget(0, col_idx)
            db_col = combo.currentText()
            data_type_item = mapping_table.item(1, col_idx)
            excel_data_type = data_type_item.text() if data_type_item else "نص"

            if db_col:
                db_data_type = db_columns.get(db_col)
                if db_data_type in ["TEXT", "VARCHAR"]:
                    pass
                elif excel_data_type == "رقم" and db_data_type not in ["INT", "INTEGER", "FLOAT"]:
                    return False, f"نوع بيانات العمود '{excel_col}' (رقم) لا يتطابق مع '{db_col}' ({db_data_type})"
                elif excel_data_type == "تاريخ" and db_data_type != "DATE":
                    return False, f"نوع بيانات العمود '{excel_col}' (تاريخ) لا يتطابق مع '{db_col}' ({db_data_type})"
                elif excel_data_type == "نص" and db_data_type not in ["TEXT", "VARCHAR", "CHAR"]:
                    return False, f"نوع بيانات العمود '{excel_col}' (نص) لا يتطابق مع '{db_col}' ({db_data_type})"
                column_mappings[excel_col] = db_col

        return True, ""

    def import_data():
        if excel_df is None:
            QMessageBox.warning(dialog, "تحذير", "يرجى رفع ملف Excel أولاً")
            return

        selected_table = table_combo.currentText()
        valid, error_msg = validate_data_types()

        if not valid:
            QMessageBox.critical(dialog, "خطأ", error_msg)
            return

        try:
            columns = list(column_mappings.values())
            if not columns:
                conn.rollback()
                QMessageBox.warning(dialog, "تحذير", "يرجى ربط عمود واحد على الأقل")
                return

            placeholders = ", ".join(["%s"] * len(columns))
            query = f"INSERT INTO {selected_table} ({', '.join(columns)}) VALUES ({placeholders})"

            for _, row in excel_df.iterrows():
                values = []
                is_empty_row = True
                for excel_col in column_mappings.keys():
                    value = row[excel_col]
                    if pd.isna(value) or value == "":
                        values.append(None)
                    else:
                        values.append(value)
                        is_empty_row = False

                if not is_empty_row:
                    cursor.execute(query, values)

            conn.commit()
            QMessageBox.information(dialog, "نجاح", "تم استيراد البيانات بنجاح")

        except mysql.connector.Error as err:
            conn.rollback()
            QMessageBox.critical(dialog, "خطأ", f"فشل استيراد البيانات: {err}")

    table_combo.currentTextChanged.connect(on_table_changed)
    upload_button.clicked.connect(upload_excel)
    import_button.clicked.connect(import_data)

    #display_table_columns(table_combo.currentText())

    dialog.exec_()

    if conn:
        conn.close()
