#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار وظائف قاعدة البيانات المطورة
Test script for the implemented database functions
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QDate, Qt

# إضافة المسار الحالي للبحث عن الوحدات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from مراحل_المشروع import ProjectPhasesWindow, EngineerTaskDialog, TimelineEntryDialog, PhaseDialog
    print("✅ تم استيراد الوحدات بنجاح")
except ImportError as e:
    print(f"❌ فشل في استيراد الوحدات: {e}")
    sys.exit(1)

def test_dialog_creation(app):
    """اختبار إنشاء الحوارات"""
    try:
        # اختبار حوار مهام المهندسين
        print("🧪 اختبار حوار مهام المهندسين...")
        engineer_dialog = EngineerTaskDialog(project_id=1)
        print("✅ تم إنشاء حوار مهام المهندسين بنجاح")

        # اختبار الحقول الجديدة في حوار مهام المهندسين
        print("🧪 اختبار الحقول الجديدة...")
        if hasattr(engineer_dialog, 'start_date'):
            print("✅ حقل تاريخ البدء موجود")
        else:
            print("❌ حقل تاريخ البدء غير موجود")
            return False

        if hasattr(engineer_dialog, 'end_date'):
            print("✅ حقل تاريخ الانتهاء موجود")
        else:
            print("❌ حقل تاريخ الانتهاء غير موجود")
            return False

        if hasattr(engineer_dialog, 'task_status_combo'):
            print("✅ حقل حالة المهمة موجود")
        else:
            print("❌ حقل حالة المهمة غير موجود")
            return False

        if hasattr(engineer_dialog, 'validate_dates'):
            print("✅ دالة التحقق من التواريخ موجودة")
        else:
            print("❌ دالة التحقق من التواريخ غير موجودة")
            return False

        # اختبار تحسينات عرض البيانات في حوار مهام المهندسين
        print("🔍 اختبار تحسين عرض المراحل في حوار مهام المهندسين...")
        phase_count = engineer_dialog.phase_combo.count()
        if phase_count > 0:
            sample_phase_text = engineer_dialog.phase_combo.itemText(0)
            print(f"✅ عينة من عرض المراحل: {sample_phase_text}")
        else:
            print("ℹ️ لا توجد مراحل في المشروع للاختبار")

        print("🔍 اختبار تحسين عرض المهندسين في حوار مهام المهندسين...")
        engineer_count = engineer_dialog.engineer_combo.count()
        if engineer_count > 0:
            sample_engineer_text = engineer_dialog.engineer_combo.itemText(0)
            if " - " in sample_engineer_text:
                print("✅ تنسيق عرض المهندسين محسن (اسم - وظيفة)")
            else:
                print("⚠️ تنسيق عرض المهندسين قد يحتاج تحسين")
        else:
            print("ℹ️ لا توجد مهندسين في قاعدة البيانات للاختبار")

        # اختبار حوار الجدول الزمني
        print("🧪 اختبار حوار الجدول الزمني...")
        timeline_dialog = TimelineEntryDialog(project_id=1)
        print("✅ تم إنشاء حوار الجدول الزمني بنجاح")

        # اختبار حوار المراحل المحسن
        print("🧪 اختبار حوار المراحل المحسن...")
        phase_dialog = PhaseDialog(project_id=1)
        print("✅ تم إنشاء حوار المراحل بنجاح")

        # اختبار الحقول الجديدة في حوار المراحل
        if hasattr(phase_dialog, 'engineer_combo'):
            print("✅ حقل المهندس المسؤول موجود")
        else:
            print("❌ حقل المهندس المسؤول غير موجود")
            return False

        if hasattr(phase_dialog, 'engineer_percentage_spin'):
            print("✅ حقل نسبة المهندس موجود")
        else:
            print("❌ حقل نسبة المهندس غير موجود")
            return False

        if hasattr(phase_dialog, 'engineer_amount_spin'):
            print("✅ حقل مبلغ المهندس موجود")
        else:
            print("❌ حقل مبلغ المهندس غير موجود")
            return False

        if hasattr(phase_dialog, 'create_engineer_task'):
            print("✅ دالة إنشاء مهمة المهندس التلقائية موجودة")
        else:
            print("❌ دالة إنشاء مهمة المهندس التلقائية غير موجودة")
            return False

        if hasattr(phase_dialog, 'on_engineer_changed'):
            print("✅ دالة التعبئة التلقائية للنسبة موجودة")
        else:
            print("❌ دالة التعبئة التلقائية للنسبة غير موجودة")
            return False

        # اختبار تحسينات عرض البيانات
        print("🧪 اختبار تحسينات عرض البيانات...")

        # اختبار تحسين عرض المهندسين
        print("🔍 اختبار تحسين عرض المهندسين...")
        engineer_count = phase_dialog.engineer_combo.count()
        if engineer_count > 1:  # أكثر من الخيار الافتراضي
            sample_text = phase_dialog.engineer_combo.itemText(1)
            if " - " in sample_text:
                print("✅ تنسيق عرض المهندسين محسن (اسم - وظيفة)")
            else:
                print("⚠️ تنسيق عرض المهندسين قد يحتاج تحسين")
        else:
            print("ℹ️ لا توجد مهندسين في قاعدة البيانات للاختبار")

        # اختبار تحسينات الجدول الزمني
        print("🧪 اختبار تحسينات الجدول الزمني...")

        # اختبار هيكل الجدول الزمني الجديد
        timeline_headers = timeline_dialog.timeline_table.horizontalHeaderLabels() if hasattr(timeline_dialog, 'timeline_table') else []
        expected_timeline_headers = ["ID", "الرقم", "المهندس", "وصف المرحلة", "تاريخ البدء", "تاريخ الانتهاء", "حالة المهمة"]

        if timeline_headers == expected_timeline_headers:
            print("✅ هيكل الجدول الزمني محدث بنجاح")
        else:
            print(f"⚠️ هيكل الجدول الزمني: متوقع {expected_timeline_headers}, موجود {timeline_headers}")

        # اختبار هيكل جدول مهام المهندسين المحدث
        engineers_headers = engineer_dialog.engineers_tasks_table.horizontalHeaderLabels() if hasattr(engineer_dialog, 'engineers_tasks_table') else []
        expected_engineers_headers = ["ID", "الرقم", "المهندس", "وصف المرحلة", "% النسبة", "مبلغ المهندس", "حالة المبلغ"]

        if engineers_headers == expected_engineers_headers:
            print("✅ هيكل جدول مهام المهندسين محدث بنجاح (إزالة أعمدة التواريخ والحالة)")
        else:
            print(f"⚠️ هيكل جدول مهام المهندسين: متوقع {expected_engineers_headers}, موجود {engineers_headers}")

        return True

    except Exception as e:
        print(f"❌ فشل في إنشاء الحوارات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_project_window(app):
    """اختبار نافذة المشروع"""
    
    # بيانات مشروع تجريبية
    project_data = {
        'id': 1,
        'اسم_المشروع': 'مشروع اختبار',
        'معرف_العميل': 1,
        'معرف_المهندس': 1,
        'المبلغ': 50000.0,
        'المدفوع': 25000.0,
        'تاريخ_الإستلام': '2024-01-01',
        'تاريخ_التسليم': '2024-12-31',
        'الحالة': 'قيد الإنجاز',
        'وصف_المشروع': 'مشروع اختبار للوظائف الجديدة',
        'ملاحظات': 'ملاحظات تجريبية'
    }
    
    try:
        print("🧪 اختبار نافذة المشروع...")
        window = ProjectPhasesWindow(None, project_data, "تصميم")
        print("✅ تم إنشاء نافذة المشروع بنجاح")
        
        # اختبار وجود الدوال المطلوبة
        required_methods = [
            'add_engineer_task',
            'edit_engineer_task',
            'delete_engineer_task',
            'add_timeline_entry',
            'edit_timeline_entry',
            'delete_timeline_entry'
        ]

        for method_name in required_methods:
            if hasattr(window, method_name):
                print(f"✅ الدالة {method_name} موجودة")
            else:
                print(f"❌ الدالة {method_name} غير موجودة")
                return False

        # اختبار وجود دوال النقر المزدوج
        double_click_methods = [
            'on_phases_table_double_click',
            'on_engineers_tasks_table_double_click',
            'on_timeline_table_double_click',
            'on_attachments_table_double_click',
            'on_expenses_table_double_click',
            'on_custody_table_double_click',
            'on_custody_payments_table_double_click',
            'on_contractors_table_double_click',
            'on_workers_table_double_click',
            'on_suppliers_table_double_click'
        ]

        for method_name in double_click_methods:
            if hasattr(window, method_name):
                print(f"✅ دالة النقر المزدوج {method_name} موجودة")
            else:
                print(f"❌ دالة النقر المزدوج {method_name} غير موجودة")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء نافذة المشروع: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        import mysql.connector
        from for_all import host, user_r, password_r
        
        print("🧪 اختبار الاتصال بقاعدة البيانات...")
        
        conn = mysql.connector.connect(
            host=host, 
            user=user_r, 
            password=password_r,
            database="project_manager_V2"
        )
        
        cursor = conn.cursor()
        
        # اختبار وجود الجداول المطلوبة
        tables_to_check = [
            'المشاريع_مهام_المهندسين',
            'المشاريع_الجدول_الزمني',
            'الموظفين_المهام'
        ]
        
        for table_name in tables_to_check:
            cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            result = cursor.fetchone()
            if result:
                print(f"✅ الجدول {table_name} موجود")
            else:
                print(f"❌ الجدول {table_name} غير موجود")
        
        conn.close()
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ فشل في الاتصال بقاعدة البيانات: {e}")
        return False

def test_auto_refresh_functionality(app):
    """اختبار وظيفة التحديث التلقائي للتابات"""
    try:
        print("🧪 اختبار وظيفة التحديث التلقائي...")

        # إنشاء نافذة مراحل المشروع
        project_data = {'id': 1, 'اسم_المشروع': 'مشروع اختبار'}
        project_window = ProjectPhasesWindow(project_data=project_data)

        # اختبار وجود دالة التحديث التلقائي
        if hasattr(project_window, 'on_tab_changed'):
            print("✅ دالة التحديث التلقائي موجودة")
        else:
            print("❌ دالة التحديث التلقائي غير موجودة")
            return False

        # اختبار وجود دالة تمديد الأعمدة
        if hasattr(project_window, 'extend_table_columns'):
            print("✅ دالة تمديد أعمدة الجداول موجودة")
        else:
            print("❌ دالة تمديد أعمدة الجداول غير موجودة")
            return False

        # اختبار ربط الإشارة
        try:
            # محاولة تشغيل دالة التحديث التلقائي
            project_window.on_tab_changed(0)
            print("✅ دالة التحديث التلقائي تعمل بنجاح")
        except Exception as e:
            print(f"⚠️ خطأ في تشغيل دالة التحديث التلقائي: {e}")

        return True

    except Exception as e:
        print(f"❌ فشل في اختبار التحديث التلقائي: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_percentage_loading(app):
    """اختبار وظيفة التعبئة التلقائية للنسبة"""
    try:
        print("🧪 اختبار وظيفة التعبئة التلقائية للنسبة...")

        # إنشاء حوار مهمة المهندس
        engineer_dialog = EngineerTaskDialog(project_id=1)

        # اختبار وجود الدوال المطلوبة
        if hasattr(engineer_dialog, 'on_engineer_changed'):
            print("✅ دالة معالجة تغيير المهندس موجودة")
        else:
            print("❌ دالة معالجة تغيير المهندس غير موجودة")
            return False

        if hasattr(engineer_dialog, 'get_engineer_default_percentage'):
            print("✅ دالة جلب النسبة الافتراضية موجودة")
        else:
            print("❌ دالة جلب النسبة الافتراضية غير موجودة")
            return False

        # اختبار وجود العلامة لتتبع التغيير اليدوي
        if hasattr(engineer_dialog, '_engineer_changed_manually'):
            print("✅ علامة تتبع التغيير اليدوي موجودة")
        else:
            print("❌ علامة تتبع التغيير اليدوي غير موجودة")
            return False

        # اختبار تحميل المهندسين مع النسب الافتراضية
        if engineer_dialog.engineer_combo.count() > 0:
            print("✅ تم تحميل المهندسين بنجاح")

            # اختبار وجود البيانات الإضافية للمهندسين
            first_item_data = engineer_dialog.engineer_combo.itemData(0, Qt.UserRole + 1)
            if first_item_data and isinstance(first_item_data, dict):
                print("✅ البيانات الإضافية للمهندسين محفوظة بنجاح")
                if 'default_percentage' in first_item_data:
                    print("✅ النسبة الافتراضية محفوظة في البيانات الإضافية")
                else:
                    print("⚠️ النسبة الافتراضية غير موجودة في البيانات الإضافية")
            else:
                print("⚠️ البيانات الإضافية للمهندسين غير محفوظة")
        else:
            print("⚠️ لا توجد مهندسين في قائمة الاختيار")

        # اختبار ربط الإشارة
        try:
            # محاولة تشغيل دالة معالجة تغيير المهندس
            engineer_dialog.on_engineer_changed()
            print("✅ دالة معالجة تغيير المهندس تعمل بنجاح")
        except Exception as e:
            print(f"⚠️ خطأ في تشغيل دالة معالجة تغيير المهندس: {e}")

        # اختبار دالة جلب النسبة الافتراضية
        try:
            default_percentage = engineer_dialog.get_engineer_default_percentage(1)
            print(f"✅ دالة جلب النسبة الافتراضية تعمل بنجاح (النسبة: {default_percentage}%)")
        except Exception as e:
            print(f"⚠️ خطأ في دالة جلب النسبة الافتراضية: {e}")

        return True

    except Exception as e:
        print(f"❌ فشل في اختبار التعبئة التلقائية للنسبة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_phases_table_column_order(app):
    """اختبار ترتيب أعمدة جدول المراحل"""
    try:
        print("🧪 اختبار ترتيب أعمدة جدول المراحل...")

        # إنشاء نافذة مراحل المشروع
        project_data = {'id': 1, 'اسم_المشروع': 'مشروع اختبار'}
        project_window = ProjectPhasesWindow(project_data=project_data)

        # اختبار ترتيب الأعمدة في جدول المراحل
        if hasattr(project_window, 'phases_table'):
            expected_headers = ["ID", "الرقم", "اسم المرحلة", "وصف المرحلة", "الوحدة", "الكمية", "السعر", "الإجمالي", "ملاحظات", "حالة المبلغ"]
            actual_headers = []

            for i in range(project_window.phases_table.columnCount()):
                header_text = project_window.phases_table.horizontalHeaderItem(i)
                if header_text:
                    actual_headers.append(header_text.text())
                else:
                    actual_headers.append("")

            if actual_headers == expected_headers:
                print("✅ ترتيب أعمدة جدول المراحل صحيح")
                print(f"   الترتيب الجديد: {actual_headers}")

                # التحقق من أن حالة المبلغ في العمود الأخير
                if actual_headers[-1] == "حالة المبلغ":
                    print("✅ عمود 'حالة المبلغ' في الموضع الصحيح (العمود الأخير)")
                else:
                    print(f"⚠️ عمود 'حالة المبلغ' ليس في الموضع الأخير: {actual_headers[-1]}")

                # التحقق من أن ملاحظات قبل حالة المبلغ
                if len(actual_headers) >= 2 and actual_headers[-2] == "ملاحظات":
                    print("✅ عمود 'ملاحظات' في الموضع الصحيح (قبل حالة المبلغ)")
                else:
                    print(f"⚠️ عمود 'ملاحظات' ليس في الموضع الصحيح: {actual_headers[-2] if len(actual_headers) >= 2 else 'غير موجود'}")

            else:
                print(f"❌ ترتيب أعمدة جدول المراحل غير صحيح")
                print(f"   المتوقع: {expected_headers}")
                print(f"   الموجود: {actual_headers}")
                return False
        else:
            print("❌ جدول المراحل غير موجود")
            return False

        return True

    except Exception as e:
        print(f"❌ فشل في اختبار ترتيب أعمدة جدول المراحل: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار الوظائف المطورة...")
    print("=" * 50)

    # اختبار الاتصال بقاعدة البيانات
    db_test = test_database_connection()
    print()

    # إنشاء تطبيق واحد للاختبارات
    app = QApplication(sys.argv)

    # اختبار إنشاء الحوارات
    dialog_test = test_dialog_creation(app)
    print()

    # اختبار نافذة المشروع
    window_test = test_project_window(app)
    print()

    # اختبار وظيفة التحديث التلقائي
    auto_refresh_test = test_auto_refresh_functionality(app)
    print()

    # اختبار وظيفة التعبئة التلقائية للنسبة
    auto_percentage_test = test_auto_percentage_loading(app)
    print()

    # اختبار ترتيب أعمدة جدول المراحل
    column_order_test = test_phases_table_column_order(app)
    print()

    # النتيجة النهائية
    print("=" * 50)
    if db_test and dialog_test and window_test and auto_refresh_test and auto_percentage_test and column_order_test:
        print("🎉 جميع الاختبارات نجحت! الوظائف جاهزة للاستخدام.")
        return True
    else:
        print("💥 فشل في بعض الاختبارات. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    success = main()
    
    # إنهاء البرنامج بالكود المناسب
    sys.exit(0 if success else 1)
