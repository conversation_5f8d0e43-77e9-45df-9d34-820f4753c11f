#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لنافذة مراحل المشروع مع قسم المقاولات
"""

import sys
import os
from PySide6.QtWidgets import QApplication

# إضافة المسار الحالي لـ Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_contracting_project_phases():
    """اختبار نافذة مراحل المشروع للمقاولات"""
    try:
        app = QApplication(sys.argv)
        
        from مراحل_المشروع import ProjectPhasesWindow
        
        print("🏗️ اختبار نافذة مراحل المشروع للمقاولات...")
        
        # بيانات مشروع مقاولات تجريبية
        contracting_project_data = {
            'id': 1,
            'اسم_المشروع': 'مشروع مقاولات تجريبي',
            'معرف_العميل': 1,
            'معرف_المهندس': 1,
            'المبلغ': 100000.0,
            'المدفوع': 50000.0,
            'الباقي': 50000.0,
            'تاريخ_الإستلام': '2024-01-01',
            'تاريخ_التسليم': '2024-12-31',
            'الحالة': 'قيد الإنجاز',
            'وصف_المشروع': 'مشروع مقاولات شامل للاختبار',
            'ملاحظات': 'ملاحظات تجريبية للمقاولات'
        }
        
        # إنشاء نافذة مراحل المشروع للمقاولات
        print("📋 إنشاء نافذة مراحل المشروع...")
        window = ProjectPhasesWindow(None, contracting_project_data, "مقاولات")
        
        print("✅ تم إنشاء نافذة مراحل المشروع للمقاولات بنجاح")
        
        # التحقق من نوع المشروع
        if window.project_type == "مقاولات":
            print("✅ نوع المشروع محدد بشكل صحيح: مقاولات")
        else:
            print(f"❌ نوع المشروع غير صحيح: {window.project_type}")
            return False
        
        # التحقق من وجود التابات الخاصة بالمقاولات
        tab_count = window.tab_widget.count()
        print(f"📊 عدد التابات: {tab_count}")
        
        # قائمة التابات المتوقعة للمقاولات
        expected_tabs = [
            "معلومات المشروع",
            "مراحل المشروع", 
            "مهام المهندسين",
            "الجدول الزمني",
            "تقارير شاملة",
            "الملفات والمرفقات",
            "المصروفات",
            "العهد المالية",
            "دفعات العهد",
            "المقاولين",
            "العمال",
            "الموردين"
        ]
        
        # التحقق من وجود التابات
        found_tabs = []
        for i in range(tab_count):
            tab_text = window.tab_widget.tabText(i)
            found_tabs.append(tab_text)
            print(f"  📑 تاب {i+1}: {tab_text}")
        
        # التحقق من التابات المطلوبة
        missing_tabs = []
        for expected_tab in expected_tabs:
            if expected_tab not in found_tabs:
                missing_tabs.append(expected_tab)
        
        if missing_tabs:
            print(f"❌ تابات مفقودة: {missing_tabs}")
            return False
        else:
            print("✅ جميع التابات المطلوبة موجودة")
        
        print("\n🎉 اختبار نافذة مراحل المشروع للمقاولات نجح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة مراحل المشروع: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_design_vs_contracting():
    """مقارنة بين نوافذ التصميم والمقاولات"""
    try:
        from مراحل_المشروع import ProjectPhasesWindow
        
        print("\n🔄 مقارنة بين نوافذ التصميم والمقاولات...")
        
        # بيانات مشروع أساسية
        base_project_data = {
            'id': 1,
            'اسم_المشروع': 'مشروع اختبار',
            'معرف_العميل': 1,
            'معرف_المهندس': 1,
            'المبلغ': 50000.0,
            'المدفوع': 25000.0,
            'الباقي': 25000.0,
            'تاريخ_الإستلام': '2024-01-01',
            'تاريخ_التسليم': '2024-12-31',
            'الحالة': 'قيد الإنجاز'
        }
        
        # إنشاء نافذة التصميم
        print("📐 إنشاء نافذة التصميم...")
        design_window = ProjectPhasesWindow(None, base_project_data, "تصميم")
        design_tab_count = design_window.tab_widget.count()
        
        # إنشاء نافذة المقاولات
        print("🏗️ إنشاء نافذة المقاولات...")
        contracting_window = ProjectPhasesWindow(None, base_project_data, "مقاولات")
        contracting_tab_count = contracting_window.tab_widget.count()
        
        print(f"📊 عدد تابات التصميم: {design_tab_count}")
        print(f"📊 عدد تابات المقاولات: {contracting_tab_count}")
        
        # المقاولات يجب أن تحتوي على تابات أكثر
        if contracting_tab_count > design_tab_count:
            print("✅ نافذة المقاولات تحتوي على تابات إضافية كما هو متوقع")
        else:
            print("❌ نافذة المقاولات لا تحتوي على تابات إضافية")
            return False
        
        # التحقق من التابات الإضافية في المقاولات
        contracting_specific_tabs = [
            "المصروفات", "العهد المالية", "دفعات العهد", 
            "المقاولين", "العمال", "الموردين"
        ]
        
        contracting_tabs = []
        for i in range(contracting_tab_count):
            tab_text = contracting_window.tab_widget.tabText(i)
            contracting_tabs.append(tab_text)
        
        missing_contracting_tabs = []
        for tab in contracting_specific_tabs:
            if tab not in contracting_tabs:
                missing_contracting_tabs.append(tab)
        
        if missing_contracting_tabs:
            print(f"❌ تابات مقاولات مفقودة: {missing_contracting_tabs}")
            return False
        else:
            print("✅ جميع التابات الخاصة بالمقاولات موجودة")
        
        print("\n🎉 مقارنة التصميم والمقاولات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مقارنة النوافذ: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 بدء الاختبار الشامل لنافذة مراحل المشروع للمقاولات...")
    
    # اختبار نافذة المقاولات
    contracting_success = test_contracting_project_phases()
    
    # مقارنة التصميم والمقاولات
    comparison_success = test_design_vs_contracting()
    
    if contracting_success and comparison_success:
        print("\n🎊 جميع الاختبارات نجحت!")
        print("✅ نافذة مراحل المشروع للمقاولات تعمل بشكل مثالي")
        print("✅ قوائم المراحل مختلفة حسب نوع المشروع")
        print("✅ التابات الإضافية للمقاولات موجودة")
        print("✅ النظام جاهز للاستخدام الكامل")
    else:
        print("\n💥 فشل في بعض الاختبارات")
        sys.exit(1)
