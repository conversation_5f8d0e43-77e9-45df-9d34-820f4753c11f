from ast import If
from for_all import *
from db import *
from الطباعة import*

#سجل الديون
def create_debts_window(self):
    # Create main window
    window = QDialog(self)
    window.setWindowTitle("سجل الديون")
    window.resize(1200, 700)
    window.setLayoutDirection(Qt.RightToLeft)
    # Main layout
    main_layout = QVBoxLayout(window)
    debts_database(self)
    # Database connection function
    def con_db():
        db_name = "project_manager2_debts"
        conn = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=db_name
        )
        cursor = conn.cursor()
        return conn, cursor
    
    # Totals display
    felter = QHBoxLayout()
    # أزرار الفلترة
    all_button = QPushButton(qta.icon('fa5s.list', color='blue'), "جميع الحسابات ")
    daenon_transaction = QPushButton(qta.icon('fa5s.arrow-up', color='green'), "الحسابات الدائنة ")
    mdenen_button = QPushButton(qta.icon('fa5s.arrow-down', color='red'), "الحسابات المدينة ")

    # أزرار العمليات
    add_button = QPushButton(qta.icon('fa5s.plus', color='darkgreen'), "إضافة حساب جديد ")
    show_debt = QPushButton(qta.icon('fa5s.money-bill', color='orange'), "سجل الديون ")
    show_payments_button = QPushButton(qta.icon('fa5s.credit-card', color='green'), "سجل السداد ")

    felter.addWidget(add_button)
    felter.addWidget(show_debt)
    felter.addWidget(show_payments_button)

    felter.addWidget(all_button)
    felter.addWidget(daenon_transaction)
    felter.addWidget(mdenen_button)
    main_layout.addLayout(felter)

    # Totals display
    totals_container = QHBoxLayout()
    total_debt_label = QLabel("إجمالي الدائن: 0")
    total_debt_label.setMinimumWidth(150)
    total_debt_label.setStyleSheet("background-color: #7eb995; padding: 5px;")
    total_debt_label.setAlignment(Qt.AlignCenter)

    total_paid_label = QLabel("إجمالي المدفوع دائن: 0")
    total_paid_label.setStyleSheet("background-color: #7eb995; padding: 5px;")
    total_paid_label.setMinimumWidth(150)
    total_paid_label.setAlignment(Qt.AlignCenter)

    total_remaining_label = QLabel("إجمالي الباقي دائن: 0")
    total_remaining_label.setStyleSheet("background-color: #7eb995; padding: 5px;")
    total_remaining_label.setMinimumWidth(150)
    total_remaining_label.setAlignment(Qt.AlignCenter)

    total_debt_label1 = QLabel("إجمالي المدين: 0")
    total_debt_label1.setStyleSheet("background-color: #be6765; padding: 5px;")
    total_debt_label1.setMinimumWidth(150)
    total_debt_label1.setAlignment(Qt.AlignCenter)

    total_paid_label1 = QLabel("إجمالي المدفوع مدين: 0")
    total_paid_label1.setStyleSheet("background-color: #be6765; padding: 5px;")
    total_paid_label1.setMinimumWidth(150)
    total_paid_label1.setAlignment(Qt.AlignCenter)

    total_remaining_label1 = QLabel("إجمالي الباقي مدين: 0")
    total_remaining_label1.setStyleSheet("background-color: #be6765; padding: 5px;")
    total_remaining_label1.setMinimumWidth(150)
    total_remaining_label1.setAlignment(Qt.AlignCenter)
    
    totals_container.addWidget(total_debt_label)
    totals_container.addWidget(total_paid_label)
    totals_container.addWidget(total_remaining_label)
    totals_container.addWidget(total_debt_label1)
    totals_container.addWidget(total_paid_label1)
    totals_container.addWidget(total_remaining_label1)
    main_layout.addLayout(totals_container)

    # Search container
    search_container = QHBoxLayout()
    search_input = QLineEdit()
    search_input.setPlaceholderText("ابحث عن حساب...")
    search_input.setAlignment(Qt.AlignCenter)
    search_container.addWidget(search_input)
    main_layout.addLayout(search_container)

    # Main table
    table = QTableWidget()
    headers=["الرقم", "          الاسم          ", " رقم الهاتف ", "نوع الحساب", "            الوصف            ", 
                                     "  المبلغ  ", " المدفوع ", " الباقي ","تاريخ الحساب", "          ملاحظات         "]
    
    add_table_column(table,headers)
    #table.setMinimumWidth(1150)
    main_layout.addWidget(table)
    table_setting(table)

    # Bottom buttons
    bottom_button_container = QHBoxLayout()
    main_layout.addLayout(bottom_button_container)

    add_transaction = QPushButton(qta.icon('fa5s.plus', color='orange'), "إضافة دين جديد ")
    pay_button = QPushButton(qta.icon('fa5s.money-bill', color='green'), " تم السداد ")

    # أزرار التعديل والطباعة
    delete_button = QPushButton(qta.icon('fa5s.trash', color='crimson'), "حذف ")
    edit_button = QPushButton(qta.icon('fa5s.edit', color='gray'), "تعديل ")
    print_button = QPushButton(qta.icon('fa5s.print', color='navy'), "طباعة ")
    
    bottom_button_container.addWidget(add_transaction)
    bottom_button_container.addWidget(pay_button)
    bottom_button_container.addWidget(delete_button)
    bottom_button_container.addWidget(edit_button)
    bottom_button_container.addWidget(print_button)

    def colorize(table,green,red,yellow):
        col_idx = 5 
        col_idx1 = 6
        col_idx2 = 7
        col_idx3 = 9
        for row_idx in range(table.rowCount()):
            item5 = table.item(row_idx, col_idx)
            item6 = table.item(row_idx, col_idx1)
            item7 = table.item(row_idx, col_idx2)
            item9 = table.item(row_idx, col_idx3)
            if item5:
                col_data = item5.text()
                item5.setBackground(QColor(green))
                if col_data =="0":
                    item5.setText("لايوجد ديون")  # تحويل "مدين" إلى "0"    
            if item6:
                col_data5 = item5.text()
                col_data6 = item6.text()
                if col_data5 == col_data6 and col_data5 != "0":
                    item6.setText("خالص")  # تحويل "مدين" إلى "0"  
                #if "خالص" in col_data6:
                    item6.setBackground(QColor(green))
                else:
                    item6.setBackground(QColor(yellow))
            if item7:
                col_data5 = item6.text()
                col_data7 = item7.text()
                
                if col_data7 == "0" and col_data5 != "0":
                    item7.setText("خالص")  # تحويل "مدين" إلى "0"  
                #if "خالص" in col_data7:
                    item7.setBackground(QColor(green))
                else:
                    item7.setBackground(QColor(red))

            # if item9:
            #     col_data = item9.text()
            #     if "غير محدد" in col_data:
            #         item9.setBackground(QColor(green))
            #     else:
            #         try:
            #             # تحويل النص إلى تاريخ
            #             item_date = QDate.fromString(col_data, "yyyy-MM-dd")
            #             current_date = QDate.currentDate()

            #             # التحقق إذا كان التاريخ صالحًا وأقدم من اليوم
            #             if item_date.isValid() and item_date < current_date:
            #                 item9.setBackground(QColor(red))
            #             else:
            #                 item9.setBackground(QColor(yellow))
            #         except Exception as e:
            #             print(f"خطأ في معالجة التاريخ: {e}")
        
        # البحث عن العمود الذي يحمل اسم "نوع الحساب"
        col_idx = -1
        for col in range(table.columnCount()):
            header_item = table.horizontalHeaderItem(col)
            if header_item and header_item.text() == "نوع الحساب":
                col_idx = col
                break
        
        # إذا تم العثور على العمود، يتم تلوينه
        if col_idx != -1:
            for row_idx in range(table.rowCount()):
                item = table.item(row_idx, col_idx)
                if item:
                    col_data = item.text().strip()
                    if col_data == "دائن":
                        item.setBackground(QColor("#6e989c"))
                    elif col_data == "مدين":
                        item.setBackground(QColor("#e4c19b"))

                                   
    def colorize1(table,color):
        col_idx = 5 
        for row_idx in range(table.rowCount()):
            item5 = table.item(row_idx, col_idx)
            if item5:
                col_data = item5.text()
                item5.setBackground(QColor(color))
        
        # البحث عن العمود الذي يحمل اسم "نوع الحساب"
        col_idx = -1
        for col in range(table.columnCount()):
            header_item = table.horizontalHeaderItem(col)
            if header_item and header_item.text() == "نوع الحساب":
                col_idx = col
                break        
        # إذا تم العثور على العمود، يتم تلوينه
        if col_idx != -1:
            for row_idx in range(table.rowCount()):
                item = table.item(row_idx, col_idx)
                if item:
                    col_data = item.text().strip()
                    if col_data == "دائن":
                        item.setBackground(QColor("#6e989c"))
                    elif col_data == "مدين":
                        item.setBackground(QColor("#e4c19b"))
        
        # البحث عن العمود الذي يحمل اسم "نوع الحساب"
        col_idx1 = -1
        for col in range(table.columnCount()):
            header_item = table.horizontalHeaderItem(col)
            if header_item and header_item.text() == "تاريخ السداد":
                col_idx1 = col
                break
        

        # إذا تم العثور على العمود، يتم تلوينه
        if col_idx1 != -1:
            for row_idx in range(table.rowCount()):
                item = table.item(row_idx, col_idx1)
                if item:
                    col_data = item.text().strip()
                    if col_data == "تم السداد":
                        item.setBackground(QColor("#7eb995"))
                    elif col_data == "غير محدد":
                        item.setBackground(QColor("#cdd7b9"))
                    else:
                        try:
                            # تحويل النص إلى تاريخ
                            item_date = QDate.fromString(col_data, "yyyy-MM-dd")
                            current_date = QDate.currentDate()
                            # التحقق إذا كان التاريخ صالحًا وأقدم من اليوم
                            if item_date.isValid() and item_date < current_date:
                                item.setBackground(QColor("#be6765"))
                            else:
                                item.setBackground(QColor("#e4c19b"))
                        except Exception as e:
                            print(f"خطأ في معالجة التاريخ: {e}")
                    
    #حميل الجدول الرئيسي                                                            
    def update_table(search_text="", filter_type="all"):
        conn, cursor = con_db()
        query = "SELECT * FROM حسابات_الديون WHERE (اسم_الحساب LIKE %s OR رقم_الهاتف LIKE %s)"
        params = (f"%{search_text}%", f"%{search_text}%")
        
        if filter_type == "daenon":  # حسابات الدائنة
            query += " AND نوع_الحساب = 'دائن'"
        elif filter_type == "mdenen":  # حسابات المدينة
            query += " AND نوع_الحساب = 'مدين'"
            
        cursor.execute(query, params)
        rows = cursor.fetchall()
        table.setRowCount(len(rows))
        
        total_debt_daenon = 0
        total_paid_daenon = 0
        total_remaining_daenon = 0
        total_debt_mdenen = 0
        total_paid_mdenen = 0
        total_remaining_mdenen = 0
        
        for row_idx, row_data in enumerate(rows):
            for col_idx, value in enumerate(row_data):
                if isinstance(value, float):
                    formatted_value = f"{value:,.2f}"  # يعرض الرقم بفاصلة عشرية (2) وفاصلة آلاف
                else:
                    formatted_value = str(value)

                item = QTableWidgetItem(formatted_value)
                item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row_idx, col_idx, item)
            
            # Calculate totals based on account type
            if row_data[3] == 'دائن':  # نوع_الحساب is in column 3
                total_debt_daenon += float(row_data[5] or 0)
                total_paid_daenon += float(row_data[6] or 0)
                total_remaining_daenon += float(row_data[7] or 0)
            elif row_data[3] == 'مدين':
                total_debt_mdenen += float(row_data[5] or 0)
                total_paid_mdenen += float(row_data[6] or 0)
                total_remaining_mdenen += float(row_data[7] or 0)
        

        # Update labels
        total_debt_label.setText(f"إجمالي الدائن: {total_debt_daenon}")
        total_debt_label1.setText(f"إجمالي المدين: {total_debt_mdenen}")
        total_paid_label.setText(f"المدفوع (دائن): {total_paid_daenon}")
        total_paid_label1.setText(f"المدفوع (مدين): {total_paid_mdenen}")
        total_remaining_label.setText(f"الباقي (دائن): {total_remaining_daenon}")
        total_remaining_label1.setText(f"الباقي (مدين): {total_remaining_mdenen}")

        # total_debt_label.setText(f"إجمالي الدائن: {total_debt_daenon:,.2f}")
        # total_debt_label1.setText(f"إجمالي المدين: {total_debt_mdenen:,.2f}")
        # total_paid_label.setText(f"المدفوع (دائن): {total_paid_daenon:,.2f}")
        # total_paid_label1.setText(f"المدفوع (مدين): {total_paid_mdenen:,.2f}")
        # total_remaining_label.setText(f"الباقي (دائن): {total_remaining_daenon:,.2f}")
        # total_remaining_label1.setText(f"الباقي (مدين): {total_remaining_mdenen:,.2f}")


        colorize(table,"#cdd7b9","#dc8484","#eee0bd")
        conn.close()

    # Filter button functions
    def show_all_accounts():
        update_table(search_input.text(), "all")
        all_button.setEnabled(False)
        daenon_transaction.setEnabled(True)
        mdenen_button.setEnabled(True)

        total_debt_label.setHidden(False)
        total_paid_label.setHidden(False)
        total_remaining_label.setHidden(False)

        total_debt_label1.setHidden(False)
        total_paid_label1.setHidden(False)
        total_remaining_label1.setHidden(False)

    def show_daenon_accounts():
        update_table(search_input.text(), "daenon")
        all_button.setEnabled(True)
        daenon_transaction.setEnabled(False)
        mdenen_button.setEnabled(True)

        total_debt_label.setHidden(False)
        total_paid_label.setHidden(False)
        total_remaining_label.setHidden(False)

        total_debt_label1.setHidden(True)
        total_paid_label1.setHidden(True)
        total_remaining_label1.setHidden(True)

    def show_mdenen_accounts():
        update_table(search_input.text(), "mdenen")
        all_button.setEnabled(True)
        daenon_transaction.setEnabled(True)
        mdenen_button.setEnabled(False)

        total_debt_label.setHidden(True)
        total_paid_label.setHidden(True)
        total_remaining_label.setHidden(True)

        total_debt_label1.setHidden(False)
        total_paid_label1.setHidden(False)
        total_remaining_label1.setHidden(False)
    
    # Connect buttons to functions
    all_button.clicked.connect(show_all_accounts)
    daenon_transaction.clicked.connect(show_daenon_accounts)
    mdenen_button.clicked.connect(show_mdenen_accounts)

    def fill_fields():
        selected_row = table.currentRow()
        if selected_row != -1:
            return tuple(table.item(selected_row, i).text() for i in range(1, 10))
        return None

    def search():
        search_text = search_input.text()
        update_table(search_text)

    search_input.textChanged.connect(search)

    #طباعة الجدول الرئيسي
    def print_debts_report():
        # تحديث العنوان بناءً على الزر المعطل
        disabled_button = None
        if not all_button.isEnabled():
            disabled_button = all_button
        elif not daenon_transaction.isEnabled():
            disabled_button = daenon_transaction
        elif not mdenen_button.isEnabled():
            disabled_button = mdenen_button

        title = f"حسابات الديون ({disabled_button.text() if disabled_button else 'لا يوجد تصفية'})"

        if disabled_button.text() == "الحسابات الدائنة ":
            totel = f"<p>( {total_debt_label.text().replace('إجمالي الدائن', 'الإجمالي').strip()} {Currency_type} - إجمالي المدفوع: {total_paid_label.text().replace('المدفوع (دائن):', '').strip()} {Currency_type} - إجمالي الباقي: {total_remaining_label.text().replace('الباقي (دائن):', '').strip()} {Currency_type} )</p>"

        elif disabled_button.text() == "الحسابات المدينة ":
            totel = f"<p>( {total_debt_label1.text().replace('إجمالي المدين', 'الإجمالي').strip()} {Currency_type} - إجمالي المدفوع: {total_paid_label1.text().replace('المدفوع (مدين):', '').strip()} {Currency_type} - إجمالي الباقي: {total_remaining_label1.text().replace('الباقي (مدين):', '').strip()} {Currency_type} )</p>"

        else:
            totel = f"""
            <p>الدائن:  ( {total_debt_label.text().replace('إجمالي الدائن', 'الإجمالي').strip()} {Currency_type} - إجمالي المدفوع: {total_paid_label.text().replace('المدفوع (دائن):', '').strip()} {Currency_type} - إجمالي الباقي: {total_remaining_label.text().replace('الباقي (دائن):', '').strip()} {Currency_type} )</p>
            <p>المدين:  ( {total_debt_label1.text().replace('إجمالي المدين', 'الإجمالي').strip()} {Currency_type} - إجمالي المدفوع: {total_paid_label1.text().replace('المدفوع (مدين):', '').strip()} {Currency_type} - إجمالي الباقي: {total_remaining_label1.text().replace('الباقي (مدين):', '').strip()} {Currency_type} )</p>
            """
        self.print_debts_report(table,title,totel)

    #حذف حساب     
    def delete_person():
        selected_items = table.selectedItems()
        if not selected_items:
            QMessageBox.warning(window, "خطأ", "يرجى تحديد حساب واحد على الأقل")
            return

        selected_rows = set()
        for item in selected_items:
            selected_rows.add(item.row())

        ids_to_delete = []
        for row in selected_rows:
            person_id = table.item(row, 0).text()
            ids_to_delete.append(person_id)

        reply = QMessageBox.question(window, "تأكيد", 
            f"هل تريد حذف {len(ids_to_delete)} حساب وجميع ديونهم ودفعاتهم؟",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            conn, cursor = con_db()
            for person_id in ids_to_delete:
                cursor.execute("DELETE FROM حسابات_الديون WHERE id = %s", (person_id,))
                cursor.execute("DELETE FROM سجل_الديون WHERE معرف_الحساب = %s", (person_id,))
                cursor.execute("DELETE FROM دفعات_الديون WHERE معرف_الحساب = %s", (person_id,))
            conn.commit()
            conn.close()
            update_table()
            QMessageBox.information(window, "نجاح", f"تم حذف {len(ids_to_delete)} حساب(ات) بنجاح")

    #تعديل حساب
    def edit_person():
        selected_row = table.currentRow()
        selected_items = table.selectedItems()
        if not selected_items:
            QMessageBox.warning(window, "خطأ", "يرجى تحديد حساب أولاً")
            return
        
        person_id = table.item(selected_row, 0).text()
        old_data = fill_fields()
        if not old_data:
            return

        edit_dialog = QDialog(self)
        edit_dialog.setWindowTitle("تعديل حساب")
        edit_dialog.resize(600, 400)
        edit_dialog.setLayoutDirection(Qt.RightToLeft)
        edit_layout = QVBoxLayout(edit_dialog)

        fields = [
            ("الاسم:", old_data[0]),
            ("رقم الهاتف:", old_data[1]),
            ("الوصف:", old_data[3]),
            ("ملاحظات:", old_data[8]),
            ("نوع الحساب:", old_data[2])
        ]

        inputs = {}
        for label_text, value in fields:
            layout = QHBoxLayout()
            label = QLabel(label_text)
            label.setFixedWidth(150)
            if label_text == "نوع الحساب:":
                input_field = QComboBox()
                input_field.addItems(["دائن", "مدين"])
                input_field.setCurrentText(value)
            else:
                input_field = QLineEdit(value)
                input_field.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            layout.addWidget(input_field)
            edit_layout.addLayout(layout)
            inputs[label_text] = input_field

        date_layout = QHBoxLayout()
        date_label = QLabel("التاريخ:")
        date_label.setFixedWidth(150)
        date_input = QDateEdit()
        date_input.setDisplayFormat("yyyy-MM-dd")
        date_input.setDate(QDate.fromString(old_data[7], "yyyy-MM-dd"))
        date_input.setCalendarPopup(True)
        date_layout.addWidget(date_label)
        date_layout.addWidget(date_input)
        edit_layout.addLayout(date_layout)

       
        confirm_button = QPushButton(qta.icon('fa5s.check-circle', color='green'), "تأكيد التعديل ")
        edit_layout.addWidget(confirm_button)
        center_all_widgets(edit_dialog)

        def confirm_edit():
            name = inputs["الاسم:"].text()
            phone = inputs["رقم الهاتف:"].text()
            desc = inputs["الوصف:"].text()
            date = date_input.date().toString("yyyy-MM-dd")
            notes = inputs["ملاحظات:"].text()
            trans_type = inputs["نوع الحساب:"].currentText()

            if not phone:
                QMessageBox.warning(edit_dialog, "خطأ", "رقم الهاتف يجب أن يكونا أرقامًا")
                return
            if not phone.startswith("+") or not phone[1:].isdigit():
                QMessageBox.warning(edit_dialog, "خطأ", "رقم الهاتف غير صالح. تأكد من إدخال مفتاح الدولة.")
                return
            if name and phone and date :
                reply = QMessageBox.question(edit_dialog, "تأكيد", "هل تريد تعديل بيانات هذا الحساب؟", QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                
                if reply == QMessageBox.Yes:
                    conn, cursor = con_db()
                    cursor.execute("""UPDATE حسابات_الديون SET اسم_الحساب=%s, رقم_الهاتف=%s, الوصف=%s, 
                                   التاريخ=%s, الباقي=المبلغ-المدفوع, 
                                   ملاحظات=%s, نوع_الحساب=%s WHERE id=%s""",
                                  (name, phone, desc, date, notes, trans_type, person_id))
                    
                    # تحديث الاسم ونوع الحساب في سجل_الديون
                    cursor.execute("""
                        UPDATE سجل_الديون 
                        SET اسم_الحساب=%s, نوع_الحساب=%s
                        WHERE معرف_الحساب=%s
                    """, (name, trans_type, person_id))

                    # تحديث الاسم ونوع الحساب في دفعات_الديون
                    cursor.execute("""
                        UPDATE دفعات_الديون 
                        SET اسم_الحساب=%s, نوع_الحساب=%s
                        WHERE معرف_الحساب=%s
                    """, (name, trans_type, person_id))
                    
                    conn.commit()
                    update_table()
                    edit_dialog.close()
                    QMessageBox.information(edit_dialog, "نجاح", "تم تعديل الحساب بنجاح")

        confirm_button.clicked.connect(confirm_edit)
        edit_dialog.exec_()

#----------------دالة اضافة حساب جديد-----------------------------------------
    def add_person():
        add_dialog = QDialog(self)
        add_dialog.setWindowTitle("إضافة حساب جديد")
        add_dialog.resize(600, 400)
        add_dialog.setLayoutDirection(Qt.RightToLeft)
        add_layout = QVBoxLayout(add_dialog)

        fields = ["الاسم:", "رقم الهاتف:", "الوصف:", "ملاحظات:"]
        inputs = {}
        
        for field in fields:
            layout = QHBoxLayout()
            label = QLabel(field)
            label.setFixedWidth(150)
            input_field = QLineEdit()
            input_field.setAlignment(Qt.AlignCenter)
            # إضافة placeholder خاص برقم الهاتف فقط
            if field == "رقم الهاتف:":
                input_field.setPlaceholderText("يجب ان يحتوي على مفتاح الدولة (مثال: 21892xxxxxxx+)")

            layout.addWidget(label)
            layout.addWidget(input_field)
            add_layout.addLayout(layout)
            inputs[field] = input_field

        # حقل المبلغ
        amount_layout = QHBoxLayout()
        amount_label = QLabel("المبلغ:")
        amount_label.setFixedWidth(150)
        amount_input = QLineEdit()
        amount_input.setPlaceholderText("أدخل المبلغ (أرقام فقط)")
        amount_input.setAlignment(Qt.AlignCenter)
        #amount_layout.addWidget(amount_label)
        #amount_layout.addWidget(amount_input)
        add_layout.addLayout(amount_layout)

        # Transaction type
        trans_layout = QHBoxLayout()
        trans_label = QLabel("نوع الحساب:")
        trans_label.setFixedWidth(150)
        trans_input = QComboBox()
        trans_input.addItems(["دائن", "مدين"])
        trans_layout.addWidget(trans_label)
        trans_layout.addWidget(trans_input)
        add_layout.addLayout(trans_layout)

        date_layout = QHBoxLayout()
        date_label = QLabel("التاريخ:")
        date_label.setFixedWidth(150)
        date_input = QDateEdit()
        date_input.setDisplayFormat("yyyy-MM-dd")
        date_input.setDate(QDate.currentDate())
        date_input.setCalendarPopup(True)
        date_layout.addWidget(date_label)
        date_layout.addWidget(date_input)
        add_layout.addLayout(date_layout)

        invoice_layout = QHBoxLayout()
        invoice_label = QLabel("تاريخ السداد:")
        invoice_label.setFixedWidth(150)
        invoice_input = QDateEdit()
        invoice_input.setDisplayFormat("yyyy-MM-dd")
        invoice_input.setDate(QDate.currentDate())
        invoice_input.setCalendarPopup(True)
        #invoice_layout.addWidget(invoice_label)
        #invoice_layout.addWidget(invoice_input)
        add_layout.addLayout(invoice_layout)

        confirm_button = QPushButton(qta.icon('fa5s.plus', color='darkgreen'), "إضافة ")
        add_layout.addWidget(confirm_button)
        center_all_widgets(add_dialog)

        #تأكيد اضافة حساب
        def confirm_add():
            name = inputs["الاسم:"].text().strip()
            phone = inputs["رقم الهاتف:"].text().strip()
            desc = inputs["الوصف:"].text().strip()
            invoice = invoice_input.date().toString("yyyy-MM-dd")
            date = date_input.date().toString("yyyy-MM-dd")
            amount = amount_input.text().strip()
            notes = inputs["ملاحظات:"].text().strip()
            trans_type = trans_input.currentText()

            if not amount:
                amount = "0"

            if not phone or not phone.startswith("+") or not phone[1:].isdigit():
                QMessageBox.warning(add_dialog, "خطأ", "رقم الهاتف غير صالح. تأكد من إدخال مفتاح الدولة.")
                return

            if invoice == date:
                invoice = "غير محدد"

            if name and phone and date:
                conn, cursor = con_db()

                # التحقق من وجود الاسم
                cursor.execute("SELECT COUNT(*) FROM حسابات_الديون WHERE اسم_الحساب = %s", (name,))
                name_exists = cursor.fetchone()[0]

                # التحقق من وجود رقم الهاتف
                cursor.execute("SELECT COUNT(*) FROM حسابات_الديون WHERE رقم_الهاتف = %s", (phone,))
                phone_exists = cursor.fetchone()[0]

                if name_exists > 0:
                    QMessageBox.warning(add_dialog, "تنبيه", f"الاسم '{name}' موجود مسبقًا في قاعدة البيانات.")
                    return

                if phone_exists > 0:
                    QMessageBox.warning(add_dialog, "تنبيه", f"رقم الهاتف '{phone}' موجود مسبقًا في قاعدة البيانات.")
                    return

                reply = QMessageBox.question(add_dialog, "تأكيد", "هل تريد إضافة هذا الحساب؟",
                                            QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.Yes:
                    # إضافة إلى حسابات_الديون
                    cursor.execute("""INSERT INTO حسابات_الديون 
                        (اسم_الحساب, رقم_الهاتف, الوصف, التاريخ, المبلغ, المدفوع, الباقي, ملاحظات, نوع_الحساب) 
                        VALUES (%s, %s, %s, %s,  %s, 0, %s, %s, %s)""",
                        (name, phone, desc, date, amount, amount, notes, trans_type))
                    
                    account_id = cursor.lastrowid  # للحصول على id الحساب المضاف
                    
                    if amount != "0":
                        # إضافة إلى سجل_الديون
                        cursor.execute("""INSERT INTO سجل_الديون 
                            (معرف_الحساب, اسم_الحساب, نوع_الحساب, الوصف, المبلغ, تاريخ_الدين, تاريخ_السداد, ملاحظات) 
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)""",
                            (account_id, name, trans_type, desc, amount, date, invoice, notes))

                    conn.commit()
                    conn.close()

                    update_table()
                    add_dialog.close()
                    QMessageBox.information(add_dialog, "نجاح", "تم إضافة الحساب بنجاح")

        confirm_button.clicked.connect(confirm_add)
        add_dialog.exec_()

    # ---------------دالة إضافة دين جديد-----------------------------------------
    def add_new_transaction():
        selected_row = table.currentRow()
        selected_items = table.selectedItems()
        if not selected_items:
            QMessageBox.warning(window, "خطأ", "يرجى تحديد حساب من الجدول أولاً")
            return

        account_id = table.item(selected_row, 0).text()
        account_name = table.item(selected_row, 1).text()
        person_Taby = table.item(selected_row, 3).text()

        add_transaction_dialog = QDialog(window)
        add_transaction_dialog.setWindowTitle("إضافة دين جديد")
        add_transaction_dialog.resize(600, 300)
        add_transaction_dialog.setLayoutDirection(Qt.RightToLeft)
        transaction_layout = QVBoxLayout(add_transaction_dialog)

        # اسم الحساب
        account_name_layout = QHBoxLayout()
        account_name_label = QLabel("اسم الحساب:")
        account_name_label.setFixedWidth(150)
        account_name_input = QLabel(account_name)
        account_name_input.setAlignment(Qt.AlignCenter)
        account_name_layout.addWidget(account_name_label)
        account_name_layout.addWidget(account_name_input)
        transaction_layout.addLayout(account_name_layout)

        # id الحساب
        account_معرف_layout = QHBoxLayout()
        account_معرف_label = QLabel("id الحساب:")
        account_معرف_label.setFixedWidth(150)
        account_معرف_input = QLabel(account_id)
        account_معرف_input.setAlignment(Qt.AlignCenter)
        account_معرف_layout.addWidget(account_معرف_label)
        account_معرف_layout.addWidget(account_معرف_input)
        transaction_layout.addLayout(account_معرف_layout)

        # المبلغ
        amount_layout = QHBoxLayout()
        amount_label = QLabel("المبلغ:")
        amount_label.setFixedWidth(150)
        amount_input = QLineEdit()
        amount_input.setPlaceholderText("أدخل المبلغ (أرقام فقط)")
        amount_input.setAlignment(Qt.AlignCenter)
        amount_layout.addWidget(amount_label)
        amount_layout.addWidget(amount_input)
        transaction_layout.addLayout(amount_layout)

        # الوصف
        desc_layout = QHBoxLayout()
        desc_label = QLabel("الوصف:")
        desc_label.setFixedWidth(150)
        desc_input = QLineEdit()
        desc_input.setPlaceholderText("أدخل الوصف")
        desc_input.setAlignment(Qt.AlignCenter)
        desc_layout.addWidget(desc_label)
        desc_layout.addWidget(desc_input)
        transaction_layout.addLayout(desc_layout)

        # الملاحظات
        notes_layout = QHBoxLayout()
        notes_label = QLabel("ملاحظات:")
        notes_label.setFixedWidth(150)
        notes_input = QLineEdit()
        notes_input.setPlaceholderText("أدخل ملاحظات إن وجدت")
        notes_input.setAlignment(Qt.AlignCenter)
        notes_layout.addWidget(notes_label)
        notes_layout.addWidget(notes_input)
        transaction_layout.addLayout(notes_layout)

        # تاريخ الدين
        date_layout = QHBoxLayout()
        date_label = QLabel("تاريخ الدين:")
        date_label.setFixedWidth(150)
        date_input = QDateEdit()
        date_input.setDisplayFormat("yyyy-MM-dd")
        date_input.setDate(QDate.currentDate())
        date_input.setCalendarPopup(True)
        date_input.setAlignment(Qt.AlignCenter)
        date_layout.addWidget(date_label)
        date_layout.addWidget(date_input)
        transaction_layout.addLayout(date_layout)

        # تاريخ السداد
        invoice_layout = QHBoxLayout()
        invoice_label = QLabel("تاريخ السداد:")
        invoice_label.setFixedWidth(150)
        invoice_input = QDateEdit()
        invoice_input.setDisplayFormat("yyyy-MM-dd")
        invoice_input.setDate(QDate.currentDate())
        invoice_input.setCalendarPopup(True)
        invoice_input.setAlignment(Qt.AlignCenter)
        invoice_layout.addWidget(invoice_label)
        invoice_layout.addWidget(invoice_input)
        transaction_layout.addLayout(invoice_layout)

        # زر الإضافة
        confirm_transaction_button = QPushButton(qta.icon('fa5s.plus', color='darkgreen'), "إضافة دين ")
        transaction_layout.addWidget(confirm_transaction_button)
        center_all_widgets(add_transaction_dialog)

        # دالة تأكيد الإضافة دين
        def confirm_transaction():
            desc = desc_input.text()
            amount = amount_input.text()
            notes = notes_input.text()
            date = date_input.date().toString("yyyy-MM-dd")
            
            invoice_date = invoice_input.date().toString("yyyy-MM-dd")
            Remaining = amount_input.text()

            try:
                amount = float(amount)
            except ValueError:
                QMessageBox.warning(add_transaction_dialog, "خطأ", "المبلغ يجب أن يكون رقمًا صالحًا (مثال: 100 أو 100.5)")
                return

            reply = QMessageBox.question(add_transaction_dialog, "تأكيد", "هل تريد إضافة هذا الدين؟",
                                        QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply != QMessageBox.Yes:
                return

            conn, cursor = con_db()

            if invoice_date == date:
                invoice_date = "غير محدد"
            cursor.execute("""
                INSERT INTO سجل_الديون 
                (معرف_الحساب, اسم_الحساب, نوع_الحساب, الوصف, تاريخ_السداد, تاريخ_الدين, المبلغ,الباقي, ملاحظات) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (account_id, account_name, person_Taby, desc, invoice_date, date, amount,Remaining, notes))
            
            # تحديث المبلغ الإجمالي
            cursor.execute("SELECT المبلغ FROM حسابات_الديون WHERE id = %s", (account_id,))
            current_amount = cursor.fetchone()
            current_amount = current_amount[0] if current_amount else 0
            new_amount = current_amount + amount
            cursor.execute("UPDATE حسابات_الديون SET المبلغ = %s, الباقي = المبلغ - المدفوع WHERE id = %s", (new_amount, account_id))

            conn.commit()
            update_table()
            add_transaction_dialog.close()
            QMessageBox.information(add_transaction_dialog, "نجاح", "تمت إضافة المعاملة بنجاح")

        confirm_transaction_button.clicked.connect(confirm_transaction)
        add_transaction_dialog.exec_()

#----------------دالة تم السداد -----------------------------------------
    def make_payment():
        selected_row = table.currentRow()
        selected_items = table.selectedItems()
        if not selected_items:
            QMessageBox.warning(window, "خطأ", "يرجى تحديد حساب أولاً")
            return
        
        amount_Taby = table.item(selected_row, 5).text()  
        pide_Taby = table.item(selected_row, 6).text()
        bake_Taby = table.item(selected_row, 7).text()
        if pide_Taby==amount_Taby:
            reply = QMessageBox.warning(self,
                "تنبيه",
                "       لا يوجد مبلغ دين باقي والمعاملة خالصة، هل تريد الاستمرار؟",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.No:
                return
    

        person_id = table.item(selected_row, 0).text()
        person_name = table.item(selected_row, 1).text()  
        person_Taby = table.item(selected_row, 3).text()  

        
        # إنشاء عناصر الإدخال
        amount_dialog = QLineEdit(f"{bake_Taby}")
        amount_dialog.setPlaceholderText("المبلغ")
        amount_dialog.setAlignment(Qt.AlignCenter)
        
        date_dialog = QDateEdit()
        date_dialog.setDisplayFormat("yyyy-MM-dd")
        date_dialog.setDate(QDate.currentDate())
        date_dialog.setCalendarPopup(True)
        date_dialog.setAlignment(Qt.AlignCenter)
        
        desc_dialog = QLineEdit()
        desc_dialog.setPlaceholderText("الوصف")
        desc_dialog.setAlignment(Qt.AlignCenter)
        
        # إنشاء الحوار
        dialog = QDialog(self)
        dialog.resize(600, 200)
        dialog.setWindowTitle("السداد مبلغ")
        dialog.setLayoutDirection(Qt.RightToLeft)
        dialog_layout = QVBoxLayout(dialog)
        
        # إضافة كل عنصر مع وصفه في صف أفقي
        NAME_layout = QHBoxLayout()
        NAME_label = QLabel(f"سداد مبلغ لـ: {person_name}")
        NAME_label.setAlignment(Qt.AlignCenter)
        NAME_layout.addWidget(NAME_label)
        dialog_layout.addLayout(NAME_layout)
        # حقل المبلغ
        amount_layout = QHBoxLayout()
        amount_label = QLabel("المبلغ:")
        amount_label.setFixedWidth(150)
        amount_layout.addWidget(amount_label)
        amount_layout.addWidget(amount_dialog)
        dialog_layout.addLayout(amount_layout)
        
        # حقل الوصف
        desc_layout = QHBoxLayout()
        desc_label = QLabel("الوصف:")
        desc_label.setFixedWidth(150)
        desc_layout.addWidget(desc_label)
        desc_layout.addWidget(desc_dialog)
        dialog_layout.addLayout(desc_layout)

        # حقل الملاحظات
        invoice_layout = QHBoxLayout()
        invoice_label = QLabel("رقم الفاتورة:")
        invoice_label.setFixedWidth(150)
        invoice_input = QLineEdit()
        invoice_input.setPlaceholderText("أدخل رقم الفاتورة إن وجدت")
        invoice_input.setAlignment(Qt.AlignCenter)
        invoice_layout.addWidget(invoice_label)
        invoice_layout.addWidget(invoice_input)
        dialog_layout.addLayout(invoice_layout)

        # حقل الملاحظات
        notes_layout = QHBoxLayout()
        notes_label = QLabel("ملاحظات:")
        notes_label.setFixedWidth(150)
        notes_input = QLineEdit()
        notes_input.setPlaceholderText("أدخل ملاحظات إن وجدت")
        notes_input.setAlignment(Qt.AlignCenter)
        notes_layout.addWidget(notes_label)
        notes_layout.addWidget(notes_input)
        dialog_layout.addLayout(notes_layout)
        
        # حقل التاريخ
        date_layout = QHBoxLayout()
        date_label = QLabel("تاريخ السداد:")
        date_label.setFixedWidth(150)
        date_layout.addWidget(date_label)
        date_layout.addWidget(date_dialog)
        dialog_layout.addLayout(date_layout)
        
        # زر التأكيد
        confirm_button = (qta.icon('fa5s.check-circle', color='green'), "تأكيد السداد ")
        dialog_layout.addWidget(confirm_button)
        center_all_widgets(dialog)

        # دالة "تأكيد السداد"
        def confirm_payment():
            amount = amount_dialog.text()
            date = date_dialog.date().toString("yyyy-MM-dd")
            desc = desc_dialog.text()
            invoice = invoice_input.text()
            notes = notes_input.text()
            
            # التحقق من صحة المدخلات
            if not amount.isdigit():
                QMessageBox.warning(dialog, "خطأ", "المبلغ يجب أن يكون رقمًا")
                return
            
            
            if amount>bake_Taby:
                reply = QMessageBox.warning(self,
                    "تنبيه",
                    "        مبلغ السداد يتجاوز المبلغ الباقي ، هل تريد الاستمرار؟",
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply == QMessageBox.No:
                    return
            
            if amount and date:
                reply = QMessageBox.question(dialog, "تأكيد", "هل تريد تأكيد هذا السداد؟", 
                                            QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.Yes:
                    conn, cursor = con_db()
                    cursor.execute("INSERT INTO دفعات_الديون (معرف_الحساب, اسم_الحساب, نوع_الحساب, المبلغ_المدفوع, تاريخ_الدفع, وصف_المدفوع, ملاحظات, رقم_الفاتورة) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)",
                                    (person_id,person_name, person_Taby, amount, date, desc, notes, invoice))
                    cursor.execute(f"UPDATE حسابات_الديون SET المدفوع = المدفوع + %s, الباقي = الباقي - %s WHERE id = %s", 
                                    (amount, amount, person_id))
                    conn.commit()
                    update_table()
                    dialog.close()
                    QMessageBox.information(dialog, "نجاح", "تم إضافة المبلغ إلى الحساب بنجاح")
            else:
                QMessageBox.warning(dialog, "خطأ", "يرجى ملء الحقول الأساسية")
        
        confirm_button.clicked.connect(confirm_payment)
        dialog.exec_()
        
#----------------دالة عرض سجل الديون-----------------------------------------
    def show_debt_history():
        selected_row = table.currentRow()
        debt_window = QDialog(self)
        debt_window.resize(600, 600)
        debt_window.setLayoutDirection(Qt.RightToLeft)
        account_id = None
        account_name = "غير محدد"

        debt_layout = QVBoxLayout(debt_window)
        NAME_layout = QHBoxLayout()

        # تحديد العنوان بناءً على التحديد
        selected_items = table.selectedItems()
        if selected_items:
            account_id = table.item(selected_row, 0).text()
            account_name = table.item(selected_row, 1).text()
            debt_window.setWindowTitle(f"سجل الديون لـ: {account_name}")            
            NAME_label = QLabel(f"سجل الديون لـ: {account_name}")
            NAME_label.setAlignment(Qt.AlignCenter)

            # تحديث عنوان الطباعة ليشمل الاسم المحدد
            title = f"سجل الديون لـ: {account_name}"
        else:
            debt_window.setWindowTitle(f"سجل الديون لجميع الحسابات")
            NAME_label = QLabel(f"سجل الديون لجميع الحسابات")
            NAME_label.setAlignment(Qt.AlignCenter)
            # تحديث عنوان الطباعة لجميع الحسابات
            title = "سجل الديون لجميع الحسابات"

        NAME_layout.addWidget(NAME_label)
        debt_layout.addLayout(NAME_layout)
        # حقل البحث في الأعلى
        search_layout = QHBoxLayout()
        search_label = QLabel("البحث:")
        search_label.setAlignment(Qt.AlignCenter)
        search_label.setFixedWidth(100)
        search_input = QLineEdit()
        search_input.setPlaceholderText("ابحث باسم الحساب أو الوصف...")
        search_input.setAlignment(Qt.AlignCenter)
        search_layout.addWidget(search_label)
        search_layout.addWidget(search_input)
        debt_layout.addLayout(search_layout)

        # جدول سجل الديون
        debt_table = QTableWidget()
        headers = ["الرقم", التصنيف, "اسم الحساب", "نوع الحساب", "وصف الدين", "مبلغ الدين", " الباقي ", "تاريخ الدين", "تاريخ السداد", "ملاحظات"]
        add_table_column(debt_table,headers)
        
        debt_table.setMinimumWidth(1150)
        debt_layout.addWidget(debt_table)
        table_setting(debt_table)

        # تحديث جدول سجل الديون
        def update_debt_table(search_text=""):
            conn, cursor = con_db()
            selected_row = debt_table.currentRow()
            if selected_items:
                # افتراض أن selected_items هي قائمة من QTableWidgetItem
                item = selected_items[0]  # أول عنصر محدد
                item = account_id

                #account_id = item.text()  # استخراج النص من العنصر (id الحساب)
                # عرض سجل الديون للحساب المحدد فقط
                # استعلام لجلب سجل الديون للحساب المحدد فقط مع البحث
                cursor.execute("""
                    SELECT * FROM سجل_الديون 
                    WHERE معرف_الحساب = %s 
                    AND (اسم_الحساب LIKE %s OR الوصف LIKE %s)
                """, (account_id, f"%{search_text}%", f"%{search_text}%"))
            else:
                cursor.execute("""
                    SELECT سجل_الديون.id, سجل_الديون.معرف_الحساب, سجل_الديون.اسم_الحساب, حسابات_الديون.نوع_الحساب, 
                        سجل_الديون.الوصف, سجل_الديون.المبلغ, سجل_الديون.الباقي, سجل_الديون.تاريخ_الدين, سجل_الديون.تاريخ_السداد, سجل_الديون.ملاحظات
                    FROM سجل_الديون
                    JOIN حسابات_الديون ON سجل_الديون.معرف_الحساب = حسابات_الديون.id
                    WHERE سجل_الديون.اسم_الحساب LIKE %s OR سجل_الديون.الوصف LIKE %s
                """, (f"%{search_text}%", f"%{search_text}%")) 
            # جلب جميع الصفوف
            rows = cursor.fetchall()
            # حساب إجمالي مبلغ الدين
            total_amount = sum(int(row[5]) for row in rows if row[5] is not None)
            total_Remaining= sum(int(row[6]) for row in rows if row[6] is not None)
            # تحديث العنوان بعد الحساب
            if selected_items:
                NAME_label.setText(f"سجل الديون لـ: {account_name}\n( إجمالي الباقي للديون: {total_Remaining} {Currency_type} )")
            else:
                NAME_label.setText(f"سجل الديون لجميع الحسابات\n( إجمالي الباقي للديون: {total_Remaining} {Currency_type} )")
            # تحديد عدد الصفوف في الجدول
            debt_table.setRowCount(len(rows))
            # ملء الجدول بالبيانات
            for row_idx, row_data in enumerate(rows):
                for col_idx, value in enumerate(row_data):
                    if isinstance(value, float):
                        formatted_value = f"{value:,.2f}"  # يعرض الرقم بفاصلة عشرية (2) وفاصلة آلاف
                    else:
                        formatted_value = str(value)
                        
                    item = QTableWidgetItem(formatted_value)
                    item.setTextAlignment(Qt.AlignCenter)
                    debt_table.setItem(row_idx, col_idx, item)
            
            colorize1(debt_table,"#dc8484")
        # ربط حقل البحث بتحديث الجدول
        search_input.textChanged.connect(lambda: update_debt_table(search_input.text()))
        update_debt_table()  # تحديث الجدول عند الفتح

        # أزرار التحكم في الأسفل
        button_layout = QHBoxLayout()
        sdad_button = QPushButton(qta.icon('fa5s.money-bill', color='green'), "سداد ")
        delete_button = QPushButton(qta.icon('fa5s.trash', color='crimson'), "حذف ")
        edit_button = QPushButton(qta.icon('fa5s.edit', color='gray'), "تعديل ")
        print_button = QPushButton(qta.icon('fa5s.print', color='navy'), "طباعة ")
        button_layout.addWidget(sdad_button)
        button_layout.addWidget(delete_button)      
        button_layout.addWidget(edit_button)
        button_layout.addWidget(print_button)
        debt_layout.addLayout(button_layout)

        #سداد مبلغ سجل الديون
        def sdad_payment():
            selected_row = debt_table.currentRow()
            selected_items = debt_table.selectedItems()
            if not selected_items:
                QMessageBox.warning(debt_window, "خطأ", "يرجى تحديد حساب أولاً")
                return
            desc_Taby = debt_table.item(selected_row, 4).text() 
            amount_Taby = debt_table.item(selected_row, 5).text() 
            Remaining= debt_table.item(selected_row, 6).text()

            id = debt_table.item(selected_row, 0).text()
            person_id = debt_table.item(selected_row, 1).text()
            person_name = debt_table.item(selected_row, 2).text()  
            person_Taby = debt_table.item(selected_row, 3).text()  

            if Remaining == "0" or Remaining == "خالص":
                QMessageBox.warning(debt_window, "خطأ", "لا يوجد مبلغ دين باقي على المعاملة")
                return
            # إنشاء عناصر الإدخال
            amount_dialog = QLineEdit(f"{Remaining}")
            amount_dialog.setPlaceholderText("المبلغ")
            amount_dialog.setAlignment(Qt.AlignCenter)
            
            date_dialog = QDateEdit()
            date_dialog.setDisplayFormat("yyyy-MM-dd")
            date_dialog.setDate(QDate.currentDate())
            date_dialog.setCalendarPopup(True)
            date_dialog.setAlignment(Qt.AlignCenter)
            
            desc_dialog = QLineEdit(f"{desc_Taby}")
            desc_dialog.setPlaceholderText("الوصف")
            desc_dialog.setAlignment(Qt.AlignCenter)
            
            # إنشاء الحوار
            dialog = QDialog(self)
            dialog.resize(600, 200)
            dialog.setWindowTitle("السداد مبلغ")
            dialog.setLayoutDirection(Qt.RightToLeft)
            dialog_layout = QVBoxLayout(dialog)
            
            # إضافة كل عنصر مع وصفه في صف أفقي
            NAME_layout = QHBoxLayout()
            NAME_label = QLabel(f"سداد مبلغ لـ: {person_name}")
            NAME_label.setAlignment(Qt.AlignCenter)
            NAME_layout.addWidget(NAME_label)
            dialog_layout.addLayout(NAME_layout)
            # حقل المبلغ
            amount_layout = QHBoxLayout()
            amount_label = QLabel("المبلغ:")
            amount_label.setFixedWidth(150)
            amount_layout.addWidget(amount_label)
            amount_layout.addWidget(amount_dialog)
            dialog_layout.addLayout(amount_layout)
            
            # حقل الوصف
            desc_layout = QHBoxLayout()
            desc_label = QLabel("الوصف:")
            desc_label.setFixedWidth(150)
            desc_layout.addWidget(desc_label)
            desc_layout.addWidget(desc_dialog)
            dialog_layout.addLayout(desc_layout)

            # حقل الملاحظات
            invoice_layout = QHBoxLayout()
            invoice_label = QLabel("رقم الفاتورة:")
            invoice_label.setFixedWidth(150)
            invoice_input = QLineEdit()
            invoice_input.setPlaceholderText("أدخل رقم الفاتورة إن وجدت")
            invoice_input.setAlignment(Qt.AlignCenter)
            invoice_layout.addWidget(invoice_label)
            invoice_layout.addWidget(invoice_input)
            dialog_layout.addLayout(invoice_layout)

            # حقل الملاحظات
            notes_layout = QHBoxLayout()
            notes_label = QLabel("ملاحظات:")
            notes_label.setFixedWidth(150)
            notes_input = QLineEdit()
            notes_input.setPlaceholderText("أدخل ملاحظات إن وجدت")
            notes_input.setAlignment(Qt.AlignCenter)
            notes_layout.addWidget(notes_label)
            notes_layout.addWidget(notes_input)
            dialog_layout.addLayout(notes_layout)
            
            # حقل التاريخ
            date_layout = QHBoxLayout()
            date_label = QLabel("تاريخ السداد:")
            date_label.setFixedWidth(150)
            date_layout.addWidget(date_label)
            date_layout.addWidget(date_dialog)
            dialog_layout.addLayout(date_layout)
            
            # زر التأكيد
            confirm_button = QPushButton(qta.icon('fa5s.check-circle', color='green'), "تأكيد السداد ")
            dialog_layout.addWidget(confirm_button)
            center_all_widgets(dialog)

            #  سجل الديون دالة "تأكيد السداد"
            def confirm_payment():
                amount = amount_dialog.text()
                date = date_dialog.date().toString("yyyy-MM-dd")
                desc = desc_dialog.text()
                invoice = invoice_input.text()
                notes = notes_input.text()

                # التحقق من أن المبلغ رقم (يدعم الأعداد العشرية)
                try:
                    amount_float = float(amount)
                except ValueError:
                    QMessageBox.warning(dialog, "خطأ", "المبلغ يجب أن يكون رقمًا صالحًا)")
                    return

                baky = float(Remaining) - amount_float
                if amount_float > float(Remaining):
                    QMessageBox.warning(dialog, "خطأ", "المبلغ المدفوع أكبر من المبلغ المتبقي")
                    return

                if amount and date:
                    reply = QMessageBox.question(dialog, "تأكيد", "هل تريد تأكيد هذا السداد؟", 
                                                QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                    if reply == QMessageBox.Yes:
                        conn, cursor = con_db()
                        cursor.execute(
                            "INSERT INTO دفعات_الديون (معرف_الحساب, اسم_الحساب, نوع_الحساب, المبلغ_المدفوع, تاريخ_الدفع, وصف_المدفوع, ملاحظات, رقم_الفاتورة) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)",
                            (person_id, person_name, person_Taby, amount_float, date, desc, notes, invoice)
                        )
                        cursor.execute(
                            "UPDATE حسابات_الديون SET المدفوع = المدفوع + %s, الباقي = الباقي - %s WHERE id = %s", 
                            (amount_float, amount_float, person_id)
                        )

                        if amount_float == float(amount_Taby):
                            cursor.execute("UPDATE سجل_الديون SET تاريخ_السداد = %s WHERE id = %s", 
                                        ("تم السداد", id))
                            cursor.execute("UPDATE سجل_الديون SET الباقي = %s WHERE id = %s", 
                                        (baky, id))
                        else:
                            cursor.execute("UPDATE سجل_الديون SET الباقي = %s WHERE id = %s", 
                                        (baky, id))
                            
                            if baky == 0:
                                cursor.execute("UPDATE سجل_الديون SET تاريخ_السداد = %s WHERE id = %s", 
                                            ("تم السداد", id))

                        conn.commit()
                        update_debt_table()
                        update_table()
                        dialog.close()
                        QMessageBox.information(dialog, "نجاح", "تم إضافة المبلغ إلى الحساب بنجاح")
                else:
                    QMessageBox.warning(dialog, "خطأ", "يرجى ملء الحقول الأساسية")

            
            confirm_button.clicked.connect(confirm_payment)
            dialog.exec_()

        # دالة الحذف سجل الديون     
        def delete_debt():
            selected_items = debt_table.selectedItems()
            if not selected_items:
                QMessageBox.warning(debt_window, "خطأ", "يرجى تحديد سجل أو أكثر لحذفه")
                return

            selected_rows = set()
            for item in selected_items:
                selected_rows.add(item.row())

            debt_ids = []
            for row in selected_rows:
                item = debt_table.item(row, 0)
                if item:
                    debt_ids.append(item.text())

            if not debt_ids:
                QMessageBox.warning(debt_window, "خطأ", "تعذر العثور على idات السجلات المحددة")
                return

            reply = QMessageBox.question(
                debt_window,
                "تأكيد",
                f"هل تريد حذف {len(debt_ids)} سجل(ات)؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                conn, cursor = con_db()
                for debt_id in debt_ids:
                    # جلب id الحساب والمبلغ
                    cursor.execute("SELECT معرف_الحساب, المبلغ FROM سجل_الديون WHERE id = %s", (debt_id,))
                    result = cursor.fetchone()
                    if result:
                        account_id, amount = result

                        # تحديث المبلغ
                        cursor.execute("""
                            UPDATE حسابات_الديون 
                            SET المبلغ = المبلغ - %s
                            WHERE id = %s
                        """, (amount, account_id))

                        # تحديث الباقي
                        cursor.execute("""
                            UPDATE حسابات_الديون 
                            SET الباقي = المبلغ - المدفوع
                            WHERE id = %s
                        """, (account_id,))

                        # حذف السجل
                        cursor.execute("DELETE FROM سجل_الديون WHERE id = %s", (debt_id,))
                
                conn.commit()
                conn.close()

                update_debt_table()
                update_table(search_input.text(), "all")

                QMessageBox.information(debt_window, "نجاح", f"تم حذف {len(debt_ids)} سجل(ات) وتحديث الحساب(ات) بنجاح")


        # دالة التعديل سجل الديون
        def edit_debt():
            selected_debt_row = debt_table.currentRow()            
            selected_items = debt_table.selectedItems()
            if not selected_items:
                QMessageBox.warning(debt_window, "خطأ", "يرجى تحديد معاملة من الجدول أولاً")
                return


            debt_id = debt_table.item(selected_debt_row, 0).text()
            old_account_id = debt_table.item(selected_debt_row, 1).text()
            old_account_name = debt_table.item(selected_debt_row, 2).text()
            old_desc = debt_table.item(selected_debt_row, 4).text()
            old_amount = debt_table.item(selected_debt_row, 5).text()
            old_remaining = debt_table.item(selected_debt_row, 6).text()
            old_date = debt_table.item(selected_debt_row, 7).text()
            invoice_date = debt_table.item(selected_debt_row, 8).text()
            old_notes = debt_table.item(selected_debt_row, 9).text()

            edit_dialog = QDialog(self)
            edit_dialog.setWindowTitle("تعديل سجل الدين")
            edit_dialog.resize(600, 300)
            edit_dialog.setLayoutDirection(Qt.RightToLeft)
            main_layout = QVBoxLayout(edit_dialog)

            # اسم الحساب
            account_name_row = QHBoxLayout()
            account_name_label = QLabel("اسم الحساب:")
            account_name_label.setFixedWidth(150)
            account_name_input = QLabel(old_account_name)
            account_name_row.addWidget(account_name_label)
            account_name_row.addWidget(account_name_input)
            main_layout.addLayout(account_name_row)

            # id الحساب
            account_معرف_row = QHBoxLayout()
            account_معرف_label = QLabel("id الحساب:")
            account_معرف_label.setFixedWidth(150)
            account_معرف_input = QLabel(old_account_id)
            account_معرف_row.addWidget(account_معرف_label)
            account_معرف_row.addWidget(account_معرف_input)
            main_layout.addLayout(account_معرف_row)

            # المبلغ
            amount_row = QHBoxLayout()
            amount_label = QLabel("المبلغ:")
            amount_label.setFixedWidth(150)
            amount_input = QLineEdit(old_amount)
            amount_input.setPlaceholderText("أدخل المبلغ (أرقام فقط)")
            #amount_row.addWidget(amount_label)
            #amount_row.addWidget(amount_input)
            main_layout.addLayout(amount_row)

            # الوصف
            desc_row = QHBoxLayout()
            desc_label = QLabel("الوصف:")
            desc_label.setFixedWidth(150)
            desc_input = QLineEdit(old_desc)
            desc_input.setPlaceholderText("أدخل الوصف")
            desc_row.addWidget(desc_label)
            desc_row.addWidget(desc_input)
            main_layout.addLayout(desc_row)

            # الملاحظات
            notes_row = QHBoxLayout()
            notes_label = QLabel("ملاحظات:")
            notes_label.setFixedWidth(150)
            notes_input = QLineEdit(old_notes)
            notes_input.setPlaceholderText("أدخل ملاحظات إن وجدت")
            notes_row.addWidget(notes_label)
            notes_row.addWidget(notes_input)
            main_layout.addLayout(notes_row)

            # التاريخ
            date_row = QHBoxLayout()
            date_label = QLabel("التاريخ:")
            date_label.setFixedWidth(150)
            date_input = QDateEdit()
            date_input.setDisplayFormat("yyyy-MM-dd")
            date_input.setDate(QDate.fromString(old_date, "yyyy-MM-dd"))
            date_input.setCalendarPopup(True)
            date_input.setAlignment(Qt.AlignCenter)
            date_row.addWidget(date_label)
            date_row.addWidget(date_input)
            main_layout.addLayout(date_row)

            # "تاريخ السداد:"
            invoice_row = QHBoxLayout()
            invoice_label = QLabel("تاريخ السداد:")
            invoice_label.setFixedWidth(150)
            invoice_input = QDateEdit()
            invoice_input.setDisplayFormat("yyyy-MM-dd")
            if invoice_date == "غير محدد":
                invoice_input.setDate(QDate.fromString(old_date, "yyyy-MM-dd")) 
            elif invoice_date == "تم السداد":
                invoice_input.setDate(QDate.fromString(old_date, "yyyy-MM-dd")) 
            else:  
                invoice_input.setDate(QDate.fromString(invoice_date, "yyyy-MM-dd"))
            if old_remaining == "خالص" or old_remaining == "0" :
                invoice_label.setHidden(True)
                invoice_input.setHidden(True)

            invoice_input.setCalendarPopup(True)
            invoice_input.setAlignment(Qt.AlignCenter)
            invoice_row.addWidget(invoice_label)
            invoice_row.addWidget(invoice_input)
            main_layout.addLayout(invoice_row)

            confirm_edit_button = QPushButton(qta.icon('fa5s.check-circle', color='green'), "تأكيد التعديل ")
            main_layout.addWidget(confirm_edit_button)
            center_all_widgets(edit_dialog)

            # دالة تأكيد التعديل            
            def confirm_edit():
                desc = desc_input.text()
                date = date_input.date().toString("yyyy-MM-dd")
                invoice = invoice_input.date().toString("yyyy-MM-dd")
                amount = amount_input.text()
                notes = notes_input.text()

                if not amount.isdigit():
                    QMessageBox.warning(edit_dialog, "خطأ", "المبلغ يجب أن يكون رقمًا")
                    return

                if invoice == date:
                    invoice = "غير محدد"
                
                if old_remaining == "0" or old_remaining == "خالص":
                    invoice = "تم السداد"

                reply = QMessageBox.question(edit_dialog, "تأكيد", "هل تريد تعديل هذا السجل؟",
                                            QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.Yes:
                    conn, cursor = con_db()

                    # تحويل المبالغ لأرقام
                    try:
                        old_amount_value = float(old_amount)
                        new_amount_value = float(amount)
                    except ValueError:
                        QMessageBox.warning(edit_dialog, "خطأ", "تأكد من صحة المبلغ")
                        return

                    # جلب المبلغ والمدفوع الحالي من حسابات_الديون
                    cursor.execute("SELECT المبلغ, المدفوع FROM حسابات_الديون WHERE id=%s", (old_account_id,))
                    result = cursor.fetchone()

                    if result:
                        total_amount, paid = result
                        total_amount = float(total_amount or 0)
                        paid = float(paid or 0)

                        # تحديث المبلغ الجديد بطرح القديم ثم جمع الجديد
                        updated_total = total_amount - old_amount_value + new_amount_value
                        updated_remaining = updated_total - paid

                        # تحديث جدول حسابات_الديون
                        cursor.execute("""
                            UPDATE حسابات_الديون 
                            SET المبلغ=%s, الباقي=%s 
                            WHERE id=%s
                        """, (updated_total, updated_remaining, old_account_id))

                    else:
                        QMessageBox.warning(edit_dialog, "خطأ", "لم يتم العثور على الحساب المرتبط بهذا الدين")
                        return

                    # تعديل سجل الدين نفسه
                    cursor.execute("""
                        UPDATE سجل_الديون 
                        SET الوصف=%s, تاريخ_السداد=%s, تاريخ_الدين=%s, المبلغ=%s, ملاحظات=%s 
                        WHERE id=%s
                    """, (desc, invoice, date, new_amount_value, notes, debt_id))

                    # نحسب الباقي الجديد لهذا السجل
                    # new_remaining = new_amount_value - (old_amount_value - float(debt_table.item(selected_debt_row, 6).text()))

                    # cursor.execute("""
                    #     UPDATE سجل_الديون 
                    #     SET الوصف=%s, تاريخ_السداد=%s, تاريخ_الدين=%s, المبلغ=%s, ملاحظات=%s, الباقي=%s
                    #     WHERE id=%s
                    # """, (desc, invoice, date, new_amount_value, notes, new_remaining, debt_id))


                    conn.commit()
                    update_debt_table()
                    update_table()
                    edit_dialog.close()
                    QMessageBox.information(debt_window, "نجاح", "تم تعديل السجل وتحديث مبلغ الحساب بنجاح")
                    
            confirm_edit_button.clicked.connect(confirm_edit)
            edit_dialog.exec_()

        # دالة الطباعة (مثال بسيط)
        def print_debt():
            if selected_items:
                pattern = f"سجل الديون لـ: {account_name}\n"
            else:
                pattern = f"سجل الديون لجميع الحسابات\n"
            totel = f"<p>{NAME_label.text().replace(pattern, '').strip()}</p>"
            self.print_debts_report(debt_table,title,totel)
            # يمكنك هنا إضافة كود للطباعة باستخدام QPrinter أو مكتبة أخرى

        # ربط الأزرار بالدوال
        sdad_button.clicked.connect(sdad_payment)
        print_button.clicked.connect(print_debt)
        edit_button.clicked.connect(edit_debt)
        delete_button.clicked.connect(delete_debt)

        debt_window.exec_()

#----------------دالة عرض سجل السداد-----------------------------------------
    def show_payments():
        selected_row = table.currentRow()
        payment_window = QDialog(self)
        payment_window.resize(600, 600)
        payment_window.setLayoutDirection(Qt.RightToLeft)

        payment_layout = QVBoxLayout(payment_window)
        NAME_layout = QHBoxLayout()

        # تحديد العنوان بناءً على التحديد
        selected_items = table.selectedItems()
        if selected_items:
            account_id = table.item(selected_row, 0).text()
            person_name = table.item(selected_row, 1).text()
            payment_window.setWindowTitle(f"سجل السداد لـ: {person_name}")            
            NAME_label = QLabel(f"سجل السداد لـ: {person_name}")
            NAME_label.setAlignment(Qt.AlignCenter)
            # تحديث عنوان الطباعة ليشمل الاسم المحدد
            title = f"سجل السداد لـ: {person_name}"
        else:
            payment_window.setWindowTitle(f"سجل السداد لجميع الحسابات")
            NAME_label = QLabel(f"سجل السداد لجميع الحسابات")
            NAME_label.setAlignment(Qt.AlignCenter)

            # تحديث عنوان الطباعة لجميع الحسابات
            title = "سجل السداد لجميع الحسابات"
        
        
        payment_layout.addWidget(NAME_label)

        payment_search_container = QHBoxLayout()
        payment_search_input = QLineEdit()
        payment_search_input.setPlaceholderText("ابحث في السجل...")
        payment_search_input.setAlignment(Qt.AlignCenter)
        payment_search_container.addWidget(payment_search_input)
        payment_layout.addLayout(payment_search_container)

        payment_table = QTableWidget()
        headers= ["الرقم",التصنيف, "اسم الحساب", "نوع الحساب",  "وصف المدفوع","المبلغ المدفوع", "تاريخ الدفع", "رقم الفاتورة", "ملاحظات"]
        add_table_column(payment_table,headers)
        payment_table.setMinimumWidth(1100)
        payment_layout.addWidget(payment_table)
        table_setting(payment_table)

        def update_payment_table(search_text=""):
            conn, cursor = con_db()
            if selected_items:
                item = selected_items[0]  # أول عنصر محدد
                item = account_id
                #account_id = item.text()
                cursor.execute("""
                    SELECT * FROM دفعات_الديون 
                    WHERE معرف_الحساب = %s 
                    AND (اسم_الحساب LIKE %s OR وصف_المدفوع LIKE %s)
                """, (account_id, f"%{search_text}%", f"%{search_text}%"))
                print(account_id)
            
            else:
                cursor.execute("""
                    SELECT دفعات_الديون.id, دفعات_الديون.معرف_الحساب, دفعات_الديون.اسم_الحساب, حسابات_الديون.نوع_الحساب, 
                        دفعات_الديون.وصف_المدفوع, دفعات_الديون.المبلغ_المدفوع, دفعات_الديون.تاريخ_الدفع, دفعات_الديون.رقم_الفاتورة, دفعات_الديون.ملاحظات
                    FROM دفعات_الديون
                    JOIN حسابات_الديون ON دفعات_الديون.معرف_الحساب = حسابات_الديون.id
                    WHERE دفعات_الديون.اسم_الحساب LIKE %s OR دفعات_الديون.وصف_المدفوع LIKE %s
                """, (f"%{search_text}%", f"%{search_text}%"))

            rows = cursor.fetchall()
            total_amount = sum(float(row[5]) for row in rows if row[5] is not None)

            # تحديث العنوان بعد الحساب
            if selected_items:
                NAME_label.setText(f"سجل السداد لـ: {person_name}\n( إجمالي المدفوع: {total_amount} {Currency_type} )")
            else:
                NAME_label.setText(f"سجل السداد لجميع الحسابات\n( إجمالي المدفوع: {total_amount} {Currency_type} )")

            payment_table.setRowCount(len(rows))
            for row_idx, row_data in enumerate(rows):
                for col_idx, value in enumerate(row_data):
                    if isinstance(value, float):
                        formatted_value = f"{value:,.2f}"  # يعرض الرقم بفاصلة عشرية (2) وفاصلة آلاف
                    else:
                        formatted_value = str(value)
                        
                    item = QTableWidgetItem(formatted_value)
                    item.setTextAlignment(Qt.AlignCenter)
                    payment_table.setItem(row_idx, col_idx, item)
            
            colorize1(payment_table,"#cdd7b9")

        payment_search_input.textChanged.connect(lambda: update_payment_table(payment_search_input.text()))
        update_payment_table()
        payment_button_container = QHBoxLayout()
        delete_payment_button = QPushButton(qta.icon('fa5s.trash', color='crimson'), "حذف ")
        edit_payment_button = QPushButton(qta.icon('fa5s.edit', color='gray'), "تعديل ")
        print_payment_button = QPushButton(qta.icon('fa5s.print', color='navy'), "طباعة ")
        
        payment_button_container.addWidget(delete_payment_button)
        payment_button_container.addWidget(edit_payment_button)
        payment_button_container.addWidget(print_payment_button)
        payment_layout.addLayout(payment_button_container)


        # دالة الحذف نافذة السداد
        def delete_payment():
            selected_debt_row = payment_table.currentRow()
            if selected_debt_row == -1:
                QMessageBox.warning(payment_window, "خطأ", "يرجى تحديد سجل لحذفه")
                return

            debt_payment_id = payment_table.item(selected_debt_row, 0).text()
            account_id = payment_table.item(selected_debt_row, 1).text()

            reply = QMessageBox.question(payment_window, "تأكيد", "هل تريد حذف هذا السجل؟",
                                        QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                conn, cursor = con_db()

                # الحصول على قيمة المبلغ المدفوع من السجل قبل الحذف
                cursor.execute("SELECT المبلغ_المدفوع FROM دفعات_الديون WHERE id = %s", (debt_payment_id,))
                result = cursor.fetchone()
                if result:
                    paid_amount = result[0]

                    # تحديث جدول حسابات_الديون بطرح المبلغ المدفوع من المدفوع، وجمعه على الباقي
                    cursor.execute("""
                        UPDATE حسابات_الديون 
                        SET المدفوع = المدفوع - %s, الباقي = الباقي + %s 
                        WHERE id = %s
                    """, (paid_amount, paid_amount, account_id))

                    # حذف السجل من جدول دفعات_الديون
                    cursor.execute("DELETE FROM دفعات_الديون WHERE id = %s", (debt_payment_id,))
                    conn.commit()

                    update_payment_table()
                    update_table()
                    QMessageBox.information(payment_window, "نجاح", "تم حذف السجل بنجاح")
                else:
                    QMessageBox.warning(payment_window, "خطأ", "تعذر العثور على السجل المحدد.")
        
        def delete_payment():
            selected_items = payment_table.selectedItems()
            if not selected_items:
                QMessageBox.warning(payment_window, "خطأ", "يرجى تحديد سجل أو أكثر لحذفه")
                return

            selected_rows = set()
            for item in selected_items:
                selected_rows.add(item.row())

            payments_to_delete = []
            for row in selected_rows:
                payment_معرف_item = payment_table.item(row, 0)
                account_معرف_item = payment_table.item(row, 1)
                if payment_معرف_item and account_معرف_item:
                    payments_to_delete.append((payment_معرف_item.text(), account_معرف_item.text()))

            if not payments_to_delete:
                QMessageBox.warning(payment_window, "خطأ", "تعذر تحديد السجلات المطلوبة")
                return

            reply = QMessageBox.question(
                payment_window,
                "تأكيد",
                f"هل تريد حذف {len(payments_to_delete)} سجل(ات)؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                conn, cursor = con_db()
                for payment_id, account_id in payments_to_delete:
                    # جلب المبلغ المدفوع
                    cursor.execute("SELECT المبلغ_المدفوع FROM دفعات_الديون WHERE id = %s", (payment_id,))
                    result = cursor.fetchone()
                    if result:
                        paid_amount = result[0]

                        # تحديث المبالغ في جدول حسابات_الديون
                        cursor.execute("""
                            UPDATE حسابات_الديون 
                            SET المدفوع = المدفوع - %s, الباقي = الباقي + %s 
                            WHERE id = %s
                        """, (paid_amount, paid_amount, account_id))

                        # حذف السجل
                        cursor.execute("DELETE FROM دفعات_الديون WHERE id = %s", (payment_id,))
                
                conn.commit()
                conn.close()

                update_payment_table()
                update_table()
                QMessageBox.information(payment_window, "نجاح", f"تم حذف {len(payments_to_delete)} سجل(ات) بنجاح")


        # دالة التعديل نافذة السداد
        def edit_payment():
            selected_debt_row = payment_table.currentRow()
            if selected_debt_row == -1:
                QMessageBox.warning(payment_window, "خطأ", "يرجى تحديد سجل لتعديله")
                return

            debt_id = payment_table.item(selected_debt_row, 0).text()
            old_account_id = payment_table.item(selected_debt_row, 1).text()
            old_name = payment_table.item(selected_debt_row, 2).text()
            old_desc = payment_table.item(selected_debt_row, 4).text()
            old_amount = payment_table.item(selected_debt_row, 5).text()
            old_date = payment_table.item(selected_debt_row, 6).text()
            old_FATORA = payment_table.item(selected_debt_row, 7).text()
            old_notes = payment_table.item(selected_debt_row, 8).text()

            edit_dialog = QDialog(payment_window)
            edit_dialog.setWindowTitle("تعديل سجل السداد")
            edit_dialog.resize(600, 300)
            edit_dialog.setLayoutDirection(Qt.RightToLeft)
            main_layout = QVBoxLayout(edit_dialog)
            # اسم الحساب
            account_name_row = QHBoxLayout()
            account_name_label = QLabel("اسم الحساب:")
            account_name_label.setFixedWidth(150)
            account_name_input = QLabel(old_name)
            account_name_row.addWidget(account_name_label)
            account_name_row.addWidget(account_name_input)
            main_layout.addLayout(account_name_row)

            # id الحساب
            account_معرف_row = QHBoxLayout()
            account_معرف_label = QLabel("id الحساب:")
            account_معرف_label.setFixedWidth(150)
            account_معرف_input = QLabel(old_account_id)
            account_معرف_row.addWidget(account_معرف_label)
            account_معرف_row.addWidget(account_معرف_input)
            main_layout.addLayout(account_معرف_row)

            # المبلغ
            amount_row = QHBoxLayout()
            amount_label = QLabel("المبلغ المدفوع:")
            amount_label.setFixedWidth(150)
            amount_input = QLineEdit(old_amount)
            amount_input.setPlaceholderText("أدخل المبلغ (أرقام فقط)")
            amount_row.addWidget(amount_label)
            amount_row.addWidget(amount_input)
            main_layout.addLayout(amount_row)

            # الوصف
            desc_row = QHBoxLayout()
            desc_label = QLabel("الوصف:")
            desc_label.setFixedWidth(150)
            desc_input = QLineEdit(old_desc)
            desc_input.setPlaceholderText("أدخل الوصف")
            desc_row.addWidget(desc_label)
            desc_row.addWidget(desc_input)
            main_layout.addLayout(desc_row)

            # الوصف
            FATORA_row = QHBoxLayout()
            FATORA_label = QLabel("رقم الفاتورة:")
            FATORA_label.setFixedWidth(150)
            FATORA_input = QLineEdit(old_FATORA)
            FATORA_input.setPlaceholderText("أدخل رقم الفاتورة إن وجدت")
            FATORA_row.addWidget(FATORA_label)
            FATORA_row.addWidget(FATORA_input)
            main_layout.addLayout(FATORA_row)

            # الملاحظات
            notes_row = QHBoxLayout()
            notes_label = QLabel("ملاحظات:")
            notes_label.setFixedWidth(150)
            notes_input = QLineEdit(old_notes)  # استبدال QLineEdit بـ QTextEdit لملاحظات متعددة الأسطر
            notes_input.setPlaceholderText("أدخل ملاحظات إن وجدت")
            notes_row.addWidget(notes_label)
            notes_row.addWidget(notes_input)
            main_layout.addLayout(notes_row)

            # التاريخ
            date_row = QHBoxLayout()
            date_label = QLabel("تاريخ الدفع:")
            date_label.setFixedWidth(150)
            date_input = QDateEdit()
            date_input.setDisplayFormat("yyyy-MM-dd")
            date_input.setDate(QDate.fromString(old_date, "yyyy-MM-dd"))
            date_input.setCalendarPopup(True)
            date_input.setAlignment(Qt.AlignCenter)
            date_row.addWidget(date_label)
            date_row.addWidget(date_input)
            main_layout.addLayout(date_row)

            # أزرار التأكيد
            button_layout = QHBoxLayout()
            cancel_button = QPushButton("إلغاء")
            confirm_edit_button = QPushButton(qta.icon('fa5s.check-circle', color='green'), "تأكيد التعديل ")
            
            button_layout.addWidget(cancel_button)
            button_layout.addWidget(confirm_edit_button)
            
            main_layout.addLayout(button_layout)
            center_all_widgets(edit_dialog)

            #"جدول السداد تأكيد التعديل "
            def confirm_edit():
                amount = amount_input.text()
                date = date_input.date().toString("yyyy-MM-dd")
                desc = desc_input.text()
                fatora = FATORA_input.text()
                notes = notes_input.text()
                if not amount:
                    QMessageBox.warning(edit_dialog, "خطأ", "يرجى إدخال المبلغ")
                    return

                reply = QMessageBox.question(edit_dialog, "تأكيد", "هل تريد تعديل هذا السجل؟",QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.Yes:
                    conn, cursor = con_db()
                    # تحويل المبالغ لأرقام
                    try:
                        old_amount_value = float(old_amount)
                        new_amount_value = float(amount)
                    except ValueError:
                        QMessageBox.warning(edit_dialog, "خطأ", "تأكد من صحة المبلغ")
                        return

                    # جلب المبلغ والمدفوع الحالي من حسابات_الديون
                    cursor.execute("SELECT المبلغ, المدفوع FROM حسابات_الديون WHERE id=%s", (old_account_id,))
                    result = cursor.fetchone()

                    if result:
                        total_amount, paid = result
                        total_amount = float(total_amount or 0)
                        paid = float(paid or 0)

                        paid1 = paid - old_amount_value 
                        # تحديث المبلغ الجديد بطرح القديم ثم جمع الجديد
                        updated_paid = paid1 + new_amount_value
                        
                        updated_remaining = total_amount - updated_paid

                        # تحديث حسابات الديون
                        cursor.execute("""
                            UPDATE حسابات_الديون 
                            SET المدفوع=%s, الباقي=%s 
                            WHERE id=%s
                        """, (updated_paid, updated_remaining, old_account_id))


                    else:
                        QMessageBox.warning(edit_dialog, "خطأ", "لم يتم العثور على الحساب المرتبط بهذا الدين")
                        return
                    
                    cursor.execute("UPDATE دفعات_الديون SET المبلغ_المدفوع=%s, تاريخ_الدفع=%s, وصف_المدفوع=%s, رقم_الفاتورة=%s, ملاحظات=%s WHERE id=%s",
                                (amount, date, desc, fatora, notes,debt_id))
                    conn.commit()
                    update_payment_table()
                    update_table()
                    edit_dialog.close()
                    QMessageBox.information(payment_window, "نجاح", "تم تعديل السجل بنجاح")

            confirm_edit_button.clicked.connect(confirm_edit)
            cancel_button.clicked.connect(edit_dialog.reject)
            edit_dialog.exec_()


        # دالة الطباعة للجدول الكامل مع بيانات الحساب
        def print_payment():
            if selected_items:
                pattern = f"سجل السداد لـ: {person_name}\n"
            else:
                pattern = f"سجل السداد لجميع الحسابات\n"
            totel = f"<p>{NAME_label.text().replace(pattern, '').strip()}</p>"
            self.print_debts_report(payment_table,title,totel)



        # ربط الأزرار بالدوال
        print_payment_button.clicked.connect(print_payment)
        edit_payment_button.clicked.connect(edit_payment)
        delete_payment_button.clicked.connect(delete_payment)
        payment_window.exec()  
        # ربط الأزرار بالدوال
    add_transaction.clicked.connect(add_new_transaction)
    show_debt.clicked.connect(show_debt_history)
    #pay_button.clicked.connect(make_payment)
    show_payments_button.clicked.connect(show_payments)
        
    add_button.clicked.connect(add_person)
    print_button.clicked.connect(print_debts_report)
    edit_button.clicked.connect(edit_person)
    delete_button.clicked.connect(delete_person)

    update_table()
    all_button.setEnabled(False)
    window.show()


def check_due_debts(self):
    try:
        db_name = "project_manager2_debts"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        today_str = datetime.today().strftime("%Y-%m-%d")

        cursor.execute("""
            SELECT 
                سجل_الديون.id,
                سجل_الديون.معرف_الحساب,
                سجل_الديون.اسم_الحساب,
                سجل_الديون.نوع_الحساب,
                سجل_الديون.الوصف,
                سجل_الديون.المبلغ,
                سجل_الديون.تاريخ_السداد,
                حسابات_الديون.رقم_الهاتف
            FROM 
                سجل_الديون
            JOIN 
                حسابات_الديون ON حسابات_الديون.id = سجل_الديون.معرف_الحساب
            WHERE 
                STR_TO_DATE(سجل_الديون.تاريخ_السداد, '%Y-%m-%d') < %s
            ORDER BY 
                سجل_الديون.تاريخ_السداد ASC
        """, (today_str,))

        overdue_debts = cursor.fetchall()

        if overdue_debts:
            for debt in overdue_debts:
                debt_id, account_id, name, acc_type, description, amount, due_date, phone = debt

                message = f"""
                <div dir='rtl' style='font-family:"Segoe UI"; font-size:16px;'>
                <b>⚠  هناك حساب {acc_type} متأخر في السداد </b><br><br>
                👤 <b>الاسم:</b> {name}<br>
                📞 <b>الهاتف:</b> {phone}<br>
                🏷️ <b>نوع الحساب:</b> {acc_type}<br>
                💬 <b>الوصف:</b> {description}<br>
                💰 <b>المبلغ:</b> {amount} {Currency_type}<br>
                📅 <b>تاريخ السداد:</b> {due_date}
                </div>
                """

                msg_box = QMessageBox(self)
                msg_box.setWindowTitle("تنبيه سداد متأخر")
                msg_box.setTextFormat(Qt.RichText)
                msg_box.setText(message)
                msg_box.setIcon(QMessageBox.Warning)
                msg_box.setStyleSheet("..."  # نسخ نفس الـ CSS اللي عندك هنا
                )

                ok_btn = msg_box.addButton("تخطي", QMessageBox.AcceptRole)
                extend_btn = msg_box.addButton("⏳ تمديد السداد", QMessageBox.ActionRole)
                sadad_btn = msg_box.addButton("⏳تم السداد", QMessageBox.ActionRole)
                if acc_type == "دائن":
                    whatsapp_btn = msg_box.addButton("📲 مراسلة وتساب", QMessageBox.ActionRole)
                else:
                    copy_btn = msg_box.addButton("📋 نسخ رقم الهاتف", QMessageBox.ActionRole)
                msg_box.exec_()

                if acc_type != "دائن" and msg_box.clickedButton() == copy_btn:
                    clipboard = QApplication.clipboard()
                    clipboard.setText(f"{phone}")

                if msg_box.clickedButton() == extend_btn:
                    new_date, ok = QInputDialog.getText(
                        self, "تمديد السداد", "أدخل تاريخ السداد الجديد (yyyy-mm-dd):", 
                        text=(datetime.today() + timedelta(days=7)).strftime("%Y-%m-%d")
                    )
                    if ok and new_date:
                        try:
                            cursor.execute(
                                "UPDATE سجل_الديون SET تاريخ_السداد = %s WHERE id = %s",
                                (new_date, debt_id)
                            )
                            conn.commit()
                            QMessageBox.information(self, "تم التحديث", f"تم تمديد السداد حتى {new_date}")
                        except Exception as ex:
                            QMessageBox.critical(self, "خطأ", f"فشل التحديث: {str(ex)}")
                
                if msg_box.clickedButton() == sadad_btn:
                    reply = QMessageBox.question(self, "تأكيد", f"هل تم سداد مبلغ {amount}",
                                            QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                    if reply == QMessageBox.Yes:
                        try:
                            cursor.execute(
                                "UPDATE سجل_الديون SET تاريخ_السداد = %s,الباقي = %s WHERE id = %s",
                                ("تم السداد",0, debt_id)
                            )
                            cursor.execute(
                            "INSERT INTO دفعات_الديون (معرف_الحساب, اسم_الحساب, نوع_الحساب, المبلغ_المدفوع, تاريخ_الدفع, وصف_المدفوع, ملاحظات, رقم_الفاتورة) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)",
                            (account_id, name, acc_type, amount, today_str, description,"", "")
                            )

                            cursor.execute(
                            "UPDATE حسابات_الديون SET المدفوع = المدفوع + %s, الباقي = الباقي - %s WHERE id = %s", 
                            (amount, amount, account_id)
                            )
                            
                            conn.commit()
                            QMessageBox.information(self, "تم التحديث", f" تم سداد المبلغ {amount}")
                        except Exception as ex:
                            QMessageBox.critical(self, "خطأ", f"فشل التحديث: {str(ex)}")
                    

                elif acc_type == "دائن" and msg_box.clickedButton() == whatsapp_btn:
                    message_text = (
                        f"السلام عليكم {name}،\n"
                        f"نذكركم بأن هناك مبلغ مستحق بقيمة {amount} {Currency_type}\n"
                        f"الوصف: {description}\n"
                        f"تاريخ السداد: {due_date}\n"
                        f"يرجى التواصل معنا. شكرًا.\n"
                        f"{company_name}"
                    )
                    encoded_message = message_text.replace(' ', '%20').replace('\n', '%0A')
                    whatsapp_url = f"https://wa.me/{phone}?text={encoded_message}"
                    webbrowser.open(whatsapp_url)

        conn.close()

    except Exception as e:
        pass
