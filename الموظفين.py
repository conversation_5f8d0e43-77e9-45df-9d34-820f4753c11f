from for_all import*

# نافذة رصيد الموظفين ///////////////////////////////////////////////////////////////////////////// 

def open_balance_moratb(self):
    moamala = ["إضافة مرتب", "إضافة نسبة", "إضافة رصيد"]
    filter_types = moamala  # فقط هذه الأنواع تظهر في الجدول
    self.open_balance_dialog("إضافة مرتب", moamala, filter_types)

def open_balance_sdad(self):
    selected_items = self.table.selectedItems()
    if not selected_items:
        QMessageBox.warning(self, "خطأ", f"يجب تحديد معاملة أولا")
        return  
    try:
        balance_text = self.table.item(self.table.currentRow(), 8).text()
        if int(balance_text) <= 0:
            reply = QMessageBox.warning(self, "تنبيه", "رصيد الموظف غير كافي، هل تريد الاستمرار؟", QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.No:
                return
    except (AttributeError, ValueError):
        QMessageBox.warning(self, "خطأ", "يجب تحديد صف الموظف أولا")
        return

    moamala = ["سداد مبلغ", "خصم مبلغ", "الغاء سحب"]
    filter_types = moamala
    self.open_balance_dialog("سداد مبلغ", moamala, filter_types)

def open_balance_dialog(self, add_box, moamala, filter_types):
    self.transaction_filter_types = filter_types  # حفظ الفلاتر في خاصية

#   def open_balance_dialog(self,add_box,moamala):
    dialog = QDialog(self)
    dialog.setWindowTitle("رصيد الموظف")
    dialog.resize(900, 650)
    dialog.setLayoutDirection(QtCore.Qt.RightToLeft)
    dialog.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
    
    gridLayout_2 = QtWidgets.QGridLayout(dialog)
    gridLayout_2.setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
    gridLayout_2.setContentsMargins(10, 10, 10, 10)
    gridLayout_2.setSpacing(10)

    gridLayout = QtWidgets.QGridLayout()
    gridLayout.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
    gridLayout.setContentsMargins(0, 0, 0, 0)
    
    employee_name = QLabel("اسم الموظف") 
    employee_name.setMinimumSize(QtCore.QSize(50, 40))
    employee_name.setAlignment(Qt.AlignCenter)
    gridLayout.addWidget(employee_name, 0, 0, 1, 1) 
    
    self.employee_name_lineEdit = QtWidgets.QLineEdit(dialog)
    self.employee_name_lineEdit.setReadOnly(True)
    self.employee_name_lineEdit.setAlignment(Qt.AlignCenter) 
    gridLayout.addWidget(self.employee_name_lineEdit, 0, 1, 1, 1)  # Adjust the position as needed
    
    moathf_label = QtWidgets.QLabel("نوع الحساب")
    moathf_label.setMinimumSize(QtCore.QSize(50, 40))
    moathf_label.setLayoutDirection(QtCore.Qt.RightToLeft)
    moathf_label.setAlignment(QtCore.Qt.AlignCenter)
    gridLayout.addWidget(moathf_label, 1, 0, 1, 1)
    
    selected_items = self.table.selectedItems()
    if not selected_items:
        QMessageBox.warning(self, "خطأ", f"يجب تحديد معاملة أولا")
        return  
    
    try:
        entry_id = self.table.item(self.table.currentRow(), 1).text()
    except Exception as e:
        QMessageBox.warning(self, "خطأ", f"يجب تحديد صف الموظف أولا")
        return
    
    self.moathf_lineEdit = QtWidgets.QLineEdit(dialog)
    self.moathf_lineEdit.setMinimumSize(QtCore.QSize(0, 40))
    self.moathf_lineEdit.setLayoutDirection(QtCore.Qt.RightToLeft)
    self.moathf_lineEdit.setText("")
    self.moathf_lineEdit.setAlignment(QtCore.Qt.AlignCenter)
    self.moathf_lineEdit.setPlaceholderText(" ادخل كود الموظف")
    self.moathf_lineEdit.setText(str(entry_id))
    gridLayout.addWidget(self.moathf_lineEdit, 1, 1, 1, 1)
    self.moathf_lineEdit.textChanged.connect(self.load_reports)  # Connect the textChanged signal to load_reports
    
    label = QtWidgets.QLabel("المبلغ")
    label.setMinimumSize(QtCore.QSize(50, 40))
    label.setLayoutDirection(QtCore.Qt.RightToLeft)
    label.setAlignment(QtCore.Qt.AlignCenter)
    gridLayout.addWidget(label, 2, 0, 1, 1)
    
    self.lineEdit = QtWidgets.QLineEdit(dialog)
    self.lineEdit.setMinimumSize(QtCore.QSize(0, 40))
    self.lineEdit.setLayoutDirection(QtCore.Qt.RightToLeft)
    self.lineEdit.setText("")
    self.lineEdit.setAlignment(QtCore.Qt.AlignCenter)
    self.lineEdit.setPlaceholderText("يجب إدخال المبلغ")
    gridLayout.addWidget(self.lineEdit, 2, 1, 1, 1)

    label_2 = QtWidgets.QLabel("الوصف")
    label_2.setMinimumSize(QtCore.QSize(0, 40))
    label_2.setAlignment(QtCore.Qt.AlignCenter)
    
    gridLayout.addWidget(label_2, 3, 0, 1, 1)
    
    lineEdit_2 = QtWidgets.QLineEdit(dialog)
    lineEdit_2.setMinimumSize(QtCore.QSize(0, 40))
    lineEdit_2.setAlignment(QtCore.Qt.AlignCenter)
    lineEdit_2.setPlaceholderText("الوصف (إختياري)")
    gridLayout.addWidget(lineEdit_2, 3, 1, 1, 1)
    
    self.label_4 = QtWidgets.QLabel("النسبة")
    self.label_4.setMinimumSize(QtCore.QSize(0, 40))
    self.label_4.setAlignment(QtCore.Qt.AlignCenter)
    gridLayout.addWidget(self.label_4, 4, 0, 1, 1)
    
    self.lineEdit_4 = QtWidgets.QLineEdit(dialog)
    self.lineEdit_4.setMinimumSize(QtCore.QSize(0, 40))
    self.lineEdit_4.setAlignment(QtCore.Qt.AlignCenter)
    self.lineEdit_4.setPlaceholderText("إدخل النسبة عند اختيار نوع المعاملة (اضافة نسبة)")
    gridLayout.addWidget(self.lineEdit_4, 4, 1, 1, 1)
    
    label_3 = QtWidgets.QLabel("التاريخ")
    label_3.setMinimumSize(QtCore.QSize(0, 40))
    label_3.setAlignment(QtCore.Qt.AlignCenter)
    gridLayout.addWidget(label_3, 5, 0, 1, 1)
    
    dateEdit = QtWidgets.QDateEdit(dialog)
    dateEdit.setMinimumSize(QtCore.QSize(0, 40))
    dateEdit.setAlignment(QtCore.Qt.AlignCenter)
    dateEdit.setObjectName("dateEdit")
    dateEdit.setCalendarPopup(True)
    dateEdit.setDisplayFormat("dd/MM/yyyy")
    dateEdit.setDate(QDate.currentDate())
    gridLayout.addWidget(dateEdit, 5, 1, 1, 1)

    self.comboBox = QtWidgets.QComboBox(dialog)
    sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
    sizePolicy.setHorizontalStretch(0)
    sizePolicy.setVerticalStretch(0)
    sizePolicy.setHeightForWidth(self.comboBox.sizePolicy().hasHeightForWidth())
    
    self.comboBox.setSizePolicy(sizePolicy)
    self.comboBox.setMinimumSize(QtCore.QSize(50, 40))
    self.comboBox.setInsertPolicy(QtWidgets.QComboBox.NoInsert)
    self.moamala=moamala
    self.comboBox.addItems(moamala)
    
    self.add_box = add_box
    self.comboBox.setItemText(0,add_box)
    line_edit = QtWidgets.QLineEdit()
    line_edit.setAlignment(QtCore.Qt.AlignCenter) 
    self.comboBox.setLineEdit(line_edit)
    self.comboBox.setEditable(True)
    self.comboBox.lineEdit().setReadOnly(True) 
    self.comboBox.setStyleSheet(f"""
        QLabel {{
            font-family: {font_app}
            ;font-weight: bold; font-size: 16px;background-color: #e0e0e0 ;width:100px;height:10px;
        }}""")
    
    self.comboBox.lineEdit().mousePressEvent = lambda event: open_combo(self.comboBox, event)
    self.comboBox.setItemDelegate(AlignedItemDelegate(self.comboBox))
    self.comboBox.currentTextChanged.connect(self.load_data1)  
    gridLayout.addWidget(self.comboBox, 6, 0, 1, 1)

    pushButton_2 = QtWidgets.QPushButton(qta.icon('fa5s.plus', color='darkgreen'), "إضافة المعاملة ", dialog)
    pushButton_2.setMinimumSize(QtCore.QSize(0, 40))
    pushButton_2.setLayoutDirection(QtCore.Qt.RightToLeft)
    pushButton_2.clicked.connect(lambda: self.save_transaction(self.year_combo, self.moathf_lineEdit, self.lineEdit, lineEdit_2,self.lineEdit_4, dateEdit, self.comboBox))
    gridLayout.addWidget(pushButton_2, 6, 1, 1, 1)
    
    self.tableWidget = QtWidgets.QTableWidget(dialog)
    #self.tableWidget.setColumnCount(6) 
    headers = ["الرقم", "       نوع المعاملة       ", "     المبلغ     ", "     التاريخ     ","    النسبة    ", "                                  الوصف                                  ", "id"]
    self.tableWidget.setColumnCount(len(headers))
    self.tableWidget.setHorizontalHeaderLabels(headers)
   
    gridLayout.addWidget(self.tableWidget, 8, 0, 1, 2)
    gridLayout_2.addLayout(gridLayout, 0, 1, 1, 1)
    dialog.setLayout(gridLayout_2)

    
    
    # زر الحذف
    delete_button = QPushButton(qta.icon('fa5s.trash', color='crimson'), "حذف معاملة ")
    delete_button.setMinimumSize(QtCore.QSize(0, 40))
    delete_button.setLayoutDirection(QtCore.Qt.RightToLeft)
    # delete_btn_icon = os.path.join(icons_dir, 'delete_icon.png')
    # delete_button.setIcon(QIcon(delete_btn_icon))
    delete_button.clicked.connect(self.employee_delete_selected_row)
    gridLayout.addWidget(delete_button, 9, 0, 1, 1)
    # زر الطباعة
    print_button = QPushButton(qta.icon('fa5s.print', color='navy'), "طباعة فاتورة ")
    print_button.setMinimumSize(QtCore.QSize(0, 40))
    print_button.setLayoutDirection(QtCore.Qt.RightToLeft)
    print_button.clicked.connect(self.print_Employee)
    # print_button_icon = os.path.join(icons_dir, 'printer.png')
    # print_button.setIcon(QIcon(print_button_icon))
    gridLayout.addWidget(print_button, 9, 1, 1, 2)
                    
    self.load_reports()
    dialog.exec()
    #Basic_Styles(self)
    

# def load_reports(self):
#     moathf_code = self.moathf_lineEdit.text()
#     if not moathf_code:
#         return
#     selected_year = self.year_combo.currentText()
#     db_name = f"project_manager_V2"
#     try:
#         conn = mysql.connector.connect(host=host,user=user,password=password,database=db_name)
#         cursor = conn.cursor()
#         # # Fetch employee name based on the code
#         # cursor.execute("SELECT اسم_الموظف FROM الموظفين_معاملات_مالية WHERE id=%s", (moathf_code,))
#         # employee_name = cursor.fetchone()
#         # if employee_name:
#         #     self.employee_name_lineEdit.setText(employee_name[0])
#         # else:
#         #     self.employee_name_lineEdit.setText("")
        
#         cursor.execute("SELECT id, نوع_المعاملة, المبلغ, التاريخ , النسبة, الوصف FROM الموظفين_معاملات_مالية WHERE id=%s", (moathf_code,))
#         reports = cursor.fetchall()
#         self.tableWidget.setRowCount(0)
#         for row_number, row_data in enumerate(reversed(reports)):
#             self.tableWidget.insertRow(row_number)
#             for column_number, data in enumerate(row_data):
#                 item = QTableWidgetItem(str(data))
#                 item.setTextAlignment(Qt.AlignCenter)
#                 self.tableWidget.setItem(row_number, column_number, item) 
#         conn.close()
#         self.load_data1()
#         colorize(self.tableWidget,"#cdd7b9","#dc8484")
#     except Exception as e:
#         QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

def load_reports(self):
    moathf_code = self.moathf_lineEdit.text()
    if not moathf_code:
        return
    selected_year = self.year_combo.currentText()
    db_name = f"project_manager_V2"
    try:
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, نوع_المعاملة, المبلغ, التاريخ, النسبة, الوصف FROM الموظفين_معاملات_مالية WHERE id=%s", (moathf_code,))
        reports = cursor.fetchall()
        self.tableWidget.setRowCount(0)

        for row_number, row_data in enumerate(reversed(reports)):
            self.tableWidget.insertRow(row_number)

            # العمود الأول: الترقيم يبدأ من 1
            #item_number = QTableWidgetItem(str(row_number + 1))
            item_number = QTableWidgetItem(str(len(reports) - row_number))

            item_number.setTextAlignment(Qt.AlignCenter)
            self.tableWidget.setItem(row_number, 0, item_number)

            # الأعمدة 1 إلى 5: نوع المعاملة، المبلغ، التاريخ، النسبة، الوصف
            for i in range(1, len(row_data)):
                item = QTableWidgetItem(str(row_data[i]))
                item.setTextAlignment(Qt.AlignCenter)
                self.tableWidget.setItem(row_number, i, item)

            # العمود الأخير: نخزن فيه الـ id الحقيقي (مخفي)
            hidden_معرف_item = QTableWidgetItem(str(row_data[0]))  # id
            hidden_معرف_item.setTextAlignment(Qt.AlignCenter)
            self.tableWidget.setItem(row_number, 6, hidden_معرف_item)

        conn.close()
        self.tableWidget.setColumnHidden(6, True)  # إخفاء عمود id
        self.load_data1()
        colorize(self.tableWidget, "#cdd7b9", "#dc8484")

    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    table_setting(self.tableWidget)

#تلوين الخلايا في الجدول
def colorize(table,green,red):
    col_idx = 1
    for row_idx in range(table.rowCount()):
        item4 = table.item(row_idx, col_idx)
        if item4:
            col_data = item4.text()
            #item4.setBackground(QColor(green))
                
    # # البحث عن العمود الذي يحمل اسم "نوع الحساب"
    col_idx = -1
    for col in range(table.columnCount()):
        header_item = table.horizontalHeaderItem(col)
        if header_item and header_item.text() == "       نوع المعاملة       " or header_item.text() == "نوع المعاملة"  or header_item.text() == "   نوع المعاملة   "  :
            col_idx = col
            break
    # إذا تم العثور على العمود، يتم تلوينه
    if col_idx != -1:
        for row_idx in range(table.rowCount()):
            item = table.item(row_idx, col_idx)
            if item:
                col_data = item.text().strip()
                if col_data == "إضافة مرتب":
                    item.setBackground(QColor(green))
                elif col_data == "إضافة نسبة":
                    item.setBackground(QColor(green))
                elif col_data == "إضافة رصيد":
                    item.setBackground(QColor(green))
                elif col_data == "سداد مبلغ":
                    item.setBackground(QColor(red))
                elif col_data == "خصم مبلغ":
                    item.setBackground(QColor(red))
                elif col_data == "الغاء سحب":
                    item.setBackground(QColor(red))
# 

# def load_reports(self):
#     moathf_code = self.moathf_lineEdit.text()
#     if not moathf_code:
#         return
#     selected_year = self.year_combo.currentText()
#     db_name = f"project_manager_V2"
#     try:
#         conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
#         cursor = conn.cursor()
#         query = "SELECT id, نوع_المعاملة, المبلغ, التاريخ, النسبة, الوصف FROM الموظفين_معاملات_مالية WHERE id=%s"
#         params = [moathf_code]

#         query = """
#             SELECT id, نوع_المعاملة, المبلغ, التاريخ, النسبة, الوصف
#             FROM الموظفين_معاملات_مالية
#             WHERE id=%s
#         """
#         params = [moathf_code]

#         # إذا تم تحديد فلاتر أنواع معاملات، نضيفها للاستعلام
#         if hasattr(self, "transaction_filter_types") and self.transaction_filter_types:
#             placeholders = ','.join(['%s'] * len(self.transaction_filter_types))
#             query += f" AND نوع_المعاملة IN ({placeholders})"
#             params += self.transaction_filter_types

#         # ترتيب النتائج من الأحدث إلى الأقدم
#         query += " ORDER BY التاريخ DESC"

#         cursor.execute(query, params)
#         reports = cursor.fetchall()
#         self.tableWidget.setRowCount(0)

#         for row_number, row_data in enumerate(reports):  # مباشرة بدون reverse
#             self.tableWidget.insertRow(row_number)
#             for column_number, data in enumerate(row_data):
#                 item = QTableWidgetItem(str(data))
#                 item.setTextAlignment(Qt.AlignCenter)
#                 self.tableWidget.setItem(row_number, column_number, item) 
#         conn.close()
#         self.load_data1()
#     except Exception as e:
#         QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

def load_data1(self):
    transaction_type = self.comboBox.currentText()
    moathf_code = self.moathf_lineEdit.text()
    if not moathf_code:
        QMessageBox.warning(self, "خطأ", "يجب إدخال كود الموظف.")
        return
    selected_year = self.year_combo.currentText()
    db_name = f"project_manager_V2"
    try:
        conn = mysql.connector.connect(host=host,user=user,password=password,database=db_name)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM الموظفين WHERE id=%s", (moathf_code,))
        employee = cursor.fetchone()
        if employee:
            name = employee[2]
            self.employee_name_lineEdit.setText(str(name))
            salary = employee[5]
            rate = employee[6] if employee[6] is not None else 0
            rased = employee[8]
            if transaction_type == "إضافة مرتب":
                self.lineEdit.setText(str(salary))
                self.lineEdit_4.setText(str(0))
                self.label_4.hide()
                self.lineEdit_4.hide()
            elif transaction_type == "سداد مبلغ":# and self.lineEdit.text() != str(salary):
                self.lineEdit.setText(str(rased))
                self.lineEdit_4.setText(str(0))
                self.label_4.hide()
                self.lineEdit_4.hide()
            elif transaction_type == "إضافة نسبة":
                self.lineEdit_4.setText(str(rate))   
                self.label_4.show()
                self.lineEdit_4.show()
            elif transaction_type != "إضافة نسبة":
                self.lineEdit_4.setText(str(0))
                self.label_4.hide()
                self.lineEdit_4.hide()
            
        conn.close()   
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")


def save_transaction(self, year_combo, moathf_lineEdit, lineEdit, lineEdit_2,lineEdit_4, dateEdit, comboBox):
    selected_year = self.year_combo.currentText()
    transaction_type = self.comboBox.currentText()
    db_name = f"project_manager_V2"
    try:
        try:
            moathf_code = moathf_lineEdit.text()
            amount = int(lineEdit.text())
            if transaction_type == "إضافة نسبة":
                #amount = (int(lineEdit.text()) / 100) * int(lineEdit_4.text())
                amount = int((int(lineEdit.text()) / 100) * int(lineEdit_4.text()))
            description = lineEdit_2.text()
            date = dateEdit.date().toString("yyyy-MM-dd")
            transaction_type = comboBox.currentText()
        except ValueError:
            QMessageBox.warning(self, "خطأ","يجب اضافة مبلغ او نسبة كرقم صحيح")
            return

        conn = mysql.connector.connect(host=host,user=user,password=password,database=db_name)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM الموظفين WHERE id=%s", (moathf_code,))
        employee = cursor.fetchone()

        if employee:
            employee_id = employee[0]
            employee_name = employee[2]
            job_title = employee[3]
            salary = employee[5]
            rate = int(lineEdit_4.text())
            #self.lineEdit_4.setText(str(employee[6]))
            current_balance = int(employee[8])
            withdrawal = int(employee[9]) if employee[9] else 0
                            
                            
            # رسالة التأكيد
            # msg = QMessageBox()
            # msg.setIcon(QMessageBox.Question)
            # msg.setWindowTitle("تأكيد")
            # msg.setText(f"هل تريد حفظ المعاملة التالية؟\n\nاسم الموظف: {employee_name}\nنوع المعاملة: {transaction_type}\nالمبلغ: {int(amount)}")
            # msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            # result = msg.exec()

            result = GEN_MSG_BOX('تأكيد معاملة موظف',f"هل تريد حفظ المعاملة التالية؟\nاسم الموظف : {employee_name}\nنوع المعاملة : {transaction_type}\nالمبلغ : {int(amount)}",'information.png','تأكيد','إلغاء',msg_box_color)
            if result == QMessageBox.Ok:
                # إضافة سجل جديد إلى جدول تقارير الموظفين
                cursor.execute('''
                    INSERT INTO الموظفين_معاملات_مالية (التصنيف, اسم_الموظف, الوظيفة, نوع_المعاملة, المبلغ, التاريخ,النسبة, الوصف, معرف_الموظف)
                    VALUES (%s, %s, %s, %s, %s, %s, %s,%s,%s)
                ''', (moathf_code, employee_name, job_title, transaction_type, amount, date, rate, description, employee_id))

                # تعديل الرصيد في جدول الموظفين بناءً على نوع المعاملة
                if transaction_type == "إضافة رصيد":
                    new_balance = current_balance + amount
                elif transaction_type == "سداد مبلغ":
                    balance_text = self.table.item(self.table.currentRow(), 8).text()
                    if int(balance_text) <= 0 or int(balance_text) < amount:
                        reply = QMessageBox.warning(
                            self,
                            "تنبيه",
                            "   رصيد الموظف أقل من مبلغ السداد ، هل تريد الاستمرار؟",
                            QMessageBox.Yes | QMessageBox.No
                        )
                        if reply == QMessageBox.No:
                            return
                    new_balance = current_balance - amount
                    withdrawal += amount
                    cursor.execute("UPDATE الموظفين SET السحب=%s WHERE id=%s", (withdrawal, employee_id))
                    
                elif transaction_type == "خصم مبلغ":
                    new_balance = current_balance - amount
                elif transaction_type == "الغاء سحب":
                    new_balance = current_balance
                    withdrawal -= amount
                    cursor.execute("UPDATE الموظفين SET السحب=%s WHERE id=%s", (withdrawal, employee_id))
                elif transaction_type == "إضافة مرتب":
                    new_balance = current_balance + amount
                elif transaction_type == "إضافة نسبة":
                    new_balance = current_balance + int(amount)
                
                cursor.execute("UPDATE الموظفين SET الرصيد=%s WHERE id=%s", (new_balance, employee_id))

                # الحصول على id المعاملة المضافة
                transaction_id = cursor.lastrowid

                # حفظ التغييرات
                conn.commit()

                # ===== الربط المحاسبي =====
                try:
                    # إنشاء نظام الربط المحاسبي
                    from accounting_integration import AccountingIntegration
                    accounting = AccountingIntegration()

                    # إعداد بيانات معاملة الموظف للنظام المحاسبي
                    employee_data = {
                        'id': transaction_id,
                        'معرف_الموظف': employee_id,
                        'اسم_الموظف': employee_name,
                        'الوظيفة': job_title,
                        'نوع_المعاملة': transaction_type,
                        'المبلغ': amount,
                        'التاريخ': date,
                        'النسبة': rate,
                        'الوصف': description,
                        'المستخدم': 'النظام'  # يمكن تحديث هذا ليكون المستخدم الحالي
                    }

                    # تسجيل معاملة الموظف محاسبياً
                    success, message = accounting.record_employee_transaction(employee_data)

                    if success:
                        print(f"تم تسجيل معاملة الموظف محاسبياً: {message}")
                    else:
                        print(f"خطأ في التسجيل المحاسبي: {message}")
                        # يمكن إضافة تحذير للمستخدم هنا إذا أردت

                    accounting.close_connection()

                except Exception as e:
                    print(f"خطأ في الربط المحاسبي: {e}")
                    # الاستمرار حتى لو فشل الربط المحاسبي
                # ===== نهاية الربط المحاسبي =====

                entry_id = self.table.item(self.table.currentRow(), 0).text()
                table_name = self.Interface_combo.currentText()
                cursor.execute(f"SELECT * FROM {table_name} WHERE id = %s", (entry_id,))
                updated_row = cursor.fetchone()
                if updated_row:
                    self.table.setRowCount(self.table.rowCount())  # لضمان وجود الصف
                    for col_idx, col_data in enumerate(updated_row):
                        item = QTableWidgetItem(str(col_data))
                        item.setTextAlignment(Qt.AlignCenter)
                        self.table.setItem(self.table.currentRow(), col_idx, item)

                conn.close()
                self.load_reports()
                self.calculate_current_month_total()
                self.update_paid_lcd()
                self.colorize_cells(self.table)
                QMessageBox.information(self, "نجاح", "تم حفظ المعاملة بنجاح.")

                
        else:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على الموظف.")
            
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

# حذف معاملة////////////////////////////////////////////////////////////////////
def employee_delete_selected_row(self):
    selected_items = self.tableWidget.selectedItems()
    
    if not selected_items:
        QMessageBox.warning(self, "تحذير", "يرجى تحديد الصف المراد حذفه.")
        return

    # الحصول على الصف المحدد
    selected_row = selected_items[0].row()
    transaction_type = self.tableWidget.item(selected_row, 1).text()  # نوع المعاملة

    # الحصول على id الصف المحدد
    #record_معرف_item = self.tableWidget.item(selected_row, 0)
    record_معرف_item = self.tableWidget.item(selected_row, 6)
    if record_معرف_item is None:
        QMessageBox.critical(self, "خطأ", "تعذر تحديد الid.")
        return

    record_id = record_معرف_item.text()
    selected_year = self.year_combo.currentText()
    db_name = f"project_manager_V2"

    # تأكيد الحذف
    confirmation = QMessageBox.question(
        self,
        "تأكيد الحذف",
        "هل أنت متأكد أنك تريد حذف هذا الصف؟",
        QMessageBox.Yes | QMessageBox.No
    )
    if confirmation == QMessageBox.No:
        return

    # الاتصال بقاعدة البيانات
    conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
    cursor = conn.cursor()

    # جلب بيانات الدفعة قبل الحذف
    cursor.execute("SELECT التصنيف, المبلغ FROM الموظفين_معاملات_مالية WHERE id = %s", (record_id,))
    payment_data = cursor.fetchone()

    if not payment_data:
        QMessageBox.critical(self, "خطأ", "تعذر العثور على بيانات الدفعة.")
        conn.close()
        return

    project_code, paid_amount = payment_data
    paid_amount = int(paid_amount)

    # جلب بيانات المشروع المرتبط
    cursor.execute("SELECT الرصيد, السحب FROM الموظفين WHERE id = %s", (project_code,))
    project_data = cursor.fetchone()

    if not project_data:
        QMessageBox.critical(self, "خطأ", "تعذر العثور على المشروع المرتبط.")
        conn.close()
        return

    current_paid, withdrawal = project_data
    withdrawal = int(withdrawal)

    # تعديل الرصيد في جدول الموظفين بناءً على نوع المعاملة
    if transaction_type == "إضافة رصيد":
        new_balance = current_paid - paid_amount
    elif transaction_type == "سداد مبلغ":
        new_balance = current_paid + paid_amount
        withdrawal -= paid_amount
    elif transaction_type == "خصم مبلغ":
        new_balance = current_paid + paid_amount
    elif transaction_type == "الغاء سحب":
        new_balance = current_paid
        withdrawal += paid_amount
    elif transaction_type == "إضافة مرتب":
        new_balance = current_paid - paid_amount
    elif transaction_type == "إضافة نسبة":
        new_balance = current_paid - paid_amount
    else:
        QMessageBox.critical(self, "خطأ", f"نوع المعاملة '{transaction_type}' غير مدعوم.")
        conn.close()
        return


    # تحديث جدول "المشاريع"
    cursor.execute("UPDATE الموظفين SET الرصيد = %s, السحب = %s WHERE id = %s",(new_balance, withdrawal, project_code))

    # حذف الدفعة من جدول "المشاريع_المدفوعات"
    cursor.execute("DELETE FROM الموظفين_معاملات_مالية WHERE id = %s", (record_id,))
    conn.commit()
            
    # التأكد من تنفيذ التحديثات
    conn.commit()

    selected_row_table = self.table.currentRow()
    table_name = self.Interface_combo.currentText()
    entry_id = self.table.item(selected_row_table, 0).text()
    
    #تحديث الصف الرئيسي
    cursor.execute(f"SELECT * FROM {table_name} WHERE id = %s", (entry_id,))
    updated_row = cursor.fetchone()
    if updated_row:
        self.table.setRowCount(self.table.rowCount())  # لضمان وجود الصف
        for col_idx, col_data in enumerate(updated_row):
            item = QTableWidgetItem(str(col_data))
            item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(self.table.currentRow(), col_idx, item)

    # حذف الصف من جدول الدفعات في الواجهة
    self.tableWidget.removeRow(selected_row)
            
    QMessageBox.information(self, "نجاح", "تم حذف الصف وتحديث البيانات بنجاح.")
    conn.close()
    
    # تحديث الواجهة
    self.colorize_cells(self.table)
    self.update_paid_lcd()
    self.count_projects_in_progress()
    self.sum_remaining_amount()
    self.calculate_current_month_total()
    self.display_selected_row_data()
                  
#تقارير المظفين //////////////////////////////////////////////////////////////////////////////  
def open_reports_dialog(self):
    dialog = QDialog(self)
    dialog.setWindowTitle("تقارير الموظفين")
    dialog.resize(1200, 600)
    dialog.setLayoutDirection(QtCore.Qt.RightToLeft)
    dialog.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
    
    layout = QVBoxLayout(dialog)
    
    # إضافة خيارات البحث والسنة
    search_layout = QHBoxLayout()
    search_label = QLabel("بحث:")
    search_label.setFixedSize(100, 30)
    search_label.setAlignment(Qt.AlignCenter)
    self.search_lineEdit = QLineEdit()
    self.search_lineEdit.setPlaceholderText("ادخل اسم الموظف أو كود الموظف أو نوع المعاملة او التاريخ او الوظيفة")
    self.search_lineEdit.textChanged.connect(self.filter_reports)
    self.search_lineEdit.setAlignment(Qt.AlignCenter)
    
    year_label = QLabel("السنة:")
    year_label.setAlignment(Qt.AlignCenter)
    year_label.setFixedSize(100, 30)
    self.year_combo1 = QComboBox()
    
    self.populate_years(self.year_combo1) 
    self.populate_years1(self.year_combo1) 
    self.year_combo1.currentTextChanged.connect(self.load_reports_all)
    
    self.year_combo1.lineEdit().mousePressEvent = lambda event: open_combo(self.year_combo1, event)
    
    search_layout.addWidget(search_label)
    search_layout.addWidget(self.search_lineEdit)
    search_layout.addWidget(year_label)
    search_layout.addWidget(self.year_combo1)
    
    layout.addLayout(search_layout)
    
    
    total_layout = QHBoxLayout()
    self.Employee_Balance_label = QLabel(' رصيد الموظفين: 0', self)
    self.Employee_Balance_label.setStyleSheet("background-color: #e4c19b; padding: 5px;")
    self.Employee_Balance_label.setAlignment(Qt.AlignCenter)

    self.Employee_Withdraw_label = QLabel('سحب الموظفين: 0', self)
    self.Employee_Withdraw_label.setStyleSheet("background-color: #e48f8b; padding: 5px;")
    self.Employee_Withdraw_label.setAlignment(Qt.AlignCenter)
    
    total_layout.addWidget(self.Employee_Balance_label)
    total_layout.addWidget(self.Employee_Withdraw_label)
    # إضافة الصف إلى التخطيط الرئيسي
    layout.addLayout(total_layout)
    
    
    # إضافة الجدول
    self.reports_table = QTableWidget()
    headers= ["الرقم", "التصنيف", "   اسم الموظف   ", "   الوظيفة   ", "   نوع المعاملة   ", "  المبلغ  ", "  التاريخ  ", "  النسبة  ", "  الوصف  "]
    add_table_column(self.reports_table,headers)
    layout.addWidget(self.reports_table)

    self.reports_table.setColumnHidden(1, True)
    
    # إضافة زر الطباعة
    print_button = QPushButton(qta.icon('fa5s.print', color='navy'), "طباعة تقرير ")
    print_button.setMinimumSize(QtCore.QSize(0, 40))
    print_button.setLayoutDirection(QtCore.Qt.RightToLeft)
    print_button.clicked.connect(self.print_reports)
    # print_button_icon = os.path.join(icons_dir, 'printer.png')
    # print_button.setIcon(QIcon(print_button_icon))
    layout.addWidget(print_button)
    
    dialog.setLayout(layout)
    self.load_reports_all()
    self.Employee_calculate_totals()
    dialog.exec()
    
#السنة للتقارير
def populate_years1(self,comp):
    selected_year = self.year_combo.currentText()
    project_dir = folder_path
    if not os.path.exists(project_dir):
        os.makedirs(project_dir)
    pattern = re.compile(r'database_(\d{4})')
    #current_year = QDate.currentDate().year()
    current_year = selected_year
    # years = set()
    # for file_name in os.listdir(project_dir):
    #     match = pattern.match(file_name)
    #     if match:
    #         years.add(int(match.group(1)))
    # if current_year not in years:
    #     years.add(current_year)
    # years = sorted(years, reverse=True)
    # for year in years:
    #     self.year_combo1.addItem(str(year))
    comp.setCurrentText(str(current_year))    
    comp.setItemDelegate(AlignedItemDelegate(comp))
    
    line_edit = QtWidgets.QLineEdit()
    line_edit.setAlignment(QtCore.Qt.AlignCenter)  # Center the text
    comp.setLineEdit(line_edit)
    comp.setEditable(True)
    comp.lineEdit().setReadOnly(True) 


def filter_reports(self):
    # تطبيق منطق التصفية هنا بناءً على النص المدخل في self.search_lineEdit
    search_text = self.search_lineEdit.text().lower()
    
    for row in range(self.reports_table.rowCount()):
        item_col1 = self.reports_table.item(row, 1)  # العمود الثاني (اسم الموظف)
        item_col2 = self.reports_table.item(row, 2)  # العمود الثاني (اسم الموظف)
        item_col3 = self.reports_table.item(row, 3)  # العمود الثاني (اسم الموظف)
        item_col4 = self.reports_table.item(row, 4)  # العمود الرابع
        item_col6 = self.reports_table.item(row, 6)  # العمود الخامس
        
        # التحقق من النص في الأعمدة 2، 4، و 5
        if ((item_col2 and search_text in item_col2.text().lower()) or
            (item_col1 and search_text in item_col1.text().lower()) or
            (item_col3 and search_text in item_col3.text().lower()) or
            (item_col4 and search_text in item_col4.text().lower()) or
            (item_col6 and search_text in item_col6.text().lower())):
            self.reports_table.setRowHidden(row, False)
        else:
            self.reports_table.setRowHidden(row, True)
            
    self.Employee_calculate_totals()
    colorize(self.reports_table,"#cdd7b9","#dc8484")
    

def load_reports_all(self):
    selected_year = self.year_combo1.currentText()
    db_name = f"project_manager_V2"
    try:
        conn = mysql.connector.connect(host=host,user=user,password=password,database=db_name)
        cursor = conn.cursor()
        cursor.execute("SELECT id,  اسم_الموظف, الوظيفة, نوع_المعاملة, المبلغ, التاريخ, النسبة, الوصف FROM الموظفين_معاملات_مالية")
        reports = cursor.fetchall()
        self.reports_table.setRowCount(0)

        total_reports = len(reports)  # عدد السجلات
        
        for row_number, row_data in enumerate(reversed(reports)):
            self.reports_table.insertRow(row_number)

            # العمود الأول: الترقيم بالعكس
            serial_number = total_reports - row_number
            serial_item = QTableWidgetItem(str(serial_number))
            serial_item.setTextAlignment(Qt.AlignCenter)
            self.reports_table.setItem(row_number, 0, serial_item)


            for column_number, data in enumerate(row_data):
                item = QTableWidgetItem(str(data))
                item.setTextAlignment(Qt.AlignCenter)
                #self.reports_table.setItem(row_number, column_number, item)
                self.reports_table.setItem(row_number, column_number + 1, item)  # لاحظ +1 لأن العمود 0 للتسلسل
        conn.close()
        self.filter_reports()
        colorize(self.reports_table,"#cdd7b9","#dc8484")
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    table_setting(self.reports_table)
    

def Employee_calculate_totals(self):
    """حساب الإجماليات من الجداول"""
    self.Employee_Balance = 0
    self.Employee_Withdraw = 0
    # اجمالي الرصيد
    for row in range(self.reports_table.rowCount()):
        if self.reports_table.isRowHidden(row):
            continue
        Amount_item = self.reports_table.item(row, 5)  # العمود الذي يحتوي على المبلغ
        Transaction_type_item = self.reports_table.item(row, 4)  # العمود الذي يحتوي على نوع المعاملة
        # التأكد من أن العناصر ليست فارغة
        if Amount_item and Transaction_type_item:
            try:
                Amount = int(Amount_item.text())  # تحويل النص إلى عدد صحيح
                Transaction_type = Transaction_type_item.text().strip()  # الحصول على النص وإزالة الفراغات
                # إضافة المبالغ إذا كان نوع المعاملة "إضافة مرتب" أو "إضافة نسبة" أو "إضافة رصيد"
                if Transaction_type in ["إضافة مرتب", "إضافة نسبة", "إضافة رصيد"]:
                    self.Employee_Balance += Amount
                # طرح المبالغ إذا كان نوع المعاملة "خصم مبلغ"
                elif Transaction_type == "خصم مبلغ":
                    self.Employee_Balance -= Amount
            except ValueError:
                print(f"خطأ في تحويل المبلغ إلى عدد صحيح في الصف {row}.")
    
    # اجمالي السحب
    for row in range(self.reports_table.rowCount()):
        if self.reports_table.isRowHidden(row):
            continue
        Amount_item = self.reports_table.item(row, 5)  # العمود الذي يحتوي على المبلغ
        Transaction_type_item = self.reports_table.item(row, 4)  # العمود الذي يحتوي على نوع المعاملة
        # التأكد من أن العناصر ليست فارغة
        if Amount_item and Transaction_type_item:
            try:
                Amount = int(Amount_item.text())  # تحويل النص إلى عدد صحيح
                Transaction_type = Transaction_type_item.text().strip()  # الحصول على النص وإزالة الفراغات
                # إضافة المبالغ إذا كان نوع المعاملة "إضافة مرتب" أو "إضافة نسبة" أو "إضافة رصيد"
                if Transaction_type in ["سداد مبلغ"]:
                    self.Employee_Withdraw += Amount
                # طرح المبالغ إذا كان نوع المعاملة "خصم مبلغ"
                elif Transaction_type == "الغاء سحب":
                    self.Employee_Withdraw -= Amount
            except ValueError:
                print(f"خطأ في تحويل المبلغ إلى عدد صحيح في الصف {row}.")

    # تحديث المسمى التوضيحي للإجماليات في الواجهة
    self.Employee_Balance_label.setText(f"رصيد الموظفين: {self.Employee_Balance}")
    self.Employee_Withdraw_label.setText(f" سحب الموظفين: {self.Employee_Withdraw}")

#مهام الموظفين============================================================================
def open_maham(self):
    pass
    