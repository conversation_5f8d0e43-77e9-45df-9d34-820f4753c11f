#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار نافذة مراحل المشروع الجديدة
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
if getattr(sys, 'frozen', False):
    application_path = os.path.dirname(sys.executable)
else:
    application_path = os.path.dirname(os.path.abspath(__file__))

sys.path.insert(0, application_path)

from PySide6.QtWidgets import QApplication
from مراحل_المشروع import open_project_phases_window

def test_project_phases_window():
    """اختبار نافذة مراحل المشروع"""
    
    app = QApplication(sys.argv)
    
    # بيانات تجريبية للمشروع
    sample_project_data = {
        'id': 1,
        'معرف_العميل': 1,
        'معرف_المهندس': 1,
        'نوع_المشروع': 'تصميم',
        'اسم_المشروع': 'فيلا سكنية حديثة',
        'وصف_المشروع': 'تصميم فيلا سكنية من دورين بمساحة 400 متر مربع',
        'المبلغ': 150000,
        'المدفوع': 75000,
        'الباقي': 75000,
        'تاريخ_الإستلام': '2024-01-01',
        'تاريخ_التسليم': '2024-06-01',
        'الحالة': 'قيد الإنجاز',
        'ملاحظات': 'مشروع فيلا سكنية حديثة مع حديقة خارجية'
    }
    
    # فتح نافذة مراحل المشروع
    window = open_project_phases_window(None, sample_project_data, "تصميم")
    
    sys.exit(app.exec())

def test_contracting_project():
    """اختبار نافذة مراحل المشروع للمقاولات"""
    
    app = QApplication(sys.argv)
    
    # بيانات تجريبية لمشروع مقاولات
    sample_contracting_data = {
        'id': 2,
        'معرف_العميل': 2,
        'معرف_المهندس': 2,
        'نوع_المشروع': 'مقاولات',
        'اسم_المشروع': 'بناء مجمع تجاري',
        'وصف_المشروع': 'بناء مجمع تجاري من 3 طوابق بمساحة 1000 متر مربع',
        'المبلغ': 500000,
        'المدفوع': 200000,
        'الباقي': 300000,
        'تاريخ_الإستلام': '2024-02-01',
        'تاريخ_التسليم': '2024-12-01',
        'الحالة': 'قيد الإنجاز',
        'ملاحظات': 'مشروع مجمع تجاري مع مواقف سيارات'
    }
    
    # فتح نافذة مراحل المشروع للمقاولات
    window = open_project_phases_window(None, sample_contracting_data, "مقاولات")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    print("اختبار نافذة مراحل المشروع المحدثة")
    print("التحديثات الجديدة:")
    print("- تخطيط أفقي للحاويات الثلاث الأولى في تاب معلومات المشروع")
    print("- تاب جديد للملفات والمرفقات مع إمكانيات كاملة لإدارة الملفات")
    print("")
    print("1. اختبار مشروع تصميم")
    print("2. اختبار مشروع مقاولات")

    choice = input("اختر نوع الاختبار (1 أو 2): ")

    if choice == "1":
        test_project_phases_window()
    elif choice == "2":
        test_contracting_project()
    else:
        print("اختيار غير صحيح")
        test_project_phases_window()
